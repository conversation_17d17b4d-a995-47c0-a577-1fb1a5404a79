import type { PageLoad } from './$types';

export const load: PageLoad = async ({ fetch, }) => {
	try {
		const baseUrl = '/api/v1/jobs';
		const params = new URLSearchParams({
			homepage: 'true'
		});
		const url = baseUrl + '?' + params.toString();

		const jobs = await fetch(url, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json'
			}
		});

		if (!jobs.ok) {
			const res = await jobs.json();
			throw new Error(res.message);
		} else {
			const res = await jobs.json();

			const data = res?.data;

			if (!data) {
				throw new Error('No data found');
			}
			return {
				items: data?.jobs ?? [],
				count: data?.total ?? 0,
				pages: data?.pages ?? 0,
				page: data?.page ?? 0,
				limit: data?.limit ?? 0,
				message: 'success'
			};
		}
	} catch (error: unknown) {
		let errorMessage = 'Unknown error';
		if (error instanceof Error) {
			errorMessage = error.message;
		}
		return {
			items: [],
			count: 0,
			message: 'error',
			error: errorMessage
		};
	}
};
