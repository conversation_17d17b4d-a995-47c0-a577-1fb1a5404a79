<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import ServiceSplitScreen from '$lib/components/shared/ServiceSplitScreen.svelte';

	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';

	import { getContext } from 'svelte';
	import type { ExternalLinksConfig } from '../../app';

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;

	const h1 = `Downloads`;

	const p =
		'Download your application form and other required forms, complete and email them to us.';

	const downloadItems = [
		{
			id: 1,
			number: 1,
			title: 'Timesheet',
			downloadUrl: externalLinks.timesheetForm?.url
		},
		{
			id: 2,
			number: 2,
			title: 'Required Document',
			downloadUrl: externalLinks.requiredDocumentsInfo?.url
		},
		{
			id: 3,
			number: 3,
			title: 'Pre Health Questionnaire',
			downloadUrl: externalLinks.preHealthQuestionnaire?.url
		},
		{
			id: 4,
			number: 4,
			title: 'Applicable Form-Nurses/AHP',
			downloadUrl: externalLinks.applicationFormNursesAHP?.url
		},
		{
			id: 5,
			number: 5,
			title: 'Application Form-HealthCare Assissant',
			downloadUrl: externalLinks.applicationFormHCA?.url
		},
		{
			id: 6,
			number: 6,
			title: 'Application Form-Doctors/GPS',
			downloadUrl: externalLinks.applicationFormDoctorsGPs?.url
		}
	];
</script>

<PoorCarousel
	ctaLink="/contact#refer-a-friend"
	ctaText="Refer A Friend"
	baseClass="bg-[#FFFAFF]"
	{h1}
	{p}
/>

<div class="p-4 sm:p-6 lg:p-8">
	<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 lg:gap-8">
		{#each downloadItems as item (item.id)}
			<div class="flex flex-col items-center rounded-lg bg-neutral-100 p-6 text-center shadow-sm">
				<div class="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-[#E7D8FF]">
					<span class="text-3xl font-bold text-[#5D28B6]">{item.number}</span>
				</div>

				<h2 class="mb-6 flex-grow text-base font-medium text-neutral-700">
					{item.title}
				</h2>

				<a
					target="_blank"
					href={item.downloadUrl}
					download
					class="mt-auto inline-flex w-full items-center justify-center gap-2 rounded-md bg-[#5D28B6] px-6 py-2.5 text-sm font-semibold text-white shadow-sm transition-colors hover:bg-purple-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#5D28B6]"
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
						stroke-width="1.5"
						stroke="currentColor"
						class="h-5 w-5"
						aria-hidden="true"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"
						/>
					</svg>
					Download
				</a>
			</div>
		{/each}
	</div>
</div>

<div class="mt-5">
	<Subscribe />
</div>

<style>
	/* .special {
		z-index: -20;
		border: solid 1px#FFFAFF;
		border-radius: 20px;
	} */
</style>
