import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { Resend } from 'resend';
import { env } from '$env/dynamic/private';

if (!env.RESEND_API_KEY) {
	console.error('env.RESEND_API_KEY is not set. Email functionality will be disabled.');
}
const resend = env.RESEND_API_KEY ? new Resend(env.RESEND_API_KEY) : null;

if (!env.FROM_EMAIL || !env.TO_EMAIL) {
	console.error(
		'env.FROM_EMAIL or env.TO_EMAIL is not set for communication. Emails may not be sent correctly.'
	);
}

const verifyHCaptcha = async (token: string): Promise<boolean> => {
	const secret = env.HCAPTCHA_SECRET_KEY;
	if (!secret) {
		console.error('env.HCAPTCHA_SECRET_KEY is not set in environment variables.');
		return false;
	}
	try {
		const response = await fetch('https://hcaptcha.com/siteverify', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			},
			body: `response=${token}&secret=${secret}`
		});
		if (!response.ok) {
			console.error(`hCaptcha verification failed with status: ${response.status}`);
			const errorData = await response.json().catch(() => ({}));
			console.error('hCaptcha error details:', errorData);
			return false;
		}
		const data = await response.json();
		return data.success;
	} catch (error) {
		console.error('Error during hCaptcha verification:', error);
		return false;
	}
};

function generateEmailHtml(formType: string, data: Record<string, any>): string {
	let detailsHtml = '';
	for (const [key, value] of Object.entries(data)) {
		if (key !== 'passToken' && key !== 'formType' && !(value instanceof File)) {
			detailsHtml += `<p><strong>${key.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase())}:</strong> ${value || 'N/A'}</p>`;
		}
	}

	const title = formType.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());

	return `
        <div style="font-family: sans-serif; padding: 20px; border: 1px solid #eee; border-radius: 5px; max-width: 600px; margin: 20px auto; background-color: #f9f9f9;">
            <h2 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px;">New ${title} Submission</h2>
            ${detailsHtml}
            <hr style="border: none; border-top: 1px solid #ddd; margin: 20px 0;">
            <p style="font-size: 0.9em; color: #777;">This message was sent from the website contact form.</p>
        </div>
    `;
}

// --- Main POST Handler ---
export const POST: RequestHandler = async ({ request }) => {
	if (!resend) {
		return json({ message: 'Email service is not configured.' }, { status: 503 });
	}

	let requestData;
	let formType: string;
	let passToken: string;
	const attachments: any[] = [];
	let formDataValues: Record<string, any> = {};

	const contentType = request.headers.get('content-type');

	try {
		if (contentType?.includes('multipart/form-data')) {
			const formData = await request.formData();
			for (const [key, value] of formData.entries()) {
				if (value instanceof File) {
					attachments.push({
						filename: value.name,
						content: Buffer.from(await value.arrayBuffer()) // Convert File to Buffer for Resend
					});
					formDataValues[key] = `Attached: ${value.name}`; // Placeholder for email body
				} else {
					formDataValues[key] = value as string;
				}
			}
			formType = formDataValues.formType;
			passToken = formDataValues.passToken;
		} else if (contentType?.includes('application/json')) {
			requestData = await request.json();
			formType = requestData.formType;
			passToken = requestData.passToken;
			formDataValues = { ...requestData };
			delete formDataValues.passToken;
			delete formDataValues.formType;
		} else {
			return json({ message: 'Unsupported content type.' }, { status: 415 });
		}

		if (!formType) {
			return json({ message: 'Missing formType.' }, { status: 400 });
		}
		if (!passToken) {
			return json({ message: 'Missing hCaptcha token (passToken).' }, { status: 400 });
		}
	} catch (error: any) {
		console.error('Error parsing request:', error);
		return json({ message: 'Invalid request payload: ' + error.message }, { status: 400 });
	}

	// 1. Verify hCaptcha
	const isCaptchaVerified = await verifyHCaptcha(passToken);
	if (!isCaptchaVerified) {
		return json({ message: 'Invalid hCaptcha token.' }, { status: 403 }); // 403 Forbidden is more appropriate
	}

	// 2. Prepare Email Content
	let subject = `New Website Submission: ${formType.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase())}`;
	let senderName = 'N/A';
	let senderEmail = '<EMAIL>'; // Fallback

	// Customize subject and identify sender based on formType and common field names
	if (formType === 'jobApplication' || formType === 'candidateRegistration') {
		senderName =
			formDataValues.firstName && formDataValues.lastName
				? `${formDataValues.firstName} ${formDataValues.lastName}`
				: formDataValues.firstName || formDataValues.regFirstName || 'Applicant';
		senderEmail = formDataValues.email || formDataValues.regEmail || senderEmail;
		subject = `New ${formType === 'jobApplication' ? 'Job Application' : 'Candidate Registration'}: ${senderName} ${formDataValues.roleApplyingFor ? '- ' + formDataValues.roleApplyingFor : ''}`;
	} else if (formType === 'contactMessage') {
		// Assuming ContactForm.svelte sends this type
		senderName =
			formDataValues.foreName && formDataValues.surname
				? `${formDataValues.foreName} ${formDataValues.surname}`
				: formDataValues.foreName || 'Enquirer';
		senderEmail = formDataValues.emailAddress || senderEmail;
		subject = `Contact Form Message from ${senderName}`;
	} else if (formType === 'referFriend') {
		senderName =
			formDataValues.referrerFirstName && formDataValues.referrerLastName
				? `${formDataValues.referrerFirstName} ${formDataValues.referrerLastName}`
				: 'Referrer';
		senderEmail = formDataValues.referrerEmail || senderEmail;
		subject = `Friend Referral from ${senderName}`;
	} else if (formType === 'sendFeedback') {
		senderName =
			formDataValues.feedbackFirstName && formDataValues.feedbackLastName
				? `${formDataValues.feedbackFirstName} ${formDataValues.feedbackLastName}`
				: 'User';
		senderEmail = formDataValues.feedbackEmail || senderEmail;
		subject = `Feedback Received from ${senderName}: ${formDataValues.feedbackSubject || ''}`;
	} else if (formType === 'joinUs' || formType === 'requestCallBack') {
		const nameKey = formType === 'joinUs' ? 'join' : 'callback';
		senderName =
			formDataValues[`${nameKey}FirstName`] && formDataValues[`${nameKey}LastName`]
				? `${formDataValues[`${nameKey}FirstName`]} ${formDataValues[`${nameKey}LastName`]}`
				: 'Prospect';
		senderEmail = formDataValues[`${nameKey}Email`] || senderEmail;
		subject = `${formType === 'joinUs' ? 'Join Us Request' : 'Callback Request'} from ${senderName}`;
	}
	// Add more else if blocks for other formTypes

	const htmlBody = generateEmailHtml(formType, formDataValues);

	// 3. Send Email using Resend
	try {
		const { data, error } = await resend.emails.send({
			from: `Central Staffing Website v2 <${env.FROM_EMAIL}>`, // Use your verified Resend 'from' email
			to: [env.TO_EMAIL], // Single recipient
			subject: subject,
			html: htmlBody,
			attachments: attachments
		});

		if (error) {
			console.error('Resend API error:', error);
			return json(
				{ success: false, message: 'Failed to send message.', error: error.message },
				{ status: 500 }
			);
		}

		console.log('Message sent successfully to central email:', data?.id);
		return json(
			{ success: true, message: 'Message sent successfully!', messageId: data?.id },
			{ status: 200 }
		);
	} catch (err: any) {
		console.error('Error in communication endpoint:', err);
		return json(
			{ success: false, message: 'Server error while sending message.', error: err.message },
			{ status: 500 }
		);
	}
};
