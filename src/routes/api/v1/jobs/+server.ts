import { json } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/server/db';

connectToDatabase();
import JobServiceInstance, {
	type JobQueryFilters,
	type PaginationOptions,
	type SortOptions
} from '$lib/server/services/manager.service';

// Use the imported singleton instance
const jobService = JobServiceInstance;

/**
 * =============================================================================
 * API Usage Examples (GET /api/v1/jobs) -
 * =============================================================================
 *
 * Base URL: /api/v1/jobs
 *
 * --- Basic Usage ---
 *
 * 1. Get all jobs (Defaults: page 1, limit 10, sort by createdAt desc)
 * URL: /api/v1/jobs
 *
 * --- Filtering (Uses keys from JobQueryFilters) ---
 * Note: Service applies specific logic (e.g., case-insensitive regex for title/company/location)
 *
 * 2. Filter by Job Title (Partial, Case-Insensitive)
 * URL: /api/v1/jobs?title=Engineer
 *
 * 3. Filter by Exact Job Type
 * URL: /api/v1/jobs?jobType=Full-time
 *
 * 4. Filter by Location (Partial, Case-Insensitive)
 * URL: /api/v1/jobs?jobLocation=Lagos
 *
 * 5. Filter by Multiple Fields (AND condition)
 * URL: /api/v1/jobs?title=Manager&jobType=Full-time&jobLocation=Remote
 *
 * --- Pagination ---
 *
 * 6. Get the Second Page (Default Limit: 10)
 * URL: /api/v1/jobs?page=2
 *
 * 7. Get the First Page with Custom Limit
 * URL: /api/v1/jobs?limit=5
 *
 * 8. Combined Pagination
 * URL: /api/v1/jobs?page=2&limit=20
 *
 * --- Sorting ---
 *
 * 9. Sort by Company Name Ascending
 * URL: /api/v1/jobs?sortBy=companyName&sortOrder=asc
 *
 * 10. Sort by Creation Date Descending (explicitly, though default)
 * URL: /api/v1/jobs?sortBy=createdAt&sortOrder=desc
 *
 * --- Combined Usage ---
 *
 * 11. Filter by Field & Location, Paginate, Sort by Title Ascending
 * URL: /api/v1/jobs?jobField=Technology&jobLocation=Nigeria&page=3&limit=15&sortBy=title&sortOrder=asc
 *
 * =============================================================================
 * Implementation Notes:
 * - This endpoint extracts known filter keys (title, jobType, etc.), pagination
 * params (page, limit), and sorting params (sortBy, sortOrder) from the URL.
 * - It passes these structured options to jobService.getJobs().
 * - The JobService handles defaults and applies DB query logic.
 * - Advanced filtering (e.g., salary > 50000) using operators like [gt], [lt]
 * in the URL is NOT supported by the current JobService implementation.
 * Such parameters will be ignored.
 * =============================================================================
 */
export const GET: RequestHandler = async ({ url }) => {
	try {
		const searchParams = url.searchParams;

		// 1. Extract All Parameters
		const allParams: Record<string, string> = Object.fromEntries(searchParams);

		const page = searchParams.get('page') ?? '1';
		const limit = searchParams.get('limit') ?? '10';

		// validate they are int
		if (isNaN(parseInt(page)) || isNaN(parseInt(limit))) {
			return json(
				{
					status: 'Fail',
					message: 'Invalid page or limit parameters',
					data: null
				},
				{ status: 400 }
			);
		}

		// 2. Separate Parameters into Categories

		// Filtering Parameters (only keys defined in JobQueryFilters)
		const filters: JobQueryFilters = {};
		const knownFilterKeys: (keyof JobQueryFilters)[] = [
			'title',
			'companyName',
			'jobType',
			'jobField',
			'jobLocation',
			'homepage'
		];
		for (const key in allParams) {
			if (knownFilterKeys.includes(key as keyof JobQueryFilters)) {
				filters[key as keyof JobQueryFilters] = allParams[key] as any; // Let service handle specific types
			}
		}

		// Pagination Parameters
		const paginationOptions: PaginationOptions = {
			page: parseInt(page),
			limit: parseInt(limit)
		};

		// Service will apply defaults (1 for page, 10 for limit) if undefined

		// Sorting Parameters
		const sortOptions: SortOptions = {
			sortBy: searchParams.get('sortBy') ?? undefined,
			sortOrder: searchParams.get('sortOrder') as 'asc' | 'desc' | undefined
		};
		// Service will apply defaults (createdAt, desc) if undefined

		// 3. Call the Service with Correct Arguments
		const jobsData = await jobService.getJobs(filters, paginationOptions, sortOptions);

		// 4. Handle Response (check for out-of-bounds page)
		// Note: jobsData from service now directly contains page, pages, total, limit
		if (
			jobsData.jobs.length === 0 &&
			jobsData.page > 1 && // Current page returned by service
			jobsData.page > jobsData.pages // Current page is beyond total available pages
		) {
			return json(
				{
					status: 'Success',
					message: 'No jobs found for this page. Maximum page is ' + jobsData.pages + '.',
					data: { ...jobsData, jobs: [] }
				},
				{ status: 200 } // Still a success, just no data for that page
			);
		}

		// 5. Return Success Response
		return json(
			{
				status: 'Success',
				message:
					jobsData.jobs.length > 0
						? 'Jobs retrieved successfully.'
						: 'No jobs found matching your criteria.',
				data: jobsData
			},
			{ status: 200 }
		);
	} catch (error: any) {
		// Catch and handle errors
		console.error('GET /api/v1/jobs error:', error);
		return json(
			{
				status: 'Fail',
				message: 'An error occurred while retrieving jobs.',
				// Avoid sending detailed error messages to the client in production
				error: error.message // Or a generic error message
			},
			{ status: 500 }
		);
	}
};
