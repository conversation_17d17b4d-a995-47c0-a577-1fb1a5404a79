import { json } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/server/db';

connectToDatabase();
import JobServiceInstance from '$lib/server/services/manager.service';

// Use the imported singleton instance
const jobService = JobServiceInstance;

export const GET: RequestHandler = async ({ params }) => {
	try {
		const idToGet = params.id;

		if (!idToGet) {
			return json(
				{
					status: 'Fail',
					message: 'An error occurred while retrieving jobs.',
					error: 'Not found'
				},
				{ status: 404 }
			);
		}

		const jobData = await jobService.getJobById(idToGet);
		if (!jobData) {
			return json(
				{
					status: 'Fail',
					message: 'An error occurred while retrieving jobs.',
					error: 'Not found'
				},
				{ status: 404 }
			);
		}

		return json(
			{
				status: 'Success',
				message: 'Job retrieved successfully.',
				data: jobData
			},
			{ status: 200 }
		);
	} catch (error: any) {
		// Catch and handle errors
		console.error('GET /api/v1/[id] error:', error);
		return json(
			{
				status: 'Fail',
				message: 'An error occurred while retrieving jobs.',
				// Avoid sending detailed error messages to the client in production
				error: error.message // Or a generic error message
			},
			{ status: 500 }
		);
	}
};
