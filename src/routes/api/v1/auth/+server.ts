import type { Request<PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';

// import { UserService, SessionService } from '$lib/services'; // Assuming your services
import * as jose from 'jose'; // For JWT creation
import { connectToDatabase } from '$lib/server/db';
import { UserService } from '$lib/server/services';

connectToDatabase(); // Connect to the database

// Helper function to verify hCaptcha
const verifyHCaptcha = async (token: string): Promise<boolean> => {
	const secret = env.HCAPTCHA_SECRET_KEY;
	if (!secret) {
		console.error('env.HCAPTCHA_SECRET_KEY is not set in environment variables.');
		return false;
	}
	try {
		const response = await fetch('https://hcaptcha.com/siteverify', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			},
			body: `response=${token}&secret=${secret}`
		});
		if (!response.ok) {
			console.error(`hCaptcha verification failed with status: ${response.status}`);
			return false;
		}
		const data = await response.json();
		return data.success;
	} catch (error) {
		console.error('Error during hCaptcha verification:', error);
		return false;
	}
};

// Define the structure of your JWT payload
interface UserJWTPayload extends jose.JWTPayload {
	userId: string;
	email: string;
}

export const POST: RequestHandler = async ({ request, cookies }) => {
	let requestData;
	try {
		requestData = await request.json();
	} catch (error) {
		return json({ message: 'Invalid JSON payload' }, { status: 400 });
	}

	const { email, otp, passToken } = requestData;

	if (email && !otp) {
		if (!passToken) {
			return json({ message: 'Missing hCaptcha token (passToken)' }, { status: 400 });
		}

		const isCaptchaVerified = await verifyHCaptcha(passToken);
		if (!isCaptchaVerified) {
			return json({ message: 'Invalid hCaptcha token' }, { status: 400 });
		}

		try {
			const userService = new UserService();
			// Assuming requestOTP handles finding or creating user and sending OTP
			// It might return some user details or just a success status
			const otpRequestResult = await userService.requestOTP(email);

			if (!otpRequestResult) {
				// Adjust based on what requestOTP returns
				return json({ message: 'Failed to process OTP request for this email.' }, { status: 500 });
			}

			return json({ message: 'OTP has been sent to your email.' }, { status: 200 });
		} catch (error: any) {
			console.error('Error requesting OTP:', error);

			return json(
				{ message: error.message || 'Server error during OTP request.' },
				{ status: 500 }
			);
		}
	}

	if (email && otp) {
		try {
			const userService = new UserService();
			const verificationResult = await userService.verifyOTP(email, otp);

			if (!verificationResult || !verificationResult.isVerified || !verificationResult.user) {
				return json({ message: 'Invalid OTP or user not found.' }, { status: 400 });
			}

			const user = verificationResult.user;

			// Create JWT
			const secretKeyUint8Array = new TextEncoder().encode(env.JWT_SECRET_KEY);
			const alg = 'HS256';

			const jwtPayload: UserJWTPayload = {
				userId: user._id.toString(), // Ensure it's a string
				email: user.email
			};

			const token = await new jose.SignJWT(jwtPayload)
				.setProtectedHeader({ alg })
				.setIssuedAt()
				.setExpirationTime('24h') // Standard expiration time (e.g., 1 day, 7 days)
				.sign(secretKeyUint8Array);

			// Set JWT in an HTTP-only cookie
			cookies.set('session_token', token, {
				path: '/',
				httpOnly: true,
				secure: env.NODE_ENV === 'production',
				sameSite: 'lax',
				maxAge: 60 * 60 * 24
			});

			// Return user information (excluding sensitive data like password hashes)
			// The client might use this for initial UI update before a full page reload/navigation

			return json(
				{
					message: 'Authentication successful!',
					user: user // Send sanitized user data
				},
				{ status: 200 }
			);
		} catch (error: any) {
			console.error('Error verifying OTP or creating session:', error);
			return json(
				{ message: error.message || 'Server error during authentication.' },
				{ status: 500 }
			);
		}
	}

	// Fallback for invalid request structure
	return json(
		{ message: 'Invalid request parameters. Provide email or email and OTP.' },
		{ status: 400 }
	);
};

// Example of a logout endpoint (optional, but good to have)
export const DELETE: RequestHandler = async ({ cookies }) => {
	// Clear the session cookie
	cookies.delete('session_token', { path: '/' });
	return json({ message: 'Logged out successfully' }, { status: 200 });
};
