import { json, type RequestHandler } from '@sveltejs/kit';
import { connectToDatabase, ExternalLink } from '$lib/server/db';
import type { IExternalLink, ExternalLinkUpdateInput } from '$lib/server/db/models/externalLinks';
import mongoose from 'mongoose';

await connectToDatabase();

// GET: Retrieve a specific external link by name
export const GET: RequestHandler = async ({ params, locals }) => {
	if (!locals.user) {
		return json({ message: 'Unauthorized' }, { status: 401 });
	}

	const { name } = params;
	if (!name || typeof name !== 'string') {
		return json({ message: 'Link name parameter is required.' }, { status: 400 });
	}

	try {
		const link: IExternalLink | null = await ExternalLink.findOne({ name: name }).lean();
		if (!link) {
			return json({ message: `External link with name "${name}" not found.` }, { status: 404 });
		}
		return json(
			{
				status: 'Success',
				message: 'External link retrieved successfully.',
				data: link
			},
			{ status: 200 }
		);
	} catch (error: any) {
		console.error(`Error retrieving external link with name ${name}:`, error);
		return json(
			{ message: 'Server error while retrieving link.', error: error.message },
			{ status: 500 }
		);
	}
};

// PATCH: Update the URL and/or description of an existing external link by name
export const PATCH: RequestHandler = async ({ request, params, locals }) => {
	if (!locals.user) {
		return json({ message: 'Unauthorized' }, { status: 401 });
	}

	const { name } = params;
	if (!name || typeof name !== 'string') {
		return json({ message: 'Link name parameter is required.' }, { status: 400 });
	}

	try {
		const body = (await request.json()) as Partial<ExternalLinkUpdateInput>;
		const { url, description } = body;

		if (!url && description === undefined) {
			return json(
				{ message: 'Either URL or description must be provided for update.' },
				{ status: 400 }
			);
		}
		if (url && (typeof url !== 'string' || url.trim() === '')) {
			return json({ message: 'URL must be a non-empty string if provided.' }, { status: 400 });
		}
		if (description !== undefined && typeof description !== 'string') {
			return json({ message: 'Description must be a string if provided.' }, { status: 400 });
		}

		const updateData: Partial<ExternalLinkUpdateInput> = {};
		if (url) updateData.url = url.trim();
		if (description !== undefined) updateData.description = description.trim();

		const updatedLink: IExternalLink | null = await ExternalLink.findOneAndUpdate(
			{ name: name },
			{ $set: updateData },
			{ new: true, runValidators: true }
		).lean();

		if (!updatedLink) {
			return json(
				{ message: `External link with name "${name}" not found or update failed.` },
				{ status: 404 }
			);
		}

		return json(
			{
				status: 'Success',
				message: 'External link updated successfully.',
				data: updatedLink
			},
			{ status: 200 }
		);
	} catch (error: any) {
		console.error(`Error updating external link with name ${name}:`, error);
		if (error.name === 'ValidationError') {
			return json({ message: 'Validation failed.', errors: error.errors }, { status: 400 });
		}
		return json(
			{ message: 'Server error while updating link.', error: error.message },
			{ status: 500 }
		);
	}
};

// DELETE: Not exposed as per user request.
// If needed for admin cleanup later, you could add it here with strict checks.
