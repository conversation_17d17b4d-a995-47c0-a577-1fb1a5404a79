import { json, type RequestHandler } from '@sveltejs/kit';
import { connectToDatabase, ExternalLink } from '$lib/server/db';
import type { IExternalLink, ExternalLinkUpdateInput } from '$lib/server/db/models/externalLinks';

await connectToDatabase();

// GET: Retrieve all external links
export const GET: RequestHandler = async ({ locals }) => {
	if (!locals.user) {
		return json({ message: 'Unauthorized' }, { status: 401 });
	}

	try {
		const links: IExternalLink[] = await ExternalLink.find().sort({ name: 1 }).lean();
		return json(
			{
				status: 'Success',
				message: 'External links retrieved successfully.',
				data: links
			},
			{ status: 200 }
		);
	} catch (error: any) {
		console.error('Error retrieving external links:', error);
		return json(
			{ message: 'Server error while retrieving links.', error: error.message },
			{ status: 500 }
		);
	}
};

// POST: Create or Update a link if it's from the predefined list (by name)
// This allows the UI to "Create" by sending a known name and URL, or "Update" if it exists.
export const POST: RequestHandler = async ({ request, locals }) => {
	if (!locals.user) {
		return json({ message: 'Unauthorized' }, { status: 401 });
	}

	try {
		const body = (await request.json()) as { name: string; url: string; description?: string };
		const { name, url, description } = body;

		if (!name || !url) {
			return json({ message: 'Missing required fields: name and url.' }, { status: 400 });
		}
		if (typeof url !== 'string' || url.trim() === '') {
			return json({ message: 'URL must be a non-empty string.' }, { status: 400 });
		}
		if (description && typeof description !== 'string') {
			return json({ message: 'Description must be a string.' }, { status: 400 });
		}

		// Upsert based on name: Update if exists, Insert if not.
		// This ensures only predefined names (from seed) can be managed.
		// If a new name is sent that wasn't seeded, it would be created here IF that was desired.
		// Since we are managing a fixed set of link categories, the UI should only allow submitting these fixed names.
		const updatedLink = await ExternalLink.findOneAndUpdate(
			{ name: name.trim() },
			{ url: url.trim(), description: description?.trim() },
			{ new: true, upsert: true, runValidators: true, setDefaultsOnInsert: true }
		).lean();

		return json(
			{
				status: 'Success',
				message: `External link "${name}" processed successfully.`, // "processed" covers create/update
				data: updatedLink
			},
			{ status: 200 }
		); // 200 for upsert often preferred over 201 if it could be an update
	} catch (error: any) {
		console.error('Error processing external link:', error);
		if (error.name === 'ValidationError') {
			return json({ message: 'Validation failed.', errors: error.errors }, { status: 400 });
		}
		return json(
			{ message: 'Server error while processing link.', error: error.message },
			{ status: 500 }
		);
	}
};
