import { json } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/server/db';

connectToDatabase();
import JobServiceInstance from '$lib/server/services/manager.service';
import type { IJob, JobInput } from '$lib/server/db/models/job';

// Use the imported singleton instance
const jobService = JobServiceInstance;

// edit job
export const PATCH: RequestHandler = async ({ request, params, locals }) => {
	if (!locals.user) {
		return json(
			{
				status: 'Fail',
				message: 'Unauthorized',
				error: 'Unauthorized'
			},
			{ status: 401 }
		);
	}
	try {
		// Validate job
		const jobToEdit = params.id;

		if (!jobToEdit) {
			return json(
				{
					status: 'Fail',
					message: 'Missing Field(s)',
					error: 'Please provide a job id'
				},
				{ status: 400 }
			);
		}

		const job = await jobService.getJobById(jobToEdit);

		if (!job) {
			return json(
				{
					status: 'Fail',
					message: 'Job not found',
					error: 'Please provide a valid job id'
				},
				{ status: 404 }
			);
		}

		const jobData: Partial<JobInput> = await request.json();

		const editedJob = await jobService.updateJob(jobToEdit, jobData);

		if (!editedJob) {
			return json(
				{
					status: 'Fail',
					message: 'Update failed',
					error: 'The Update Failed to process'
				},
				{ status: 404 }
			);
		}

		return json({
			status: 'Success',
			message: 'Job updated successfully',
			data: editedJob
		});
	} catch (error: any) {
		console.error('GET /api/v1/manager/jobs error:', error);
		if (error?.name === 'ValidationError') {
			// This catches Mongoose validation errors thrown by jobService.updateJob
			return json(
				{
					status: 'Fail',
					message: 'Update failed due to validation errors.',
					errors: error?.errors // Contains detailed field-specific errors
				},
				{ status: 400 } // 400 Bad Request for validation failures
			);
		}
		return json(
			{
				status: 'Fail',
				message: 'An error occurred while creating jobs.',
				// Avoid sending detailed error messages to the client in production
				error: error?.message // Or a generic error message
			},
			{ status: 500 }
		);
	}
};

export const DELETE: RequestHandler = async ({ params }) => {
	try {
		const jobId = params.id;
		if (!jobId) {
			return json(
				{
					status: 'Fail',
					message: 'Missing Field(s)',
					error: 'Please provide a job id'
				},
				{ status: 400 }
			);
		}

		const deletedJob = await jobService.deleteJob(jobId);
		if (!deletedJob) {
			return json(
				{
					status: 'Fail',
					message: 'Error occurred',
					error: 'Error occurred'
				},
				{ status: 500 }
			);
		}
		return json(
			{
				status: 'Success',
				message: 'Job deleted successfully',
				data: null
			},
			{ status: 200 }
		);
	} catch (error: any) {
		console.error('GET /api/v1/manager/[id] error:', error);
		return json(
			{
				status: 'Fail',
				message: 'An error occurred while deleting jobs',
				error: error.message
			},
			{ status: 500 }
		);
	}
};
