import { json } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/server/db';

connectToDatabase();
import JobServiceInstance from '$lib/server/services/manager.service';
import type { IJob, JobInput } from '$lib/server/db/models/job';

// Use the imported singleton instance
const jobService = JobServiceInstance;

// create job
export const POST: RequestHandler = async ({ request, locals }) => {
	if (!locals.user) {
		return json(
			{
				status: 'Fail',
				message: 'Unauthorized',
				error: 'Unauthorized'
			},
			{ status: 401 }
		);
	}
	try {
		const jobData: Partial<JobInput> = await request.json();
		// verify required fields
		if (
			!jobData.title ||
			!jobData.companyName ||
			!jobData.jobType ||
			!jobData.jobDesc ||
			!jobData.jobField ||
			!jobData.jobLocation ||
			!jobData.skills ||
			!jobData.salary ||
			!jobData.deadline
		) {
			return json(
				{
					status: 'Fail',
					message: 'Missing Field(s)',
					error:
						'Please provide all required fields, they are title, companyName, jobType, jobDesc, jobField, jobLocation, skills, salary, and deadline'
				},
				{ status: 400 }
			);
		}
		const createdJob = await jobService.createJob(jobData as IJob);
		if (!createdJob) {
			return json(
				{
					status: 'Fail',
					message: 'Something went wrong trying to create the job',
					error: 'Something went wrong trying to create the job'
				},
				{ status: 500 }
			);
		}
		return json(
			{
				status: 'Success',
				message: 'Job created successfully',
				data: createdJob
			},
			{ status: 201 }
		);
	} catch (error: any) {
		// Catch and handle errors
		console.error('GET /api/v1/manager/jobs error:', error);
		return json(
			{
				status: 'Fail',
				message: 'An error occurred while creating jobs.',
				// Avoid sending detailed error messages to the client in production
				error: error.message // Or a generic error message
			},
			{ status: 500 }
		);
	}
};
