<script lang="ts">
	// export let open: boolean = false;
	// export let onClose: () => void = () => {}; // For the "Thank You" button

	let {
		text = 'Your application has been submitted successfully. Good luck!',
		open,
		onClose
	} = $props();

	import { portalAction } from '@sveltelegos-blue/svelte-legos';
	// clickOutsideAction might not be desired here if you want explicit "Thank You" click
</script>

{#if open}
	<div use:portalAction={'body'}>
		<div class="modal-backdrop" role="presentation">
			<div class="success-modal-content" role="dialog" aria-labelledby="success-modal-title">
				<div class="success-icon-wrapper">
					<svg
						width="60"
						height="60"
						viewBox="0 0 24 24"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<circle cx="12" cy="12" r="10" fill="#6200EE" />
						<path
							d="M8 12.5L10.5 15L16 9.5"
							stroke="white"
							stroke-width="2"
							stroke-linecap="round"
							stroke-linejoin="round"
						/>
					</svg>
				</div>
				<h2 id="success-modal-title" class="modal-title">
					{text}
				</h2>
				<button class="thank-you-button" onclick={onClose}> Thank You </button>
				<div class="deco deco-triangle">△</div>
				<div class="deco deco-square">□</div>
				<div class="deco deco-circle-1"></div>
				<div class="deco deco-circle-2"></div>
			</div>
		</div>
	</div>
{/if}

<style>
	.modal-backdrop {
		/* ... (same as above or global) ... */
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.6);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
		padding: 20px;
		box-sizing: border-box;
	}
	.success-modal-content {
		background-color: white;
		padding: 40px 50px;
		border-radius: 16px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
		width: 100%;
		max-width: 480px; /* Adjusted for success message */
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative; /* For decorative elements */
		font-family:
			system-ui,
			-apple-system,
			BlinkMacSystemFont,
			'Segoe UI',
			Roboto,
			Oxygen,
			Ubuntu,
			Cantarell,
			'Open Sans',
			'Helvetica Neue',
			sans-serif;
	}
	.success-icon-wrapper {
		margin-bottom: 25px;
		/* Style for the outer circle if needed */
		background-color: #f0e6ff; /* Light purple bg for outer dashed circle */
		border-radius: 50%;
		width: 80px; /* Size of outer circle */
		height: 80px;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative; /* For dashed border effect if done with pseudo-elements */
	}
	/* The SVG itself has the purple circle, if you need a dashed border like in image: */
	.success-icon-wrapper::before {
		content: '';
		position: absolute;
		top: -6px;
		right: -6px;
		bottom: -6px;
		left: -6px;
		border: 2px dashed #ccaaff; /* Dashed light purple border */
		border-radius: 50%;
	}

	.modal-title {
		font-size: 1.1rem;
		font-weight: 500;
		color: #444;
		margin-bottom: 30px;
		line-height: 1.6;
	}
	.thank-you-button {
		background-color: #6200ee;
		color: white;
		border: none;
		padding: 12px 40px; /* Wider button */
		font-size: 1rem;
		font-weight: 500;
		border-radius: 25px;
		cursor: pointer;
		transition: background-color 0.2s ease;
	}
	.thank-you-button:hover {
		background-color: #5300d0;
	}

	/* Decorative elements */
	.deco {
		position: absolute;
		color: #d1c4e9; /* Light purple for decorations */
		font-size: 1.5rem;
		opacity: 0.6;
		user-select: none;
	}
	.deco-triangle {
		top: 25%;
		left: 15%;
		transform: rotate(-15deg);
	}
	.deco-square {
		top: 20%;
		right: 12%;
		font-size: 2.5rem;
		transform: rotate(15deg);
		color: #e8eaf6;
	} /* Lighter square */
	.deco-circle-1 {
		width: 15px;
		height: 15px;
		border-radius: 50%;
		background-color: #f3e5f5; /* Very light circle */
		bottom: 15%;
		left: 20%;
	}
	.deco-circle-2 {
		width: 20px;
		height: 20px;
		border-radius: 50%;
		border: 2px solid #d1c4e9;
		bottom: 20%;
		right: 15%;
	}
</style>
