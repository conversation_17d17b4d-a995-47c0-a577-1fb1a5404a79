<script lang="ts">
	// Props for the section title and subtitle
	let { sectionLabel = 'JOBS', sectionTitle = 'Available Roles' } = $props();

	// Data for the job cards
	// Using placeholder image URLs and content as seen in the image

	const jobsOfTheDay = [
		{
			id: 1,
			imageSrc: '/ongoing/02.png',
			imageAlt: 'Medical professional working',
			title: 'Medical',
			description:
				'Join dedicated healthcare teams, providing vital patient care and making a difference in diverse medical settings. Explore rewarding roles now.'
		},
		{
			id: 2,
			imageSrc: '/ongoing/03.png',
			imageAlt: 'Care giver with a patient',
			title: 'Care Givers',
			description:
				"Offer compassionate support and make a real impact on individuals' lives. Discover fulfilling opportunities to provide essential care and assistance."
		},

		{
			id: 43,
			imageSrc: '/services/3.avif',
			imageAlt: 'Person working at a desk with documents',
			title: 'Social Workers',
			description: `Be part of a dedicated team delivering essential healthcare services. From clinical
						assistance to community care, our healthcare roles offer competitive pay, training, and
						career advancement.`
		},
		{
			id: 53,
			imageSrc: '/services/1.avif',
			imageAlt: 'Person presenting charts in a meeting',
			title: 'Nurses',
			description:
				'Fuel business growth by developing engaging marketing strategies and driving sales success. Connect with dynamic commercial opportunities today.'
		},
		// {
		// 	id: 3,
		// 	imageSrc: '/ongoing/07.png',
		// 	imageAlt: 'Worker in a warehouse',
		// 	title: 'Industrial/ Warehouse',
		// 	description:
		// 		'Be essential to efficient operations in fast-paced industrial and warehouse environments. Explore hands-on roles in logistics, production, and more.'
		// },
		{
			id: 4,
			imageSrc: '/ongoing/04.png',
			imageAlt: 'Person working on a computer in an office',
			title: 'Support Workers',
			description:
				'Drive innovation and manage impactful projects within dynamic tech environments. Your expertise can shape the future in these exciting roles.'
		}
	];
	// Placeholder for the decorative star/asterisk SVG.
	// You would replace this with your actual SVG code.
	const decorativeStarSvg = `
       <svg width="75" height="73" viewBox="0 0 75 73" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M53.7242 47.4282C53.7242 46.6501 53.1022 46.0194 52.3348 46.0194C51.5674 46.0194 50.9453 46.6501 50.9453 47.4282C50.9453 48.2062 51.5674 48.8369 52.3348 48.8369C53.1022 48.8369 53.7242 48.2062 53.7242 47.4282Z" fill="#00B5E9"/>
<path d="M57.4299 50.7154C57.4299 49.678 56.6004 48.837 55.5772 48.837C54.5541 48.837 53.7246 49.678 53.7246 50.7154C53.7246 51.7528 54.5541 52.5938 55.5772 52.5938C56.6004 52.5938 57.4299 51.7528 57.4299 50.7154Z" fill="#00B5E9"/>
<path d="M62.0612 54.9416C62.0612 53.6449 61.0244 52.5937 59.7455 52.5937C58.4665 52.5937 57.4297 53.6449 57.4297 54.9416C57.4297 56.2383 58.4665 57.2896 59.7455 57.2896C61.0244 57.2896 62.0612 56.2383 62.0612 54.9416Z" fill="#00B5E9"/>
<path d="M67.6204 60.107C67.6204 58.551 66.3762 57.2895 64.8414 57.2895C63.3067 57.2895 62.0625 58.551 62.0625 60.107C62.0625 61.6631 63.3067 62.9246 64.8414 62.9246C66.3762 62.9246 67.6204 61.6631 67.6204 60.107Z" fill="#00B5E9"/>
<path d="M52.7946 25.5716C52.7946 26.3496 52.1725 26.9804 51.4051 26.9804C50.6377 26.9804 50.0156 26.3496 50.0156 25.5716C50.0156 24.7936 50.6377 24.1628 51.4051 24.1628C52.1725 24.1628 52.7946 24.7936 52.7946 25.5716Z" fill="#00B5E9"/>
<path d="M56.4982 22.2844C56.4982 23.3217 55.6688 24.1627 54.6456 24.1627C53.6224 24.1627 52.793 23.3217 52.793 22.2844C52.793 21.247 53.6224 20.406 54.6456 20.406C55.6688 20.406 56.4982 21.247 56.4982 22.2844Z" fill="#00B5E9"/>
<path d="M61.1316 18.0581C61.1316 19.3549 60.0947 20.4061 58.8158 20.4061C57.5368 20.4061 56.5 19.3549 56.5 18.0581C56.5 16.7614 57.5368 15.7102 58.8158 15.7102C60.0947 15.7102 61.1316 16.7614 61.1316 18.0581Z" fill="#00B5E9"/>
<path d="M66.6887 12.8927C66.6887 14.4488 65.4446 15.7102 63.9098 15.7102C62.375 15.7102 61.1309 14.4488 61.1309 12.8927C61.1309 11.3366 62.375 10.0752 63.9098 10.0752C65.4446 10.0752 66.6887 11.3366 66.6887 12.8927Z" fill="#00B5E9"/>
<path d="M24.0804 47.4282C24.0804 46.6501 24.7025 46.0194 25.4699 46.0194C26.2373 46.0194 26.8594 46.6501 26.8594 47.4282C26.8594 48.2062 26.2373 48.8369 25.4699 48.8369C24.7025 48.8369 24.0804 48.2062 24.0804 47.4282Z" fill="#00B5E9"/>
<path d="M20.3748 50.7152C20.3748 49.6778 21.2043 48.8368 22.2275 48.8368C23.2506 48.8368 24.0801 49.6778 24.0801 50.7152C24.0801 51.7525 23.2506 52.5935 22.2275 52.5935C21.2043 52.5935 20.3748 51.7525 20.3748 50.7152Z" fill="#00B5E9"/>
<path d="M15.7434 54.9416C15.7434 53.6449 16.7803 52.5937 18.0592 52.5937C19.3382 52.5937 20.375 53.6449 20.375 54.9416C20.375 56.2383 19.3382 57.2896 18.0592 57.2896C16.7803 57.2896 15.7434 56.2383 15.7434 54.9416Z" fill="#00B5E9"/>
<path d="M10.1863 60.107C10.1863 58.551 11.4304 57.2895 12.9652 57.2895C14.5 57.2895 15.7441 58.551 15.7441 60.107C15.7441 61.6631 14.5 62.9246 12.9652 62.9246C11.4304 62.9246 10.1863 61.6631 10.1863 60.107Z" fill="#00B5E9"/>
<path d="M23.1547 25.5716C23.1547 26.3496 23.7767 26.9804 24.5441 26.9804C25.3115 26.9804 25.9336 26.3496 25.9336 25.5716C25.9336 24.7936 25.3115 24.1628 24.5441 24.1628C23.7767 24.1628 23.1547 24.7936 23.1547 25.5716Z" fill="#00B5E9"/>
<path d="M19.4491 22.2844C19.4491 23.3217 20.2785 24.1627 21.3017 24.1627C22.3249 24.1627 23.1543 23.3217 23.1543 22.2844C23.1543 21.247 22.3249 20.406 21.3017 20.406C20.2785 20.406 19.4491 21.247 19.4491 22.2844Z" fill="#00B5E9"/>
<path d="M14.8177 18.0581C14.8177 19.3549 15.8545 20.4061 17.1334 20.4061C18.4124 20.4061 19.4492 19.3549 19.4492 18.0581C19.4492 16.7614 18.4124 15.7102 17.1334 15.7102C15.8545 15.7102 14.8177 16.7614 14.8177 18.0581Z" fill="#00B5E9"/>
<path d="M9.25855 12.8927C9.25855 14.4488 10.5027 15.7102 12.0375 15.7102C13.5722 15.7102 14.8164 14.4488 14.8164 12.8927C14.8164 11.3366 13.5722 10.0752 12.0375 10.0752C10.5027 10.0752 9.25855 11.3366 9.25855 12.8927Z" fill="#00B5E9"/>
<path d="M53.7481 37.3683C53.2055 37.9185 52.3257 37.9185 51.7831 37.3683C51.2405 36.8182 51.2405 35.9262 51.7831 35.376C52.3257 34.8259 53.2055 34.8259 53.7481 35.376C54.2907 35.9262 54.2907 36.8182 53.7481 37.3683Z" fill="#00B5E9"/>
<path d="M58.6616 37.7004C57.9381 38.4339 56.7651 38.4339 56.0416 37.7004C55.3181 36.9669 55.3181 35.7776 56.0416 35.044C56.7651 34.3105 57.9381 34.3105 58.6616 35.044C59.3851 35.7776 59.3851 36.9669 58.6616 37.7004Z" fill="#00B5E9"/>
<path d="M64.8836 38.0325C63.9792 38.9494 62.513 38.9494 61.6086 38.0325C60.7042 37.1156 60.7042 35.6289 61.6086 34.712C62.513 33.7951 63.9792 33.7951 64.8836 34.712C65.788 35.6289 65.788 37.1156 64.8836 38.0325Z" fill="#00B5E9"/>
<path d="M72.4181 38.3646C71.3329 39.4649 69.5734 39.4649 68.4881 38.3646C67.4029 37.2643 67.4029 35.4803 68.4881 34.38C69.5734 33.2797 71.3329 33.2797 72.4181 34.38C73.5034 35.4803 73.5034 37.2643 72.4181 38.3646Z" fill="#00B5E9"/>
<path d="M20.6366 37.3686C21.1793 37.9187 22.059 37.9187 22.6016 37.3686C23.1443 36.8184 23.1443 35.9264 22.6016 35.3763C22.059 34.8261 21.1793 34.8261 20.6366 35.3763C20.094 35.9264 20.094 36.8184 20.6366 37.3686Z" fill="#00B5E9"/>
<path d="M15.7232 37.7006C16.4467 38.4342 17.6197 38.4342 18.3432 37.7006C19.0667 36.9671 19.0667 35.7778 18.3432 35.0443C17.6197 34.3107 16.4467 34.3107 15.7232 35.0443C14.9997 35.7778 14.9997 36.9671 15.7232 37.7006Z" fill="#00B5E9"/>
<path d="M9.49922 38.0327C10.4036 38.9497 11.8699 38.9497 12.7742 38.0327C13.6786 37.1158 13.6786 35.6292 12.7742 34.7122C11.8699 33.7953 10.4036 33.7953 9.49922 34.7122C8.59485 35.6292 8.59485 37.1158 9.49922 38.0327Z" fill="#00B5E9"/>
<path d="M1.96859 38.3646C3.05383 39.4649 4.81335 39.4649 5.89859 38.3646C6.98383 37.2643 6.98383 35.4803 5.89859 34.38C4.81335 33.2797 3.05383 33.2797 1.96859 34.38C0.883353 35.4803 0.883354 37.2643 1.96859 38.3646Z" fill="#00B5E9"/>
<path d="M39.633 52.0809C40.1757 51.5307 40.1757 50.6387 39.633 50.0886C39.0904 49.5384 38.2107 49.5384 37.668 50.0886C37.1254 50.6387 37.1254 51.5307 37.668 52.0809C38.2107 52.631 39.0904 52.631 39.633 52.0809Z" fill="#00B5E9"/>
<path d="M39.9593 57.0616C40.6828 56.3281 40.6828 55.1387 39.9593 54.4052C39.2358 53.6717 38.0628 53.6717 37.3393 54.4052C36.6158 55.1387 36.6158 56.3281 37.3393 57.0616C38.0628 57.7951 39.2358 57.7951 39.9593 57.0616Z" fill="#00B5E9"/>
<path d="M40.2875 63.3704C41.1919 62.4535 41.1919 60.9669 40.2875 60.05C39.3831 59.133 37.9169 59.133 37.0125 60.05C36.1081 60.9669 36.1081 62.4535 37.0125 63.3704C37.9169 64.2874 39.3831 64.2874 40.2875 63.3704Z" fill="#00B5E9"/>
<path d="M40.6138 71.0077C41.699 69.9074 41.699 68.1234 40.6138 67.0231C39.5285 65.9228 37.769 65.9228 36.6838 67.0231C35.5985 68.1234 35.5985 69.9074 36.6838 71.0077C37.769 72.108 39.5285 72.108 40.6138 71.0077Z" fill="#00B5E9"/>
<path d="M39.633 20.9191C40.1757 21.4693 40.1757 22.3613 39.633 22.9114C39.0904 23.4616 38.2107 23.4616 37.668 22.9114C37.1254 22.3613 37.1254 21.4693 37.668 20.9191C38.2107 20.369 39.0904 20.369 39.633 20.9191Z" fill="#00B5E9"/>
<path d="M39.9593 15.9384C40.6828 16.6719 40.6828 17.8613 39.9593 18.5948C39.2358 19.3283 38.0628 19.3283 37.3393 18.5948C36.6158 17.8613 36.6158 16.6719 37.3393 15.9384C38.0628 15.2049 39.2358 15.2049 39.9593 15.9384Z" fill="#00B5E9"/>
<path d="M40.2875 9.62931C41.1919 10.5462 41.1919 12.0329 40.2875 12.9498C39.3831 13.8667 37.9169 13.8667 37.0125 12.9498C36.1081 12.0329 36.1081 10.5462 37.0125 9.62931C37.9169 8.71238 39.3831 8.71238 40.2875 9.62931Z" fill="#00B5E9"/>
<path d="M40.6138 1.99233C41.699 3.09264 41.699 4.8766 40.6138 5.97691C39.5285 7.07722 37.769 7.07722 36.6838 5.97691C35.5985 4.8766 35.5985 3.09264 36.6838 1.99233C37.769 0.892014 39.5285 0.892015 40.6138 1.99233Z" fill="#00B5E9"/>
</svg>

    `;
</script>

<section class="relative bg-gray-50 py-16 md:py-24">
	<div class="absolute top-8 right-8 hidden opacity-70 md:block">
		{@html decorativeStarSvg}
	</div>
	<div class="absolute top-1/2 left-8 hidden -translate-y-1/2 transform opacity-70 md:block">
		{@html decorativeStarSvg}
	</div>

	<div class="container mx-auto px-4 sm:px-6 lg:px-8">
		<div class="mb-12 text-center md:mb-16">
			<p class="mb-2 text-sm font-semibold tracking-wider text-[#FF6F00] uppercase">
				{sectionLabel}
			</p>
			<h2
				class="text-3xl leading-tight font-bold text-[#0586AB] capitalize md:text-4xl lg:text-[40px]"
			>
				{sectionTitle}
			</h2>
			<!-- <p class="mx-auto mt-4 max-w-xl text-base text-[#737373]">
				{sectionSubtitle}
			</p> -->
		</div>

		<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:gap-8 lg:grid-cols-3">
			{#each jobsOfTheDay as job (job.id)}
				<div
					href={`/ongoing-jobs/${job.id}`}
					class="flex min-h-[460px] cursor-pointer flex-col overflow-hidden rounded-[10px] bg-[#F5F5F5] shadow-sm"
				>
					<div class="h-72 w-full bg-[#F5F5F5]">
						<img src={job.imageSrc} alt={job.imageAlt} class="h-full w-full object-cover" />
					</div>
					<div class="flex flex-grow flex-col p-6">
						<h3 class="mb-2 text-xl font-semibold text-[#161A1E]">{job.title}</h3>
						<p class="flex-grow text-sm text-[#737373]">
							{job.description}
						</p>
						<!-- <p class="mt-3 text-xs">Learn More</p> -->
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<style>
	/* You can add component-specific styles here if needed */
	/* For example, to ensure consistent card heights if content varies significantly,
       though flex-grow on description and flex-col on card helps. */
</style>
