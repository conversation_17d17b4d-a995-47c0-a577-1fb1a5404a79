<script lang="ts">
	import type { PageProps } from './$types';

	let { data }: PageProps = $props();

	import AvailableJobsSection from './AvailableJobsSection.svelte'; // Adjust path
	// import type { Job } from './types'; // Assuming you have a Job type definition

	let totalJobs = $derived(data.total);
	let currentPage = $derived(data.page);
	let jobsPerPage = $derived(data.limit ?? 6);

	let displayedJobs = $derived(data.jobs ?? []);

	// async function fetchJobs(page: number) {
	// 	// Simulate API call
	// 	// const response = await fetch(`/aspi/jobs?page=${page}&limit=${jobsPerPage}`);
	// 	// const data = await response.json();
	// 	// allJobs = data.items; // This would actually be items for the current page
	// 	// totalJobs = data.totalItems;
	// 	// displayedJobs = allJobs; // Update the jobs to display

	// 	// For this example, let's simulate with a larger static list
	// 	const fullJobList = Array.from({ length: 25 }, (_, i) => ({
	// 		id: i + 1,
	// 		title: `Experienced Medical Practitioner ${i + 1}`,
	// 		description: `Description for job ${i + 1}...`,
	// 		location: 'London, Manchester (Remote)',
	// 		salary: `£ ${50 + i}k`,
	// 		isActive: i === 0 && page === 1 // Highlight first job on first page
	// 	}));
	// 	totalJobs = fullJobList.length;
	// 	displayedJobs = fullJobList.slice((page - 1) * jobsPerPage, page * jobsPerPage);
	// 	currentPage = page;
	// }

	// $effect(() => {
	// 	fetchJobs(currentPage);
	// });

	// function onPageChange(page: number) {
	// 	fetchJobs(page);
	// }
</script>

<AvailableJobsSection
	topLabel={data.findJobCategory?.title}
	mainTitle="Available Jobs"
	bind:jobs={displayedJobs}
	jobSlug={data.findJobCategory?.dbSlug}
	bind:totalItems={totalJobs}
	bind:itemsPerPage={jobsPerPage}
	bind:currentPage
/>
