import { error, json } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import JobServiceInstance, {
	type JobQueryFilters,
	type PaginationOptions,
	type SortOptions
} from '$lib/server/services/manager.service';
import { connectToDatabase } from '$lib/server/db';

connectToDatabase();

const jobService = JobServiceInstance;

export const load: PageServerLoad = async ({ params, url }) => {
	const jobsOfTheDay = [
		{ id: '1', title: 'Medical', dbSlug: 'medical' },
		{ id: '2', title: 'Care Givers', dbSlug: 'care' },
		{ id: '3', title: 'Industrial / Warehouse', dbSlug: 'industrial' },
		{ id: '4', title: 'Others', dbSlug: 'unspecified' }
	];

	const findJobCategory = jobsOfTheDay.find((category) => category.id == params.category);

	const allParams: Record<string, string> = Object.fromEntries(url.searchParams);
	allParams.jobField = findJobCategory?.dbSlug ?? 'unspecified';

	const page = url.searchParams.get('page') ?? '1';
	const limit = url.searchParams.get('limit') ?? '6';

	if (!findJobCategory) {
		return error(404, 'Not found');
	}

	try {
		// Filtering Parameters (only keys defined in JobQueryFilters)
		const filters: JobQueryFilters = {};
		const knownFilterKeys: (keyof JobQueryFilters)[] = [
			'title',
			'companyName',
			'jobType',
			'jobField',
			'jobLocation',
			'homepage'
		];
		for (const key in allParams) {
			if (knownFilterKeys.includes(key as keyof JobQueryFilters)) {
				filters[key as keyof JobQueryFilters] = allParams[key] as any; // Let service handle specific types
			}
		}

		// Pagination Parameters
		const paginationOptions: PaginationOptions = {
			page: parseInt(page),
			limit: parseInt(limit)
		};

		// Service will apply defaults (1 for page, 10 for limit) if undefined

		// Sorting Parameters
		const sortOptions: SortOptions = {
			sortBy: url.searchParams.get('sortBy') ?? undefined,
			sortOrder: url.searchParams.get('sortOrder') as 'asc' | 'desc' | undefined
		};
		// Service will apply defaults (createdAt, desc) if undefined

		// 3. Call the Service with Correct Arguments
		const jobsData = await jobService.getJobs(filters, paginationOptions, sortOptions);

		// 4. Handle Response (check for out-of-bounds page)
		// Note: jobsData from service now directly contains page, pages, total, limit
		if (
			jobsData.jobs.length === 0 &&
			jobsData.page > 1 && // Current page returned by service
			jobsData.page > jobsData.pages // Current page is beyond total available pages
		) {
			return {
				status: 'Success',
				message: 'No jobs found for this page. Maximum page is ' + jobsData.pages + '.',
				data: { ...jobsData, jobs: [] }
			};
		}

		const formattedJobs = jobsData.jobs.map((job) => {
			return {
				id: job._id.toString(),
				title: job.title,
				companyName: job.companyName,
				companyDesc: job.companyDesc,
				jobType: job.jobType,
				jobDesc: job.jobDesc,
				jobField: job.jobField,
				jobLocation: job.jobLocation,
				skills: job.skills,
				salary: job.salary,
				deadline: job.deadline,
				homepage: job.homepage,
				createdAt: job.createdAt,
				updatedAt: job.updatedAt,
				isActive: false
			};
		});

		return {
			jobs: formattedJobs,
			total: jobsData.total,
			page: jobsData.page,
			limit: jobsData.limit,
			pages: jobsData.pages,
			findJobCategory
		};
	} catch (e) {
		error(500, 'Internal Server Error');
	}
};
