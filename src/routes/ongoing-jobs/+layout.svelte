<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';

	let { children } = $props();

	const h1 = `Your Trusted Partner in <br /> Recruitment–Medical, <br /> Commercial And Industrial, & <br />Staffing`;

	const p = 'Delivering Skilled Talent for Temporary, Contract, and Permanent Roles Across the UK.';
</script>

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />
{@render children?.()}
<div class="mt-5">
	<Subscribe />
</div>

{#snippet Image()}
	<div class="aspect-square w-full max-w-xl">
		<img
			src="/ongoing/01.png"
			alt="man in a black suit"
			class="h-full w-full object-cover object-bottom-right"
		/>
	</div>
{/snippet}
