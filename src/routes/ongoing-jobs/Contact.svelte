<script lang="ts">
	import type { Snippet } from 'svelte';
	import { getContext } from 'svelte';
	import type { ExternalLinksConfig } from '../../app';

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;

	let {
		pageLabel = 'JOBS',
		pageTitle = 'Ready to Take the Next Step in Your Career?<br /> Fill the Form Below to Get Started',
		pageSubtitle = '',

		formSubmitHandler = (event: SubmitEvent) => {
			event.preventDefault();
			const formData = new FormData(event.target as HTMLFormElement);
			const data = Object.fromEntries(formData.entries());
			console.log('Form data submitted from page:', data);
			// Add your actual form submission logic here (e.g., API call)
			alert('Form submitted! Check console for data.');
		}
	} = $props<{
		pageLabel?: string;
		pageTitle?: string;
		pageSubtitle?: string;

		formSubmitHandler?: (event: SubmitEvent) => void;
		// You'll need to define GenericContactForm snippet if it's not imported from elsewhere
		GenericContactForm?: (props: { onSubmit?: (event: SubmitEvent) => void }) => Snippet;
	}>();

	type ContactFormSnippetProps = { onSubmit?: (event: SubmitEvent) => void };
</script>

{#snippet SubmitButton()}
	<button
		type="submit"
		class="mt-4 inline-flex items-center justify-center rounded-full border border-transparent bg-[#5D28B6] px-4 py-2 text-base font-medium text-white transition duration-200 hover:bg-purple-800"
	>
		Submit
		<span class="ml-2 inline-flex items-center justify-center rounded-full p-1"
			><svg
				width="33"
				height="33"
				viewBox="0 0 33 33"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<circle cx="16.5" cy="16.5" r="16.5" fill="white" />
				<path
					d="M10 24L23 11M23 11V23.48M23 11H10.52"
					stroke="#5D28B6"
					stroke-width="1.5"
					stroke-linecap="round"
					stroke-linejoin="round"
				/>
			</svg>
		</span>
	</button>
{/snippet}

{#snippet GenericContactForm(props: ContactFormSnippetProps = {})}
	<form
		class="space-y-5 rounded-lg bg-transparent p-6 shadow-none sm:p-8 md:space-y-6"
		onsubmit={props.onSubmit || (() => console.log('Form submitted (default handler)'))}
	>
		<div>
			<label
				for="firstName_{Math.random().toString(36).substring(7)}"
				class="mb-1 block text-sm font-medium text-gray-700">Fore Name*</label
			>
			<input
				type="text"
				name="firstName"
				id="firstName_{Math.random().toString(36).substring(7)}"
				class="block w-full rounded-md border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-purple-500"
				required
			/>
		</div>
		<div>
			<label
				for="lastName_{Math.random().toString(36).substring(7)}"
				class="mb-1 block text-sm font-medium text-gray-700">Last Name*</label
			>
			<input
				type="text"
				name="lastName"
				id="lastName_{Math.random().toString(36).substring(7)}"
				class="block w-full rounded-md border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-purple-500"
				required
			/>
		</div>
		<div>
			<label
				for="telephone_{Math.random().toString(36).substring(7)}"
				class="mb-1 block text-sm font-medium text-gray-700">Telephone Number*</label
			>
			<input
				type="tel"
				name="telephone"
				id="telephone_{Math.random().toString(36).substring(7)}"
				class="block w-full rounded-md border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-purple-500"
				required
			/>
		</div>
		<div>
			<label
				for="email_{Math.random().toString(36).substring(7)}"
				class="mb-1 block text-sm font-medium text-gray-700">Email*</label
			>
			<input
				type="email"
				name="email"
				id="email_{Math.random().toString(36).substring(7)}"
				class="block w-full rounded-md border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-purple-500"
				required
			/>
		</div>
		<div>
			<label
				for="roleApplyingFor_{Math.random().toString(36).substring(7)}"
				class="mb-1 block text-sm font-medium text-gray-700">Role Applying For*</label
			>
			<input
				type="text"
				name="roleApplyingFor"
				id="roleApplyingFor_{Math.random().toString(36).substring(7)}"
				class="block w-full rounded-md border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-purple-500"
				required
			/>
		</div>
		<div>
			<label
				for="cvUpload_{Math.random().toString(36).substring(7)}"
				class="mb-1 block text-sm font-medium text-gray-700">Upload CV</label
			>
			<div class="mt-1">
				<input
					type="file"
					name="cvUpload"
					id="cvUpload_{Math.random().toString(36).substring(7)}"
					class="block w-full cursor-pointer rounded-lg border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:outline-none"
				/>
			</div>
			<p class="mt-1 text-xs text-gray-500">
				When uploading documents, please ensure total file size is under 10MB.
			</p>
		</div>
		<div class="flex items-start">
			<div class="flex h-5 items-center">
				<input
					id="terms_{Math.random().toString(36).substring(7)}"
					aria-describedby="terms"
					type="checkbox"
					class="h-4 w-4 rounded border border-gray-300 bg-gray-50 focus:ring-3 focus:ring-purple-300"
					required
				/>
			</div>
			<div class="ml-3 text-sm">
				<label for="terms_{Math.random().toString(36).substring(7)}" class="text-gray-600">
					IMPORTANT: By completing the above form and Uploading CV to this website, you consent to
					such information being collected, held, used for job finding purposes, and disclosed in
					accordance with our <a
						href={externalLinks?.privacyPolicy?.url}
						class="text-purple-600 hover:underline">Privacy Policy</a
					>
					and our website
					<a href={externalLinks?.termsOfUse?.url} class="text-purple-600 hover:underline"
						>Terms of Use</a
					>
				</label>
			</div>
		</div>
		{@render SubmitButton()}
	</form>
{/snippet}

<section class="bg-transparent py-12 md:py-20">
	<div class="container mx-auto px-4 sm:px-6 lg:px-8">
		<div class="mb-10 text-center md:mb-14">
			<p class="mb-2 text-sm font-semibold tracking-wider text-[#FF6F00] uppercase">
				{pageLabel}
			</p>
			<h2
				class="text-3xl leading-tight font-bold text-[#0586AB] capitalize md:text-4xl lg:text-[40px]"
			>
				{@html pageTitle}
			</h2>
			<p class="mx-auto mt-4 max-w-xl text-base text-[#737373]">
				{pageSubtitle}
			</p>
		</div>

		<div class="grid grid-cols-1 items-start gap-8 md:gap-12 lg:grid-cols-2">
			<div class="space-y-6">
				<div class="w-full overflow-hidden">
					<img
						src="/ongoing/08.png"
						alt=""
						class="h-auto max-h-[550px] w-full max-w-[590px] object-cover"
					/>
				</div>
				<div class="max-w-[60ch] space-y-4 text-[#151414]">
					<p class=" text-xl font-semibold">
						ARE YOU A Healthcare or Medical professional? Looking for Locum / Placements?
					</p>

					<p class="text-base font-normal">
						To be the first to know about new offers please click on download to complete your
						application form or our candidate online registration form, our consultants will contact
						you.
					</p>
					<p>Or</p>
					<p class=" text-xl font-semibold">
						Are you looking for permanent or temping / contact positions in IT, HR / Administration
						/ Sales / Marketing / Accounting?
					</p>
					<p class="text-base font-normal">
						Be the first to know of new offers, complete our candidate registration form and our
						consultants will contact you.
					</p>
				</div>
			</div>

			<div>
				{@render GenericContactForm({ onSubmit: formSubmitHandler })}
			</div>
		</div>
	</div>
</section>
