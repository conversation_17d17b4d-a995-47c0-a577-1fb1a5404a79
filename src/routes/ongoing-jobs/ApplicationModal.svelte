<script lang="ts">
	import { getContext } from 'svelte';
	import type { ExternalLinksConfig } from '../../app';

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;
	let { open, jobDetails = $bindable(), onSubmitSuccess, onClose } = $props();

	import { portalAction } from '@sveltelegos-blue/svelte-legos';

	let firstName = $state('');
	let lastName = $state('');
	let telephone = $state('');
	let email = $state('');
	let roleApplyingFor = $derived(
		`${jobDetails?.title?.toUpperCase()}, at ${jobDetails?.companyName}`
	);
	let cvFile = $state<File | null>(null);
	let cvFileName = $derived(cvFile ? cvFile.name : '');
	let understandAndAgree = $state(false);

	// $effect(() => {
	// 	if (open && jobTitle !== roleApplyingFor) {
	// 		// Update if jobTitle prop changes while modal is open
	// 		roleApplyingFor = jobTitle;
	// 	}
	// });

	function handleFileChange(event: Event) {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			cvFile = target.files[0];
			if (cvFile.size > 10 * 1024 * 1024) {
				// 10MB
				alert('File size should be under 10MB.');
				cvFile = null; // Reset
			}
		} else {
			cvFile = null;
		}
	}

	function handleSubmitApplication() {
		if (!firstName || !lastName || !email || !roleApplyingFor || !cvFile || !understandAndAgree) {
			alert('Please fill in all required fields and upload your CV.');
			return;
		}
		onSubmitSuccess();
	}
</script>

{#if open}
	<div use:portalAction={'body'}>
		<div class="modal-backdrop" role="presentation">
			<div
				class="application-modal-content"
				role="dialog"
				aria-labelledby="application-modal-title"
			>
				<button class="modal-close-button" onclick={onClose} aria-label="Close modal"
					>&times;</button
				>
				<h2 id="application-modal-title" class="modal-title">Submit Job Application</h2>

				<form onsubmit={handleSubmitApplication} class="application-form">
					<div class="form-group">
						<label for="firstName">Fore Name*</label>
						<input type="text" id="firstName" bind:value={firstName} required />
					</div>
					<div class="form-group">
						<label for="lastName">Last Name*</label>
						<input type="text" id="lastName" bind:value={lastName} required />
					</div>
					<div class="form-group">
						<label for="telephone">Telephone Number*</label>
						<input type="tel" id="telephone" bind:value={telephone} required />
					</div>
					<div class="form-group">
						<label for="email">Email*</label>
						<input type="email" id="email" bind:value={email} required />
					</div>
					<div class="form-group">
						<label for="role">Role Applying For*</label>
						<input type="text" id="role" bind:value={roleApplyingFor} required />
					</div>
					<div class="form-group">
						<label for="cv">Upload CV*</label>
						<div class="file-upload-wrapper">
							<input
								type="file"
								id="cv"
								accept=".pdf,.doc,.docx"
								onchange={handleFileChange}
								style="display: none;"
							/>
							<button
								type="button"
								class="choose-file-btn"
								onclick={() => (document.getElementById('cv') as HTMLInputElement)?.click()}
							>
								Choose File
							</button>
							{#if cvFileName}
								<span class="file-name-display">
									<svg
										width="16"
										height="16"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
										style="margin-right: 5px; color: #D3382B;"
									>
										<path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"
										></path><polyline points="13 2 13 9 20 9"></polyline>
									</svg>
									{cvFileName}
								</span>
							{/if}
						</div>
						<p class="file-info-text">
							By completing and submitting this form, you are granting implied consent to Central
							Staffing for the processing of information for purpose of your enquiry and will not be
							shared with third parties without consent.
							<a href={externalLinks?.privacyPolicy?.url} class="text-purple-600 hover:underline"
								>Privacy Policy</a
							>
							and
							<a href={externalLinks?.termsOfUse?.url} class="text-purple-600 hover:underline"
								>Terms of Use</a
							>
						</p>
					</div>
					<div class="form-group checkbox-group">
						<input
							type="checkbox"
							id="understandAndAgree"
							bind:checked={understandAndAgree}
							required
						/>
						<label for="understandAndAgree" class="checkbox-label">I understand and agree</label>
					</div>
					<p class="file-size-info">
						**When uploading documents, please ensure total file size is under 10mb
					</p>

					<div class="form-actions">
						<button type="submit" class="submit-button">
							Submit
							<svg
								width="16"
								height="16"
								viewBox="0 0 24 24"
								fill="none"
								xmlns="http://www.w3.org/2000/svg"
								style="margin-left: 8px;"
							>
								<path
									d="M10 5L17 12L10 19"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
								/>
								<path
									d="M17 12H3"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
								/>
							</svg>
						</button>
					</div>
				</form>
			</div>
		</div>
	</div>
{/if}

<style>
	.modal-backdrop {
		/* ... (same as JobDetailModal or define globally) ... */
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.6);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
		padding: 20px;
		box-sizing: border-box;
	}
	.application-modal-content {
		/* ... (similar to JobDetailModal, adjust padding/width as needed) ... */
		background-color: white;
		padding: 30px 40px;
		border-radius: 16px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
		width: 100%;
		max-width: 600px; /* Adjusted for application form */
		max-height: 90vh;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow-y: auto; /* Scroll for form content */
		font-family:
			system-ui,
			-apple-system,
			BlinkMacSystemFont,
			'Segoe UI',
			Roboto,
			Oxygen,
			Ubuntu,
			Cantarell,
			'Open Sans',
			'Helvetica Neue',
			sans-serif;
	}
	.modal-close-button {
		/* ... (same as JobDetailModal) ... */
		position: absolute;
		top: 15px;
		right: 20px;
		background: none;
		border: none;
		font-size: 2rem;
		color: #888;
		cursor: pointer;
		line-height: 1;
		padding: 5px;
	}
	.modal-close-button:hover {
		color: #333;
	}

	.modal-title {
		text-align: center;
		font-size: 1.6rem;
		font-weight: 600;
		color: #265594; /* Blueish title color from image */
		margin-bottom: 25px;
	}

	.application-form {
		display: flex;
		flex-direction: column;
		gap: 18px; /* Space between form groups */
	}
	.form-group {
		display: flex;
		flex-direction: column;
	}
	.form-group label {
		font-size: 0.9rem;
		color: #555;
		margin-bottom: 6px;
	}
	.form-group input[type='text'],
	.form-group input[type='tel'],
	.form-group input[type='email'] {
		padding: 12px 15px;
		border: 1px solid #ccc;
		border-radius: 6px;
		font-size: 1rem;
		transition: border-color 0.2s ease;
	}
	.form-group input[type='text']:focus,
	.form-group input[type='tel']:focus,
	.form-group input[type='email']:focus {
		border-color: #6200ee; /* Purple focus */
		outline: none;
	}

	.file-upload-wrapper {
		display: flex;
		align-items: center;
		gap: 10px;
		margin-top: 5px;
	}
	.choose-file-btn {
		background-color: #f0f0f0;
		border: 1px solid #ddd;
		padding: 10px 15px;
		border-radius: 6px;
		cursor: pointer;
		font-size: 0.9rem;
		transition: background-color 0.2s ease;
	}
	.choose-file-btn:hover {
		background-color: #e0e0e0;
	}
	.file-name-display {
		font-size: 0.9rem;
		color: #333;
		display: flex;
		align-items: center;
	}
	.file-info-text {
		font-size: 0.8rem;
		color: #777;
		margin-top: 8px;
		line-height: 1.4;
	}
	.checkbox-group {
		flex-direction: row;
		align-items: center;
		gap: 8px;
		margin-top: 10px;
	}
	.checkbox-group input[type='checkbox'] {
		width: 18px;
		height: 18px;
		accent-color: #6200ee; /* Purple checkbox */
	}
	.checkbox-label {
		font-size: 0.9rem;
		color: #333;
		margin-bottom: 0;
	}
	.file-size-info {
		font-size: 0.8rem;
		color: #777;
		margin-top: 5px;
	}
	.form-actions {
		margin-top: 20px;
		display: flex;
		justify-content: flex-start; /* Align submit to left */
	}
	.submit-button {
		background-color: #6200ee; /* Purple color */
		color: white;
		border: none;
		padding: 12px 25px;
		font-size: 1rem;
		font-weight: 500;
		border-radius: 25px; /* Pill shape */
		cursor: pointer;
		transition: background-color 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.submit-button:hover {
		background-color: #5300d0;
	}
	.submit-button svg {
		transition: transform 0.2s ease;
	}
	.submit-button:hover svg {
		transform: translateX(3px);
	}
</style>
