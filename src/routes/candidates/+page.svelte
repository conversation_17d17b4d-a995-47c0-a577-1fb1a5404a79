<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import ServiceSplitScreen from '$lib/components/shared/ServiceSplitScreen.svelte';

	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';

	const h1 = `Connecting Talents With <br /> Opportunity Across <br /> Healthcare, Industrial, & <br />Commercial Sectors`;

	const p =
		'We source, screen, and place the right people in the right roles — so businesses run smoothly and careers thrive.';

	const service_listItems: ListItem[] = [
		{ text: 'General Nurses' },
		{ text: 'Mental Health Nurses' },
		{ text: 'MidWives' },
		{ text: 'Doctor / GPs /  Consultants' },
		{ text: 'Dentists' },
		{ text: 'Allied HealthCare Professionals ' },
		{ text: 'Social Workers' },
		{ text: 'AMP Practitioners' },
		{ text: 'Support Workers ' },
		{ text: 'Health Care Assistants' }
	];

	// const first = {
	// 	topLabel: 'STAFFING SOLUTION',
	// 	mainHeading: 'Permanent, Temporary, Contract Service',
	// 	subHeadingParagraph: `Lorem ipsum dolor sit amet consectetur. Adipiscing nec ipsum mauris porttitor adipiscing lorem praesent massa. Purus velit nunc dolor in posuere netus.`
	// };
</script>

{#snippet Image()}
	<div class="aspect-square w-full max-w-xl">
		<img
			src="/candidates/01.avif"
			alt="Industrial workers shaking hands"
			class="h-full w-full object-cover"
		/>
	</div>
{/snippet}

<!-- {#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm />
		<ReferAFriend />
	</div>
{/snippet} -->

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

{#snippet FirstImageSection()}
	<div class="aspect-square w-full max-w-xl">
		<img
			src="/candidates/02.avif"
			alt="Industrial workers shaking hands"
			class="h-full w-full object-cover"
		/>
	</div>
{/snippet}

{#snippet FirstBottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm link="/downloads" />
		<ReferAFriend />
	</div>
{/snippet}

<ServiceSplitScreen
	topLabel="MEDICALS"
	mainHeading="Medicals & HealthCare Professionals"
	subHeadingParagraph=""
	textParagraph1=""
	textParagraph2=""
	listHeading=""
	listItems={service_listItems}
	imagePosition="left"
	imageSnippet={FirstImageSection}
	baseClass="!pb-0"
	bottomFooter={FirstBottomFooter}
/>

{#snippet SecondSnippet()}
	<div class="aspect-square w-full max-w-xl">
		<img
			src="/candidates/03.avif"
			alt="Industrial workers shaking hands"
			class="h-full w-full object-cover"
		/>

		<!-- <p class="mt-6 font-bold text-[#151414]">
			Click Here To Download The Application Form To Apply Today
		</p> -->
	</div>
{/snippet}

<ServiceSplitScreen
	topLabel="COMMERCIAL"
	mainHeading="Commercial (Office Professionals)"
	subHeadingParagraph=""
	textParagraph1=""
	textParagraph2=""
	listHeading=""
	listItems={[
		{ text: 'Human Resources' },
		{ text: 'Recruitment Consultants' },
		{ text: 'Finance / Account' },
		{ text: 'Sales & Retail (customer service/sales/telesales' },
		{ text: 'Call Centres' },
		{ text: 'Marketing' },
		{ text: 'Medical Administrators / Front Desk Personnel' },
		{ text: "Office Administrators / Front Desk Personnel / PA's" },
		{ text: 'Junior / Senior Management Roles' },
		{ text: 'Banking' },
		{ text: 'IT / Project Managers' }
	]}
	imagePosition="right"
	imageSnippet={SecondSnippet}
	baseClass="!pb-0"
/>
<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
	<div class="flex flex-col gap-6 md:flex-row">
		<ReferAFriend />
		<div class="hidden lg:block">
			<ReferAFriend title="Send FeedBack" link="/contact#feedback" />
		</div>
	</div>
	<DownloadApplicationForm link="/contact#join-us" text="Register With Us Today" />
</div>

{#snippet ThirdImageSection()}
	<div class="aspect-square w-full max-w-xl">
		<img
			src="/candidates/04.png"
			alt="Industrial workers shaking hands"
			class="h-full w-full object-cover"
		/>
	</div>
{/snippet}

{#snippet ThirdBottomFooter()}
	<div class="mt-auto flex w-full flex-col gap-4 pt-6 sm:flex-row">
		<ReferAFriend />
		<DownloadApplicationForm link="/contact#join-us" text="Register With Us Today" />
	</div>
{/snippet}

<ServiceSplitScreen
	topLabel="Industrial"
	mainHeading="Industrial"
	subHeadingParagraph=""
	textParagraph1=""
	textParagraph2=""
	listHeading=""
	listItems={[
		{ text: 'Warehousing' },
		{ text: 'Cleaners' },
		{ text: 'House Keepers' },
		{ text: 'Kitchen Assistants' },
		{ text: 'Potters' },
		{ text: 'Laundry Assistants' }
	]}
	imagePosition="left"
	imageSnippet={ThirdImageSection}
	baseClass="!pb-0"
	bottomFooter={ThirdBottomFooter}
/>
<!-- <div class="mt-auto flex w-full flex-col gap-4 pt-6 sm:flex-row">
	<DownloadApplicationForm />
	<ReferAFriend />
</div> -->

<div class="mt-5">
	<Subscribe />
</div>
<svelte:head>
	<title>Career Opportunities for Candidates | Central Staffing</title>
	<meta
		name="description"
		content="Looking for your next career move? Central Staffing connects talented candidates with opportunities in healthcare, commercial, and industrial sectors. Register with us today."
	/>
	<meta
		name="keywords"
		content="jobs for candidates, career opportunities, healthcare jobs, commercial vacancies, industrial roles, register as a candidate, Central Staffing careers, nursing jobs, admin roles"
	/>
</svelte:head>

<style>
	/* .special {
		z-index: -20;
		border: solid 1px#FFFAFF;
		border-radius: 20px;
	} */
</style>
