<script lang="ts">
	import MainHomepage from '$lib/components/tenants/MainHomepage.svelte';
	import DomiciliaryHomepage from '$lib/components/tenants/DomiciliaryHomepage.svelte';
	import SupportedHomepage from '$lib/components/tenants/SupportedHomepage.svelte';
	import type { PageProps } from './$types';

	let { data }: PageProps = $props();
	let tenant = $derived(data.tenant);
</script>

{#if tenant === 'domiciliary'}
	<DomiciliaryHomepage />
{:else if tenant === 'supported'}
	<SupportedHomepage />
{:else}
	<MainHomepage {data} />
{/if}
