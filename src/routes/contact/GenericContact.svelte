<script lang="ts">
	const PUBLIC_HCAPTCHA_SITE_KEY = import.meta.env.VITE_PUBLIC_HCAPTCHA_SITE_KEY;
	import { getContext } from 'svelte';

	let {
		open = $bindable(false),
		formType = $bindable(''),
		jobTitle = $bindable(''),
		onSubmit,
		onSubmit: originalOnSubmit,
		onClose: originalOnClose
	}: {
		open?: boolean;
		formType?:
			| 'jobApplication'
			| 'referFriend'
			| 'sendFeedback'
			| 'joinUs'
			| 'requestCallBack'
			| 'candidateRegistration';
		jobTitle?: string;
		onSubmit?: (apiResponse: {
			success: boolean;
			message?: string;
			messageId?: string;
			error?: any;
		}) => void;
		onClose?: () => void;
	} = $props();

	import { portalAction } from '@sveltelegos-blue/svelte-legos';
	import { clickOutsideAction } from '@sveltelegos-blue/svelte-legos';
	import type { Snippet } from 'svelte';
	import HCaptcha from '../admin/auth/HCaptcha.svelte';
	import { watch } from 'runed';
	import type { ExternalLinksConfig } from '../../app';

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;

	type FormField = {
		name: string;
		label: string;
		type: 'text' | 'email' | 'tel' | 'textarea' | 'file' | 'checkbox' | 'select';
		required: boolean;
		placeholder?: string;
		options?: string[]; // For select
		fileAccept?: string; // For file input
		infoText?: string; // e.g., for CV upload
		checkboxText?: string | Snippet; // For checkbox label (can include HTML)
		defaultValue?: string | boolean;
	};

	let formData = $state<Record<string, any>>({});
	let cvFile = $state<File | null>(null);
	let cvFileName = $derived(cvFile ? cvFile.name : '');

	// Define field configurations for each form type
	let formConfigs: Record<
		string,
		{
			title: string;
			description?: string;
			fields: FormField[];
			submitButtonText: string;
			termsKey?: string;
		}
	> = $derived.by(() => {
		return {
			jobApplication: {
				title: 'Submit Job Application',
				submitButtonText: 'Submit Application',
				termsKey: 'jobApplicationTerms',
				fields: [
					{ name: 'firstName', label: 'Fore Name*', type: 'text', required: true },
					{ name: 'lastName', label: 'Last Name*', type: 'text', required: true },
					{ name: 'telephone', label: 'Telephone Number*', type: 'tel', required: true },
					{ name: 'email', label: 'Email*', type: 'email', required: true },
					{
						name: 'roleApplyingFor',
						label: 'Role Applying For*',
						type: 'text',
						required: true,
						defaultValue: jobTitle
					},
					{
						name: 'cv',
						label: 'Upload CV*',
						type: 'file',
						required: true,
						fileAccept: '.pdf,.doc,.docx',
						infoText: 'When uploading documents, please ensure total file size is under 10MB.'
					},
					{
						name: 'understandAndAgree',
						label: '*I understand and agree',
						type: 'checkbox',
						required: true,
						checkboxText: `	<p class="text-xs text-gray-500">
				By completing and submitting this form, you are granting implied consent to Central Staffing
				for the processing of information for purpose of your enquiry and will not be shared with
				third parties without consent.
				<a href="${externalLinks.privacyPolicy?.url}" class="text-purple-600 hover:underline">Privacy Policy</a> and
				<a href="${externalLinks.termsOfUse?.url}" class="text-purple-600 hover:underline">Terms of Use</a>
			</p>`
					}
				]
			},
			referFriend: {
				title: 'Refer a Friend',
				description:
					'Refer a friend today and earn up to £250 per referral. Further terms and conditions apply, fill the form below. We will contact you.',
				submitButtonText: 'Submit Referral',
				termsKey: 'referFriendTerms',
				fields: [
					// Your details
					{
						name: 'referrerFirstName',
						label: 'Fore Name* (Your Details)',
						type: 'text',
						required: true
					},
					{
						name: 'referrerLastName',
						label: 'Last Name* (Your Details)',
						type: 'text',
						required: true
					},
					{
						name: 'referrerEmail',
						label: 'Email Address* (Your Details)',
						type: 'email',
						required: true
					},
					{
						name: 'referrerPosition',
						label: 'Position* (Your Details)',
						type: 'text',
						required: true
					},
					// Recommends section (Friend's details)
					{
						name: 'friendFirstName',
						label: 'Fore Name* (Recommends)',
						type: 'text',
						required: true
					},
					{ name: 'friendSurname', label: 'Surname* (Recommends)', type: 'text', required: true },
					{ name: 'friendLocation', label: 'Location* (Recommends)', type: 'text', required: true },
					{ name: 'friendSector', label: 'Sector* (Recommends)', type: 'text', required: true },
					{
						name: 'friendPartFullTime',
						label: 'Part/Full Time (Recommends)',
						type: 'select',
						options: ['Part-Time', 'Full-Time'],
						required: true
					},
					{
						name: 'friendContactNumber',
						label: 'Contact Number* (Recommends)',
						type: 'tel',
						required: true
					},
					{
						name: 'friendEmailAddress',
						label: 'Email Address* (Recommends)',
						type: 'email',
						required: true
					},
					{
						name: 'referUnderstandAndAgree',
						label: '*I understand and agree',
						type: 'checkbox',
						required: true,
						checkboxText: `By Referring a friend you have consented to their contact information being processed, stored and shared for the purposes of this referral. Central Staffing will only use the provided contact information for the purpose of processing the referral in accordance with our <a href="/privacy-policy" class="text-purple-600 hover:underline">Privacy Policy</a> and <a href="${externalLinks.termsOfUse?.url}" class="text-purple-600 hover:underline">Terms of Use</a>`
					}
				]
			},
			sendFeedback: {
				title: 'Send Feedback',
				description:
					'We value your feedback—please include relevant details like position applied for, reference number, consultant name, date, or links to help us address your query effectively.',
				submitButtonText: 'Send Feedback',
				termsKey: 'feedbackTerms',
				fields: [
					{ name: 'feedbackFirstName', label: 'Fore Name*', type: 'text', required: true },
					{ name: 'feedbackLastName', label: 'Last Name*', type: 'text', required: true },
					{ name: 'feedbackEmail', label: 'Email*', type: 'email', required: true },
					{ name: 'feedbackSubject', label: 'Subject*', type: 'text', required: true },
					{
						name: 'feedbackComments',
						label: 'Comments / Questions*',
						type: 'textarea',
						required: true
					},
					{
						name: 'feedbackUnderstandAndAgree',
						label: '*I understand and agree',
						type: 'checkbox',
						required: true,
						checkboxText: `IMPORTANT: By submitting your email address and every other personal information to this website, you consent to such information being processed, held, used and disclosed in accordance with our <a href="/privacy-policy" class="text-purple-600 hover:underline">Privacy Policy</a> and <a href="${externalLinks.termsOfUse?.url}" class="text-purple-600 hover:underline">Terms of Use</a>.`
					}
				]
			},
			joinUs: {
				// Assuming this is similar to Request a Call Back but for joining
				title: 'Join Us',
				description:
					'Advance your career with a us, We are a trusted Recruitment Leader in Healthcare, Commercial, and Industrial Sectors.',
				submitButtonText: 'Request A Call Back',
				termsKey: 'joinUsTerms',
				fields: [
					{ name: 'joinFirstName', label: 'Fore Name*', type: 'text', required: true },
					{ name: 'joinLastName', label: 'Last Name*', type: 'text', required: true },
					{ name: 'joinCompanyName', label: 'Company Name', type: 'text', required: false },
					{ name: 'joinPosition', label: 'Position', type: 'text', required: false },
					{ name: 'joinEmail', label: 'Email Address*', type: 'email', required: true },
					{ name: 'joinContactNumber', label: 'Contact Number*', type: 'tel', required: true },
					{ name: 'joinDetails', label: 'Details of Request*', type: 'textarea', required: true },
					{
						name: 'joinUnderstandAndAgree',
						label: '*I understand and agree',
						type: 'checkbox',
						required: true,
						checkboxText: `By completing and submitting this form, you are granting implied consent to Central Staffing for the processing of information for purpose of your enquiry and will not be shared with third parties without consent. <a href="/privacy-policy" class="text-purple-600 hover:underline">Privacy Policy</a> and <a href="${externalLinks.termsOfUse?.url}" class="text-purple-600 hover:underline">Terms of Use</a>`
					}
				]
			},
			requestCallBack: {
				title: 'Request a Call Back',
				description: 'Let’s Talk About Your Staffing Needs – Our Team is Just a Call Away.',
				submitButtonText: 'Request A Call Back',
				termsKey: 'requestCallbackTerms',
				fields: [
					// Same fields as "Join Us" as per images
					{ name: 'callbackFirstName', label: 'Fore Name*', type: 'text', required: true },
					{ name: 'callbackLastName', label: 'Last Name*', type: 'text', required: true },
					{ name: 'callbackCompanyName', label: 'Company Name', type: 'text', required: false },
					{ name: 'callbackPosition', label: 'Position', type: 'text', required: false },
					{ name: 'callbackEmail', label: 'Email Address*', type: 'email', required: true },
					{ name: 'callbackContactNumber', label: 'Contact Number*', type: 'tel', required: true },
					{
						name: 'callbackDetails',
						label: 'Details of Request*',
						type: 'textarea',
						required: true
					},
					{
						name: 'callbackUnderstandAndAgree',
						label: '*I understand and agree',
						type: 'checkbox',
						required: true,
						checkboxText: `By completing and submitting this form, you are granting implied consent to Central Staffing for the processing of information for purpose of your enquiry and will not be shared with third parties without consent. <a href="/privacy-policy" class="text-purple-600 hover:underline">Privacy Policy</a> and <a href="${externalLinks.termsOfUse?.url}" class="text-purple-600 hover:underline">Terms of Use</a>`
					}
				]
			},
			candidateRegistration: {
				// Same as jobApplication form
				title: 'Candidate Registration',
				description:
					'ARE YOU A Healthcare or Medical professional? Looking for Locum /  Placements?',
				submitButtonText: 'Submit',
				termsKey: 'candidateRegTerms',
				fields: [
					{ name: 'regFirstName', label: 'First Name*', type: 'text', required: true },
					{ name: 'regLastName', label: 'Last Name*', type: 'text', required: true },
					{ name: 'regTelephone', label: 'Telephone Number*', type: 'tel', required: true },
					{ name: 'regEmail', label: 'Email*', type: 'email', required: true },
					{
						name: 'regRoleApplyingFor',
						label: 'Role Applying For*',
						type: 'text',
						required: true,
						defaultValue: jobTitle
					},
					{
						name: 'regCv',
						label: 'Upload CV*',
						type: 'file',
						required: true,
						fileAccept: '.pdf,.doc,.docx',
						infoText: 'When uploading documents, please ensure total file size is under 10MB.'
					},
					{
						name: 'regUnderstandAndAgree',
						label: '*I understand and agree',
						type: 'checkbox',
						required: true,
						checkboxText: `Ensure you agree to our <a href="/privacy-policy" class="text-purple-600 hover:underline">Privacy Policy</a> and <a href="${externalLinks.termsOfUse?.url}" class="text-purple-600 hover:underline">Terms of Use</a>.`
					}
				]
			}
		};
	});
	let runCount = $state(0);

	$effect(() => {
		if (formType !== 'jobApplication') {
			return;
		}

		if (runCount > 1) return;

		formData = {
			...formData,
			roleApplyingFor: jobTitle
		};
		console.log(formData);
		runCount += 1;
	});

	const onClose = () => {
		runCount = 0;
		open = false;
		// reset form
		formData = {};
		cvFile = null;
		cvFileName = '';
		formApiError = undefined;
		formApiSuccess = undefined;
		isSubmitting = false;
		passedCaptcha = false;
		passToken = undefined;
		if (originalOnClose) originalOnClose();
	};

	// watch(
	// 	() => formType === 'jobApplication',

	// 	() => {
	// 		if (formType === 'jobApplication') {
	// 			formData = {
	// 				...formData,
	// 				roleApplyingFor: jobTitle
	// 			};
	// 		}
	// 	}
	// );

	let currentConfig = $derived(formConfigs[formType] || formConfigs.sendFeedback); // Fallback to a default

	let isSubmitting = $state(false);
	let formApiError = $state<string | undefined>(undefined);
	let formApiSuccess = $state<string | undefined>(undefined);

	let passedCaptcha = $state(false);
	let passToken = $state<string | undefined>(undefined);
	let hcaptchaRef = $state(); // To potentially reset captcha

	// Reset form and captcha when modal opens or formType changes
	// $effect(() => {
	// 	if (open) {
	// 		// Reset form data
	// 		const newFormData: Record<string, any> = {};
	// 		currentConfig.fields.forEach((field) => {
	// 			if (field.name === 'roleApplyingFor' || field.name === 'regRoleApplyingFor') {
	// 				newFormData[field.name] = jobTitle || field.defaultValue || '';
	// 			} else if (
	// 				field.name.toLowerCase().includes('email') &&
	// 				formType === 'jobApplication' &&
	// 				applicationDetails?.candidateEmail
	// 			) {
	// 				// Example: Pre-fill from some context if available
	// 				newFormData[field.name] = applicationDetails.candidateEmail;
	// 			} else {
	// 				newFormData[field.name] =
	// 					field.defaultValue !== undefined
	// 						? field.defaultValue
	// 						: field.type === 'checkbox'
	// 							? false
	// 							: '';
	// 			}
	// 		});
	// 		formData = newFormData;
	// 		cvFile = null;
	// 		formApiError = undefined;
	// 		formApiSuccess = undefined;
	// 		isSubmitting = false;

	// 		// Reset CAPTCHA
	// 		passedCaptcha = false;
	// 		passToken = undefined;
	// 		// If your HCaptcha component has a reset method, call it:
	// 		// hcaptchaRef?.reset(); // Assuming HCaptcha.svelte exposes a reset method
	// 	}
	// });

	// $effect(() => {
	// 	if (open) {
	// 		// Reset form data when modal opens or formType changes
	// 		const newFormData: Record<string, any> = {};
	// 		currentConfig.fields.forEach((field) => {
	// 			if (field.name === 'roleApplyingFor' || field.name === 'regRoleApplyingFor') {
	// 				newFormData[field.name] = jobTitle || '';
	// 			} else {
	// 				newFormData[field.name] = field.type === 'checkbox' ? false : '';
	// 			}
	// 		});
	// 		formData = newFormData;
	// 		cvFile = null;

	// 		// Update specific default values if necessary
	// 		if (formType === 'jobApplication' || formType === 'candidateRegistration') {
	// 			const roleField = currentConfig.fields.find(
	// 				(f) => f.name === 'roleApplyingFor' || f.name === 'regRoleApplyingFor'
	// 			);
	// 			if (roleField) formData[roleField.name] = jobTitle;
	// 		}
	// 	}
	// });

	function handleFileChange(event: Event, fieldName: string) {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			const file = target.files[0];
			if (file.size > 10 * 1024 * 1024) {
				// 10MB
				alert('File size should be under 10MB.');
				formData[fieldName] = null; // Or some other way to mark as invalid
				if (fieldName === 'cv' || fieldName === 'regCv') cvFile = null;
			} else {
				formData[fieldName] = file; // Store the File object
				if (fieldName === 'cv' || fieldName === 'regCv') cvFile = file; // For cvFileName derived
			}
		} else {
			formData[fieldName] = null;
			if (fieldName === 'cv' || fieldName === 'regCv') cvFile = null;
		}
	}

	async function handleFormSubmitInternal(event: SubmitEvent) {
		event.preventDefault();
		formApiError = undefined;
		formApiSuccess = undefined;

		if (!passedCaptcha || !passToken) {
			formApiError = 'Please complete the CAPTCHA.';
			return;
		}

		// Basic validation check (keep your existing validation)
		for (const field of currentConfig.fields) {
			if (field.required) {
				const value = formData[field.name];
				if (field.type === 'checkbox' && !value) {
					alert(`Please agree to the terms for "${field.label}".`);
					return;
				}
				if (field.type === 'file' && (field.name === 'cv' || field.name === 'regCv') && !cvFile) {
					alert(`Please upload your CV for "${field.label}".`);
					return;
				}
				if (!value && field.type !== 'checkbox' && field.type !== 'file') {
					alert(`Please fill in all required fields. Field "${field.label}" is missing.`);
					return;
				}
			}
		}

		isSubmitting = true;

		const submissionPayload = new FormData(); // Use FormData for file uploads

		// Append all form data
		for (const key in formData) {
			if (formData[key] instanceof File) {
				submissionPayload.append(key, formData[key], (formData[key] as File).name);
			} else if (formData[key] !== undefined && formData[key] !== null) {
				submissionPayload.append(key, String(formData[key]));
			}
		}
		// Ensure CV file is appended if it's managed by cvFile state
		if (formType === 'jobApplication' || formType === 'candidateRegistration') {
			const cvFieldConfig = currentConfig.fields.find(
				(f) => (f.name === 'cv' || f.name === 'regCv') && f.type === 'file'
			);
			if (cvFieldConfig && cvFile) {
				if (!submissionPayload.has(cvFieldConfig.name)) {
					submissionPayload.append(cvFieldConfig.name, cvFile, cvFile.name);
				}
			} else if (cvFieldConfig && cvFieldConfig.required && !cvFile) {
				alert(`Please upload your CV for the field "${cvFieldConfig.label}".`);
				isSubmitting = false;
				return;
			}
		}

		submissionPayload.append('formType', formType); // Add the formType
		submissionPayload.append('passToken', passToken); // Add the captcha token

		try {
			const response = await fetch('/api/v1/communication', {
				// Your new API endpoint
				method: 'POST',
				body: submissionPayload // Send FormData
				// Headers are set automatically by browser for FormData
			});

			const result = await response.json();

			if (response.ok && result.success) {
				formApiSuccess = result.message || 'Form submitted successfully!';
				if (originalOnSubmit) {
					originalOnSubmit(result); // Call the original onSubmit passed from parent
				} else if (onClose) {
					// Default behavior if no specific onSubmit is provided by parent,
					// could be to show success and then close.
					// For now, parent's `handleGenericFormSubmit` handles modal closing.
				}
			} else {
				formApiError = result.message || 'An error occurred. Please try again.';
				if (originalOnSubmit) {
					originalOnSubmit(result);
				}
			}
		} catch (error: any) {
			console.error('Submission error:', error);
			formApiError = 'A network error occurred. Please check your connection and try again.';
			if (originalOnSubmit) {
				originalOnSubmit({ success: false, error: error.message });
			}
		} finally {
			isSubmitting = false;
			// Reset CAPTCHA for next submission attempt on error, or if you want one-time use
			passedCaptcha = false;
			// passToken = undefined; // Token is usually single-use
			// hcaptchaRef?.reset();
		}
	}

	async function handleFormSubmit(event: SubmitEvent) {
		event.preventDefault();
		// Basic validation check
		for (const field of currentConfig.fields) {
			if (
				field.required &&
				(!formData[field.name] || (field.type === 'checkbox' && !formData[field.name]))
			) {
				if (field.type === 'file' && field.name === 'cv' && !cvFile) {
					// Specific check for main CV field
					alert(`Please fill in all required fields. Field "${field.label}" is missing.`);
					return;
				} else if (field.type !== 'file') {
					alert(`Please fill in all required fields. Field "${field.label}" is missing.`);
					return;
				}
			}
		}

		const submissionData = new FormData();
		for (const key in formData) {
			if (formData[key] instanceof File) {
				submissionData.append(key, formData[key], (formData[key] as File).name);
			} else {
				submissionData.append(key, formData[key]);
			}
		}
		// If cvFile was used for the main CV upload, ensure it's in submissionData
		// This depends on how you name your 'cv' field in configs. Let's assume field.name is 'cv' for the main one.
		if (formType === 'jobApplication' || formType === 'candidateRegistration') {
			const cvFieldConfig = currentConfig.fields.find((f) => f.type === 'file');
			if (cvFieldConfig && cvFile) {
				if (!submissionData.has(cvFieldConfig.name)) {
					// Avoid duplicate if already added via formData loop
					submissionData.append(cvFieldConfig.name, cvFile, cvFile.name);
				}
			} else if (cvFieldConfig && cvFieldConfig.required && !cvFile) {
				alert(`Please upload your CV for the field "${cvFieldConfig.label}".`);
				return;
			}
		}

		await onSubmit(Object.fromEntries(submissionData.entries())); // Pass FormData or JS object
	}

	const uid = Math.random().toString(36).substring(2);
</script>

{#if open}
	<div use:portalAction={'body'}>
		<div
			class="modal-backdrop"
			role="presentation"
			onkeydown={(e) => e.key === 'Escape' && onClose && onClose()}
		>
			<div
				class="application-modal-content"
				use:clickOutsideAction
				onclickoutside={onClose}
				role="dialog"
				aria-modal="true"
				aria-labelledby={`form-modal-title-${formType}`}
			>
				<button class="modal-close-button" onclick={onClose} aria-label="Close modal"
					>&times;</button
				>
				<h2 id={`form-modal-title-${formType}`} class="modal-title">{currentConfig.title}</h2>
				{#if currentConfig.description}
					<p class="modal-description">{@html currentConfig.description}</p>
				{/if}

				<form onsubmit={handleFormSubmitInternal} class="application-form" novalidate>
					{#if formApiError}
						<div role="alert" class="form-error-alert">{formApiError}</div>
					{/if}
					{#if formApiSuccess}
						<div role="alert" class="form-success-alert">{formApiSuccess}</div>
						<button
							type="button"
							onclick={onClose}
							class="submit-button"
							style="background-color: #4CAF50; margin-top:10px;">Close</button
						>
					{:else}
						{#each currentConfig.fields as field (field.name)}
							<div class="form-group">
								<label for={`${field.name}-${uid}`}>{field.label}</label>
								{#if field.type === 'textarea'}
									<textarea
										id={`${field.name}-${uid}`}
										name={field.name}
										rows="4"
										bind:value={formData[field.name]}
										required={field.required}
										placeholder={field.placeholder || ''}
										disabled={isSubmitting}
										class="block w-full resize-none rounded-md border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-purple-500"
									></textarea>
								{:else if field.type === 'file'}
									<div class="file-upload-wrapper">
										<input
											type="file"
											id={`${field.name}-${uid}`}
											name={field.name}
											accept={field.fileAccept || '*/*'}
											onchange={(e) => handleFileChange(e, field.name)}
											style="display: none;"
											required={field.required}
											disabled={isSubmitting}
										/>
										<button
											type="button"
											class="choose-file-btn"
											disabled={isSubmitting}
											onclick={() => {
												const elem = document.getElementById(
													`${field.name}-${uid}`
												) as HTMLInputElement;
												elem?.click();
											}}
										>
											Choose File
										</button>
										{#if (field.name === 'cv' || field.name === 'regCv') && cvFileName}
											<span class="file-name-display">
												{cvFileName}
											</span>
										{:else if field.name !== 'cv' && field.name !== 'regCv' && formData[field.name] && typeof formData[field.name] === 'object' && formData[field.name]?.name}
											<span class="file-name-display">
												{formData[field.name].name}
											</span>
										{/if}
									</div>
									{#if field.infoText}
										<p class="file-info-text">{@html field.infoText}</p>
									{/if}
								{:else if field.type === 'checkbox'}
									<div class="checkbox-group">
										<input
											type="checkbox"
											id={`${field.name}-${uid}`}
											name={field.name}
											bind:checked={formData[field.name]}
											required={field.required}
											disabled={isSubmitting}
										/>
										<label for={`${field.name}-${uid}`} class="checkbox-label">
											{#if typeof field.checkboxText === 'string'}
												{@html field.checkboxText}
											{:else}
												{@render field.checkboxText()}
											{/if}
										</label>
									</div>
								{:else if field.type === 'select'}
									<select
										id={`${field.name}-${uid}`}
										name={field.name}
										bind:value={formData[field.name]}
										required={field.required}
										disabled={isSubmitting}
										class="block w-full rounded-md border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-purple-500"
									>
										<option
											value=""
											disabled
											selected={formData[field.name] === undefined || formData[field.name] === ''}
											>Select an option</option
										>
										{#each field.options || [] as option}
											<option value={option}>{option}</option>
										{/each}
									</select>
								{:else}
									<input
										type={field.type}
										id={`${field.name}-${uid}`}
										name={field.name}
										bind:value={formData[field.name]}
										required={field.required}
										placeholder={field.placeholder || ''}
										disabled={isSubmitting}
										class="block w-full rounded-md border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-purple-500"
									/>
								{/if}
							</div>
						{/each}

						<div
							class="form-group hcaptcha-container"
							style="align-items: center; margin-top: 20px; margin-bottom:10px;"
						>
							<HCaptcha
								bind:this={hcaptchaRef}
								sitekey={PUBLIC_HCAPTCHA_SITE_KEY}
								on:success={(e) => {
									passToken = e.detail.token;
									passedCaptcha = true;
									if (formApiError === 'Please complete the CAPTCHA.') formApiError = undefined;
								}}
								on:error={(e) => {
									console.error('HCaptcha error event:', e.detail);
									passToken = undefined;
									passedCaptcha = false;
									formApiError =
										e.detail?.error || 'CAPTCHA verification failed. Please try again.';
								}}
								on:expired={() => {
									passToken = undefined;
									passedCaptcha = false;
									formApiError = 'CAPTCHA token expired. Please try again.';
								}}
							/>
						</div>

						<div class="form-actions">
							<button type="submit" class="submit-button" disabled={isSubmitting || !passedCaptcha}>
								{isSubmitting ? 'Submitting...' : currentConfig.submitButtonText}
								<svg
									width="33"
									height="33"
									viewBox="0 0 33 33"
									fill="none"
									xmlns="http://www.w3.org/2000/svg"
								>
									<circle cx="16.5" cy="16.5" r="16.5" fill="white" />
									<path
										d="M10 24L23 11M23 11V23.48M23 11H10.52"
										stroke="#5D28B6"
										stroke-width="1.5"
										stroke-linecap="round"
										stroke-linejoin="round"
									/>
								</svg>
							</button>
						</div>
					{/if}
				</form>
			</div>
		</div>
	</div>
{/if}

<style>
	.modal-backdrop {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.6);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
		padding: 20px;
		box-sizing: border-box;
	}
	.application-modal-content {
		background-color: white;
		padding: 30px 40px;
		border-radius: 16px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
		width: 100%;
		max-width: 600px;
		max-height: 90vh;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow-y: auto;
		font-family:
			system-ui,
			-apple-system,
			BlinkMacSystemFont,
			'Segoe UI',
			Roboto,
			Oxygen,
			Ubuntu,
			Cantarell,
			'Open Sans',
			'Helvetica Neue',
			sans-serif;
	}
	.modal-close-button {
		position: absolute;
		top: 15px;
		right: 20px;
		background: none;
		border: none;
		font-size: 2rem;
		color: #888;
		cursor: pointer;
		line-height: 1;
		padding: 5px;
	}
	.modal-close-button:hover {
		color: #333;
	}
	.modal-title {
		text-align: center;
		font-size: 1.6rem;
		font-weight: 600;
		color: #265594;
		margin-bottom: 15px;
	}
	.modal-description {
		text-align: center;
		font-size: 0.9rem;
		color: #555;
		margin-bottom: 20px;
		line-height: 1.5;
		max-width: 80%;
		margin-left: auto;
		margin-right: auto;
	}
	.application-form {
		display: flex;
		flex-direction: column;
		gap: 18px;
	}
	.form-group {
		display: flex;
		flex-direction: column;
	}
	.form-group label {
		font-size: 0.9rem;
		color: #555;
		margin-bottom: 6px;
	}
	.form-group input[type='text'],
	.form-group input[type='tel'],
	.form-group input[type='email'],
	.form-group textarea,
	.form-group select {
		padding: 10px 12px;
		border: 1px solid #ccc;
		border-radius: 6px;
		font-size: 0.95rem;
		transition: border-color 0.2s ease;
		background-color: #f9fafb; /* Light gray background for inputs */
	}
	.form-group input[type='text']:focus,
	.form-group input[type='tel']:focus,
	.form-group input[type='email']:focus,
	.form-group textarea:focus,
	.form-group select:focus {
		border-color: #6200ee;
		outline: none;
		box-shadow: 0 0 0 2px rgba(98, 0, 238, 0.2);
	}
	.file-upload-wrapper {
		display: flex;
		align-items: center;
		gap: 10px;
		margin-top: 5px;
	}
	.choose-file-btn {
		background-color: #e9ecef;
		border: 1px solid #ced4da;
		padding: 8px 12px;
		border-radius: 6px;
		cursor: pointer;
		font-size: 0.9rem;
		transition: background-color 0.2s ease;
		color: #495057;
	}
	.choose-file-btn:hover {
		background-color: #dee2e6;
	}
	.file-name-display {
		font-size: 0.9rem;
		color: #333;
		display: flex;
		align-items: center;
	}
	.file-info-text {
		font-size: 0.8rem;
		color: #777;
		margin-top: 8px;
		line-height: 1.4;
	}
	.checkbox-group {
		display: flex;
		flex-direction: row;
		align-items: center;
		gap: 8px;
		margin-top: 5px;
	}
	.checkbox-group input[type='checkbox'] {
		width: 16px;
		height: 16px;
		accent-color: #6200ee;
		border-radius: 3px;
		border: 1px solid #ccc;
	}
	.checkbox-label {
		font-size: 0.85rem;
		color: #454545;
		line-height: 1.4;
	}
	.checkbox-label a {
		color: #6200ee;
		text-decoration: underline;
	}
	.file-size-info {
		font-size: 0.8rem;
		color: #777;
		margin-top: 5px;
	}
	.form-actions {
		margin-top: 25px;
		display: flex;
		justify-content: flex-start;
	}
	.submit-button {
		background-color: #6200ee;
		color: white;
		border: none;
		padding: 10px 20px;
		font-size: 0.95rem;
		font-weight: 500;
		border-radius: 25px;
		cursor: pointer;
		transition: background-color 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10px;
	}
	.submit-button:hover {
		background-color: #5300d0;
	}
	.submit-button svg {
		transition: transform 0.2s ease;
	}
	.submit-button:hover svg {
		transform: translateX(3px);
	}

	.form-error-alert {
		background-color: #f8d7da;
		color: #721c24;
		padding: 10px;
		border: 1px solid #f5c6cb;
		border-radius: 5px;
		margin-bottom: 15px;
		text-align: center;
	}
	.form-success-alert {
		background-color: #d4edda;
		color: #155724;
		padding: 10px;
		border: 1px solid #c3e6cb;
		border-radius: 5px;
		margin-bottom: 15px;
		text-align: center;
	}
	.hcaptcha-container {
		/* Optional: if you need specific styling for the captcha box */
		display: flex;
		justify-content: center;
	}
</style>
