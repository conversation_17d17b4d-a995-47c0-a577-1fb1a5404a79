<script lang="ts">
	// Replace with your actual Mapbox access token
	const MAPBOX_ACCESS_TOKEN =
		'pk.eyJ1IjoiYXlvbWlwb2UiLCJhIjoiY21hamM1NnVqMGF1YTJqcXpxamVlNmlwcCJ9.pkZosjS4Y052msNhBJgbOA';

	// Replace with the precise coordinates for your address
	const YOUR_LONGITUDE = -0.746933; // Example: Milton Keynes general longitude
	const YOUR_LATITUDE = 52.037013; // Example: Milton Keynes general latitude

	const mapZoom = 16; // Adjust zoom level as needed (e.g., 15-17 for a specific building)
	const mapWidth = 800; // Width of the map image in pixels
	const mapHeight = 400; // Height of the map image in pixels
	const mapBearing = 0; // Optional: 0-360
	const mapPitch = 0; // Optional: 0-60

	// Mapbox style - you can use your custom style or a default Mapbox style
	const mapboxUsername = 'mapbox'; // Your Mapbox username or 'mapbox' for default styles
	const mapStyleId = 'streets-v12'; // e.g., streets-v12, light-v11, satellite-v9

	// Marker details (optional)
	// Using a standard Mapbox Maki icon 'building' with a purple color (#6A0DAD, same as #5D28B6 is close)
	// Format: pin-{size}-{symbol}+{color}({lon},{lat})
	// You can also use custom markers: url-{encoded_url}({lon},{lat})
	const markerLon = YOUR_LONGITUDE;
	const markerLat = YOUR_LATITUDE;
	const markerName = 'pin-l-building+6A0DAD'; // Large pin, building icon, purple color
	const overlay = `${markerName}(${markerLon},${markerLat})`;

	// Construct the Mapbox Static Image API URL
	// GET https://api.mapbox.com/styles/v1/{username}/{style_id}/static/{overlay}/{lon},{lat},{zoom},{bearing},{pitch}|{bbox}|{auto}/{width}x{height}{@2x}
	let staticMapUrl = $derived(
		`https://api.mapbox.com/styles/v1/${mapboxUsername}/${mapStyleId}/static/${overlay}/${YOUR_LONGITUDE},${YOUR_LATITUDE},${mapZoom},${mapBearing},${mapPitch}/${mapWidth}x${mapHeight}@2x?access_token=${MAPBOX_ACCESS_TOKEN}&attribution=false&logo=false`
	);
	// Added &attribution=false&logo=false to hide default Mapbox attributions on the image itself.
	// Remember: If attribution=false, you are legally required to include proper attribution elsewhere on the webpage or document.
</script>

<section id="our-location" class="bg-gray-50 py-12 md:py-20">
	<div class="container mx-auto px-4 sm:px-6 lg:px-8">
		<h2
			class="mb-10 text-center text-3xl font-bold tracking-tight text-gray-800 sm:text-4xl md:mb-12"
		>
			Our Location
		</h2>
		<div class="overflow-hidden rounded-lg shadow-lg">
			<img
				src={staticMapUrl}
				alt="Map showing location of Central Staffing at Midsummer Boulevard, Milton Keynes"
				class="h-auto w-full object-cover"
				width={mapWidth}
				height={mapHeight}
			/>
		</div>
		<div class="mt-6 text-center text-gray-600">
			<p>Suit 2, Margareth Powell House, 401 - 447, Midsummer Boulevard, Milton Keynes, MK9 3BN</p>

			<p class="mt-4 text-xs">
				© <a
					href="https://www.mapbox.com/about/maps/"
					class="underline hover:text-purple-600"
					target="_blank"
					rel="noopener noreferrer">Mapbox</a
				>
				©
				<a
					href="https://www.openstreetmap.org/copyright"
					class="underline hover:text-purple-600"
					target="_blank"
					rel="noopener noreferrer">OpenStreetMap</a
				>
				Improve this map
			</p>
		</div>
	</div>
</section>

<style>
	/* Add any specific styles for this section if needed */
	/* For example, ensuring the image container has a max-width or specific aspect ratio */
	/* The Tailwind classes already provide good defaults for responsiveness */
	#our-location img {
		border: 1px solid #e2e8f0; /* Example subtle border */
	}
</style>
