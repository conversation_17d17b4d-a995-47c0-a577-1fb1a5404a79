<script lang="ts">
	import { page } from '$app/state';
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import SuccessModal from '../ongoing-jobs/SuccessModal.svelte';

	import ContactForm from './ContactForm.svelte';
	import GenericContact from './GenericContact.svelte';
	import MapBox from './MapBox.svelte';
	const h1 = `Let’s Connect, Your<br /> Next Great Hire /<br /> Career Opportunity <br /> Begin With US`;

	const p =
		"Whether you're looking to fill critical roles or take the next step in your career, our team is ready to support you with trusted recruitment solutions.";

	let showSpecialModal = $state(false);

	let pageHash = $derived(page.url.hash);

	const pageHashToModalKeys = {
		'#feedback': 'sendFeedback',
		'#join-us': 'joinUs',
		'#callback': 'requestCallBack',
		'#candidate-registration': 'candidateRegistration',
		'#refer-a-friend': 'referFriend',
		'#job-application': 'jobApplication'
	};

	let currentModal:
		| 'none'
		| 'jobApplication'
		| 'referFriend'
		| 'sendFeedback'
		| 'joinUs'
		| 'requestCallBack'
		| 'candidateRegistration' = $state('none');
	let successModalOpen = $state(false);
	let successMessage = $state('');
	let successTitle = $state('');
	let selectedJobTitle = $state('');

	$effect(() => {
		if (pageHash) {
			currentModal = pageHashToModalKeys[pageHash] || 'none';
		}
	});

	function closeModal() {
		currentModal = 'none';
		window.location.hash = '/contact';
	}
	// This function is now the callback for GenericContact
	async function handleApiFormResponse(response: {
		success: boolean;
		message?: string;
		messageId?: string;
		error?: any;
	}) {
		if (response.success) {
			currentModal = 'none'; // GenericContact will show its own success and can be closed by user.
			// Or, if you want parent to control:
			successTitle = 'Submission Successful!';
			successMessage =
				response.message ||
				`Your ${formConfigsForParent[currentModal]?.title || 'form'} has been sent.`;
			successModalOpen = true; // If you still want a separate success modal

			// The GenericContact component will display its own success message.
			// You might want to close the modal after a delay or let the user close it.
			// setTimeout(() => {
			// if (currentModal !== 'none' && !response.error) closeModal(); // closeModal sets currentModal to 'none'
			// }, 3000); // Example: close after 3 seconds
		} else {
			console.error(
				`Form type ${currentModal} submission failed:`,
				response.error || response.message
			);
			// Error is displayed within GenericContact.svelte
			// Optionally, show a generic error in a parent modal/toast if desired.
		}
	}

	// To be able to access formConfigs from parent to customize success message
	// This is a bit of a workaround as formConfigs is internal to FormModal.
	// A cleaner way might be for FormModal to emit more specific success details.
	const formConfigsForParent: Record<string, { title: string }> = {
		jobApplication: { title: 'Job Application' },
		referFriend: { title: 'Referral' },
		sendFeedback: { title: 'Feedback' },
		joinUs: { title: 'Join Us Form' },
		requestCallBack: { title: 'Call Back Request' },
		candidateRegistration: { title: 'Candidate Registration' }
	};
</script>

{#snippet Image()}
	<img
		src="/contact/01.png"
		alt="Healthcare worker assisting an elderly patient"
		class="special h-full w-full object-cover object-center"
	/>
{/snippet}

<PoorCarousel
	showSecond={false}
	ctaLink="/contact#join-us"
	ctaText="Join us Now"
	image={Image}
	baseClass="bg-[#FFFAFF]"
	{h1}
	{p}
/>

<ContactForm />

<GenericContact
	open={currentModal !== 'none'}
	formType={currentModal === 'none' ? 'sendFeedback' : currentModal}
	jobTitle={selectedJobTitle}
	onSubmit={handleApiFormResponse}
	onClose={closeModal}
/>
<MapBox />
<Subscribe />

{#if successModalOpen}
	<SuccessModal
		text={successMessage}
		open={successModalOpen}
		onClose={() => {
			successModalOpen = false;
			closeModal();
		}}
	/>
{/if}
<svelte:head>
	<title>Contact Us | Central Staffing | Milton Keynes Office</title>
	<meta
		name="description"
		content="Get in touch with Central Staffing. Find our Milton Keynes office address, phone number, and email, or use our contact form to request a callback or send a message."
	/>
	<meta
		name="keywords"
		content="Contact Central Staffing, Central Staffing address, staffing agency Milton Keynes, request a callback, healthcare recruitment contact, industrial staffing contact"
	/>
</svelte:head>

<style>
	.special {
		z-index: -20;
		border: solid 1px#FFFAFF;
		border-radius: 20px;
	}
</style>
