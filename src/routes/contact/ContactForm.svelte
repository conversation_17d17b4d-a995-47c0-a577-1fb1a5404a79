<script lang="ts">
	import { getContext, type Snippet } from 'svelte';
	import SuccessModal from '../ongoing-jobs/SuccessModal.svelte';
	import HCaptcha from '../admin/auth/HCaptcha.svelte';
	import type { ExternalLinksConfig } from '../../app';

	let modalShow = $state(false); // For your existing success modal
	let isSubmitting = $state(false);
	let formApiError = $state<string | undefined>(undefined);
	let formApiSuccess = $state<string | undefined>(undefined); // For inline success/error

	let passedCaptcha = $state(false);
	let passToken = $state<string | undefined>(undefined);
	let hcaptchaRef = $state(); // For HCaptcha component instance
	let formRef = $state<HTMLFormElement>();

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;

	const PUBLIC_HCAPTCHA_SITE_KEY = import.meta.env.VITE_PUBLIC_HCAPTCHA_SITE_KEY;

	async function handleSubmit(event: SubmitEvent) {
		event.preventDefault();
		formApiError = undefined;
		formApiSuccess = undefined;

		if (!passedCaptcha || !passToken) {
			formApiError = 'Please complete the CAPTCHA.';
			return;
		}

		isSubmitting = true;
		const formElement = event.target as HTMLFormElement;
		const formData = new FormData(formElement);

		formData.append('formType', 'contactMessage');
		formData.append('passToken', passToken);

		// Basic client-side validation (optional, server should always validate)
		const foreName = formData.get('foreName');
		if (!foreName) {
			formApiError = 'Forename is required.';
			isSubmitting = false;
			return;
		}
		// Add more client-side checks if desired

		try {
			const response = await fetch('/api/v1/communication', {
				method: 'POST',
				body: formData
			});
			const result = await response.json();

			if (response.ok && result.success) {
				formApiSuccess = result.message || 'Message sent successfully!';
				// modalShow = true; // Show your existing success modal
				formRef?.reset(); // Reset the form fields
				passedCaptcha = false; // Reset captcha state
				// hcaptchaRef?.reset(); // Call reset on HCaptcha component if available
			} else {
				formApiError = result.message || 'Failed to send message. Please try again.';
			}
		} catch (error) {
			console.error('Contact form submission error:', error);
			formApiError = 'A network error occurred. Please try again.';
		} finally {
			isSubmitting = false;
			// Consider resetting captcha token here as well if it's single-use
			// passedCaptcha = false; // if not reset on success already
			// passToken = undefined;
		}
	}
	const uid = () => Math.random().toString(36).substring(2);
</script>

{#snippet SubmitButton()}
	<button
		type="submit"
		class="mt-6 inline-flex w-full items-center justify-center rounded-full border border-transparent bg-[#5D28B6] px-6 py-3 text-base font-medium text-white shadow-sm transition duration-200 hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:outline-none sm:w-auto"
	>
		Request A Call Back
		<span class="ml-2 inline-flex items-center justify-center rounded-full bg-white p-0.5">
			<svg
				width="24"
				height="24"
				viewBox="0 0 33 33"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path
					d="M10 24L23 11M23 11V23.48M23 11H10.52"
					stroke="#5D28B6"
					stroke-width="2.5"
					stroke-linecap="round"
					stroke-linejoin="round"
				/>
			</svg>
		</span>
	</button>
{/snippet}

{#snippet ContactMessageForm(props: ContactFormSnippetProps = {})}
	<form
		bind:this={formRef}
		class="space-y-4 bg-white p-4 shadow-xs md:space-y-5"
		onsubmit={props.onSubmit || (() => {})}
	>
		{#if formApiError}
			<div
				role="alert"
				style="color: red; background-color: #fdd; padding: 10px; border-radius: 4px; margin-bottom:15px; text-align:center;"
			>
				{formApiError}
			</div>
		{/if}
		{#if formApiSuccess}
			<div
				role="alert"
				style="color: green; background-color: #dfd; padding: 10px; border-radius: 4px; margin-bottom:15px; text-align:center;"
			>
				{formApiSuccess}
			</div>
		{/if}
		<div>
			<label for="foreName_{uid()}" class="mb-1 block text-sm font-medium text-gray-700"
				>Forename*</label
			>
			<input
				type="text"
				name="foreName"
				id="foreName_{uid()}"
				class="block w-full border-0 border-b border-gray-300 bg-transparent p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-0 focus:outline-none"
				required
				disabled={isSubmitting}
			/>
		</div>
		<div>
			<label for="surname_{uid()}" class="mb-1 block text-sm font-medium text-gray-700"
				>Surname*</label
			>
			<input
				type="text"
				name="surname"
				id="surname_{uid()}"
				class="block w-full border-0 border-b border-gray-300 bg-transparent p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-0 focus:outline-none"
				required
				disabled={isSubmitting}
			/>
		</div>
		<div>
			<label for="companyName_{uid()}" class="mb-1 block text-sm font-medium text-gray-700"
				>Company Name*</label
			>
			<input
				type="text"
				name="companyName"
				id="companyName_{uid()}"
				class="block w-full border-0 border-b border-gray-300 bg-transparent p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-0 focus:outline-none"
				required
				disabled={isSubmitting}
			/>
		</div>
		<div>
			<label for="position_{uid()}" class="mb-1 block text-sm font-medium text-gray-700"
				>Position*</label
			>
			<input
				type="text"
				name="position"
				id="position_{uid()}"
				class="block w-full border-0 border-b border-gray-300 bg-transparent p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-0 focus:outline-none"
				required
				disabled={isSubmitting}
			/>
		</div>
		<div>
			<label for="emailAddress_{uid()}" class="mb-1 block text-sm font-medium text-gray-700"
				>Email Address*</label
			>
			<input
				type="email"
				name="emailAddress"
				id="emailAddress_{uid()}"
				class="block w-full border-0 border-b border-gray-300 bg-transparent p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-0 focus:outline-none"
				required
				disabled={isSubmitting}
			/>
		</div>
		<div>
			<label for="contactNumber_{uid()}" class="mb-1 block text-sm font-medium text-gray-700"
				>Contact Number*</label
			>
			<input
				type="tel"
				name="contactNumber"
				id="contactNumber_{uid()}"
				class="block w-full border-0 border-b border-gray-300 bg-transparent p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-0 focus:outline-none"
				required
				disabled={isSubmitting}
			/>
		</div>
		<div>
			<label for="detailsOfRequest_{uid()}" class="mb-1 block text-sm font-medium text-gray-700"
				>Details of Request*</label
			>
			<textarea
				name="detailsOfRequest"
				id="detailsOfRequest_{uid()}"
				rows="4"
				class="block w-full resize-none rounded-md border border-gray-300 bg-transparent p-2.5 text-sm text-gray-900 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 focus:outline-none"
				required
				disabled={isSubmitting}
			></textarea>
		</div>

		<div class="pt-2">
			<p class="text-xs text-gray-500">
				By completing and submitting this form, you are granting implied consent to Central Staffing
				for the processing of information for purpose of your enquiry and will not be shared with
				third parties without consent.
				<a href={externalLinks.privacyPolicy?.url} class="text-purple-600 hover:underline"
					>Privacy Policy</a
				>
				and
				<a href={externalLinks.termsOfUse?.url} class="text-purple-600 hover:underline"
					>Terms of Use</a
				>
			</p>
		</div>

		<div class="flex items-center pt-1">
			<div class="flex h-5 items-center">
				<input
					id="terms_{uid()}"
					name="terms"
					aria-describedby="terms-description"
					type="checkbox"
					class="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-2 focus:ring-purple-500"
					required
					disabled={isSubmitting}
				/>
			</div>
			<div class="ml-3 text-sm">
				<label for="terms_{uid()}" class="font-medium text-gray-700">*I understand and agree</label>
			</div>
		</div>
		<div style="display: flex; justify-content: center; margin-top: 1rem; margin-bottom: 0.5rem;">
			<HCaptcha
				bind:this={hcaptchaRef}
				sitekey={PUBLIC_HCAPTCHA_SITE_KEY}
				on:success={(e) => {
					passToken = e.detail.token;
					passedCaptcha = true;
					if (formApiError === 'Please complete the CAPTCHA.') formApiError = undefined;
				}}
				on:error={(e) => {
					console.error('HCaptcha error event:', e.detail);
					passToken = undefined;
					passedCaptcha = false;
					formApiError = e.detail?.error || 'CAPTCHA verification failed.';
				}}
				on:expired={() => {
					passToken = undefined;
					passedCaptcha = false;
					formApiError = 'CAPTCHA token expired. Please try again.';
				}}
			/>
		</div>
		{@render SubmitButton()}
	</form>
{/snippet}

<section class="bg-[#FBF9FF] py-12 md:py-20">
	<div class="container mx-auto px-4 sm:px-6 lg:px-8">
		<div class="grid grid-cols-1 items-start gap-x-4 gap-y-10 lg:grid-cols-2">
			<div class="space-y-8 lg:h-full">
				<div class="w-full overflow-hidden rounded-lg">
					<img
						src="/contact/02.png"
						alt="Contact us illustration"
						class="h-auto max-h-[627px] w-full max-w-[500px] object-cover"
					/>
				</div>
				<div class="space-y-6 text-[#04091B]">
					<h2 class="text-2xl font-bold text-gray-800">Get In Touch</h2>
					<div class="space-y-4">
						<div class="flex items-start">
							<div class="flex-shrink-0">
								<span
									class="inline-flex h-10 w-10 items-center justify-center rounded-full bg-purple-100 text-purple-600"
								>
									<svg
										width="60"
										height="60"
										viewBox="0 0 60 60"
										fill="none"
										xmlns="http://www.w3.org/2000/svg"
									>
										<circle cx="30" cy="30" r="30" fill="#5D28B6" />
										<path
											d="M29.9987 14.1667C23.032 14.1667 17.332 19.8667 17.332 26.8334C17.332 35.3834 28.4154 45.0418 28.8904 45.5168C29.207 45.6751 29.682 45.8334 29.9987 45.8334C30.3154 45.8334 30.7904 45.6751 31.107 45.5168C31.582 45.0418 42.6654 35.3834 42.6654 26.8334C42.6654 19.8667 36.9654 14.1667 29.9987 14.1667ZM29.9987 42.1918C26.6737 39.0251 20.4987 32.2168 20.4987 26.8334C20.4987 21.6084 24.7737 17.3334 29.9987 17.3334C35.2237 17.3334 39.4987 21.6084 39.4987 26.8334C39.4987 32.0584 33.3237 39.0251 29.9987 42.1918ZM29.9987 20.5001C26.5154 20.5001 23.6654 23.3501 23.6654 26.8334C23.6654 30.3168 26.5154 33.1668 29.9987 33.1668C33.482 33.1668 36.332 30.3168 36.332 26.8334C36.332 23.3501 33.482 20.5001 29.9987 20.5001ZM29.9987 30.0001C28.257 30.0001 26.832 28.5751 26.832 26.8334C26.832 25.0918 28.257 23.6667 29.9987 23.6667C31.7404 23.6667 33.1654 25.0918 33.1654 26.8334C33.1654 28.5751 31.7404 30.0001 29.9987 30.0001Z"
											fill="white"
										/>
									</svg>
								</span>
							</div>
							<div class="ml-4">
								<h3 class="text-lg font-medium text-gray-800">Address</h3>
								<p class="text-base">
									Suit 2, Margareth Powell House, 401 - 447, Midsummer Boulevard, Milton Keynes, MK9
									3BN.
								</p>
							</div>
						</div>
						<div class="flex items-start">
							<div class="flex-shrink-0">
								<span
									class="inline-flex h-10 w-10 items-center justify-center rounded-full bg-purple-100 text-purple-600"
								>
									<svg
										width="60"
										height="60"
										viewBox="0 0 60 60"
										fill="none"
										xmlns="http://www.w3.org/2000/svg"
									>
										<circle cx="30" cy="30" r="30" fill="#5D28B6" />
										<path
											d="M21.355 18.9167C21.45 20.3258 21.6875 21.7033 22.0675 23.0175L20.1675 24.9175C19.5183 23.0175 19.1067 21.0067 18.9642 18.9167H21.355ZM36.9667 37.9483C38.3125 38.3283 39.69 38.5658 41.0833 38.6608V41.02C38.9933 40.8775 36.9825 40.4658 35.0667 39.8325L36.9667 37.9483ZM22.875 15.75H17.3333C16.4625 15.75 15.75 16.4625 15.75 17.3333C15.75 32.2008 27.7992 44.25 42.6667 44.25C43.5375 44.25 44.25 43.5375 44.25 42.6667V37.1408C44.25 36.27 43.5375 35.5575 42.6667 35.5575C40.7033 35.5575 38.7875 35.2408 37.0142 34.655C36.8574 34.5964 36.6906 34.5695 36.5233 34.5758C36.1117 34.5758 35.7158 34.7342 35.3992 35.035L31.9158 38.5183C27.4278 36.223 23.777 32.5722 21.4817 28.0842L24.965 24.6008C25.4083 24.1575 25.535 23.54 25.3608 22.9858C24.7613 21.1617 24.4566 19.2535 24.4583 17.3333C24.4583 16.4625 23.7458 15.75 22.875 15.75Z"
											fill="white"
										/>
									</svg>
								</span>
							</div>
							<div class="ml-4">
								<h3 class="text-lg font-medium text-gray-800">Phone Number</h3>
								<p class="text-base">01908915205, Out of hours call: 07482272158</p>
							</div>
						</div>
						<div class="flex items-start">
							<div class="flex-shrink-0">
								<span
									class="inline-flex h-10 w-10 items-center justify-center rounded-full bg-purple-100 text-purple-600"
								>
									<svg
										width="60"
										height="60"
										viewBox="0 0 60 60"
										fill="none"
										xmlns="http://www.w3.org/2000/svg"
									>
										<circle cx="30" cy="30" r="30" fill="#5D28B6" />
										<path
											d="M45.8346 20.4999C45.8346 18.7583 44.4096 17.3333 42.668 17.3333H17.3346C15.593 17.3333 14.168 18.7583 14.168 20.4999V39.4999C14.168 41.2416 15.593 42.6666 17.3346 42.6666H42.668C44.4096 42.6666 45.8346 41.2416 45.8346 39.4999V20.4999ZM42.668 20.4999L30.0013 28.4166L17.3346 20.4999H42.668ZM42.668 39.4999H17.3346V23.6666L30.0013 31.5833L42.668 23.6666V39.4999Z"
											fill="white"
										/>
									</svg>
								</span>
							</div>
							<div class="ml-4">
								<h3 class="text-lg font-medium text-gray-800">Email</h3>
								<p class="text-base"><EMAIL></p>
							</div>
						</div>
					</div>
					<div class="pt-4">
						<h3 class="mb-2 text-lg font-medium text-gray-800">Follow Us</h3>
						<div class="flex space-x-3">
							<a
								href="https://twitter.com/StaffingCentral"
								aria-label="X social media link"
								class="text-purple-600 hover:text-purple-800"
							>
								<svg
									width="52"
									height="52"
									viewBox="0 0 52 52"
									fill="none"
									xmlns="http://www.w3.org/2000/svg"
								>
									<circle cx="26" cy="26" r="26" fill="#5D28B6" />
									<g clip-path="url(#clip0_638_299)">
										<mask
											id="mask0_638_299"
											style="mask-type:luminance"
											maskUnits="userSpaceOnUse"
											x="14"
											y="14"
											width="24"
											height="24"
										>
											<path d="M14 14H38V38H14V14Z" fill="white" />
										</mask>
										<g mask="url(#mask0_638_299)">
											<path
												d="M32.9 15.1245H36.5806L28.5406 24.3371L38 36.8754H30.5943L24.7897 29.2725L18.1554 36.8754H14.4714L23.0703 27.0182L14 15.1262H21.5943L26.8331 22.0742L32.9 15.1245ZM31.6057 34.6674H33.6457L20.48 17.2177H18.2926L31.6057 34.6674Z"
												fill="white"
											/>
										</g>
									</g>
									<defs>
										<clipPath id="clip0_638_299">
											<rect width="24" height="24" fill="white" transform="translate(14 14)" />
										</clipPath>
									</defs>
								</svg>
							</a>
							<a
								href="https://www.facebook.com/Centralstaffinglimited/"
								aria-label="Facebook social media link"
								class="text-purple-600 hover:text-purple-800"
							>
								<svg
									width="52"
									height="52"
									viewBox="0 0 52 52"
									fill="none"
									xmlns="http://www.w3.org/2000/svg"
								>
									<circle cx="26" cy="26" r="26" fill="#5D28B6" />
									<path
										d="M28.5 27.875H31.625L32.875 22.875H28.5V20.375C28.5 19.0875 28.5 17.875 31 17.875H32.875V13.675C32.4675 13.6213 30.9287 13.5 29.3037 13.5C25.91 13.5 23.5 15.5713 23.5 19.375V22.875H19.75V27.875H23.5V38.5H28.5V27.875Z"
										fill="white"
									/>
								</svg>
							</a>
						</div>
					</div>
				</div>
			</div>

			<div class="bg-white p-8 lg:h-full">
				<h2 class="mb-6 text-2xl font-bold text-gray-800">Send a Message</h2>
				{@render ContactMessageForm({ onSubmit: handleSubmit })}
			</div>
		</div>
	</div>
</section>

<!-- <SuccessModal
	text="Message Sent"
	open={modalShow}
	onClose={() => {
		modalShow = false;
		window.location.href = '/';
	}}
/> -->

{#if modalShow && !formApiError}
	<SuccessModal
		text={formApiSuccess || 'Message Sent Successfully!'}
		open={modalShow}
		onClose={() => {
			modalShow = false;
			window.location.href = '/';
		}}
	/>
{/if}
