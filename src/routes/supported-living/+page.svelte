<script lang="ts">
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ServiceSplitScreen from '$lib/components/shared/ServiceSplitScreen.svelte';
	import Subscribe from '$lib/components/home/<USER>';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';

	const h1 = 'SUPPORTED LIVING';
	const p =
		'Your Life, Your Home, Your Support - Empowered Living for Adults. We enable adults and young people with diverse needs to live independently.';

	const coreServices = [
		{
			title: 'Tenancy Management',
			description: 'Assistance with tenancy agreements and liaising with landlords.'
		},
		{
			title: 'Budgeting & Bills',
			description: 'Help with managing money, benefits, and accessing financial entitlements.'
		},
		{
			title: 'Household Tasks',
			description: 'Support with cleaning, laundry, and maintaining home safety.'
		},
		{
			title: 'Accessing Health Services',
			description: 'Help with GP visits, dentist appointments, and prescription management.'
		},
		{
			title: 'Social & Recreational Activities',
			description: 'Encouragement and support to engage in social and fun activities.'
		},
		{
			title: 'Developing Life Skills',
			description: 'Guidance on travel, communication, and personal safety to build confidence.'
		}
	];

	const whoWeSupportList: ListItem[] = [
		{ text: 'Adults (18+) with learning disabilities or autism' },
		{ text: 'People with mental health needs' },
		{ text: 'Those with physical or sensory impairments' },
		{ text: 'Individuals transitioning from hospital, residential care, or family homes' },
		{ text: 'People at risk of homelessness or social isolation' }
	];

	const processSteps = [
		{
			step: 1,
			title: 'Referral & Enquiry',
			description: 'We receive referrals from families, social workers, or other professionals.'
		},
		{
			step: 2,
			title: 'Assessment',
			description:
				'We review individual needs, risks, goals, and support preferences collaboratively.'
		},
		{
			step: 3,
			title: 'Housing & Transition',
			description:
				'We assist in identifying a suitable property and coordinate a smooth transition.'
		},
		{
			step: 4,
			title: 'Ongoing Support & Reviews',
			description:
				'We provide regular check-ins and reviews to adapt the support plan as needs evolve.'
		}
	];
</script>

<svelte:head>
	<title>Supported Living Services | Centred Healthcare Living</title>
	<meta
		name="description"
		content="Empowering adults with disabilities and complex needs to live independently with our tailored supported living services."
	/>
</svelte:head>

{#snippet HeroImage()}
	<img
		src="/services/04.avif"
		alt="A person enjoying their independent living space"
		class="h-full w-full object-cover"
	/>
{/snippet}
<PoorCarousel
	{h1}
	{p}
	image={HeroImage}
	ctaText="Make an Enquiry"
	ctaLink="/contact#callback"
	showSecond={false}
	baseClass="bg-white"
/>

<section id="services" class="bg-gray-50 py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 text-center">
			<h2 class="text-3xl font-bold text-[#0586AB]">Our Support Services</h2>
		</div>
		<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
			{#each coreServices as service}
				<div
					class="flex flex-col rounded-lg bg-white p-6 shadow-md transition-shadow hover:shadow-xl"
				>
					<h3 class="mb-2 text-xl font-semibold">{service.title}</h3>
					<p class="flex-grow text-sm text-gray-600">{service.description}</p>
				</div>
			{/each}
		</div>
	</div>
</section>

{#snippet WhoWeSupportImage()}
	<div class="h-full w-full">
		<img
			src="/fold/13.jpg"
			alt="A diverse community of people"
			class="h-full w-full rounded-lg object-cover shadow-lg"
		/>
	</div>
{/snippet}
<ServiceSplitScreen
	topLabel="WHO WE SUPPORT"
	mainHeading="Empowering a Wide Range of Individuals"
	listItems={whoWeSupportList}
	imagePosition="left"
	imageSnippet={WhoWeSupportImage}
	baseClass="bg-white"
/>

<section id="process" class="bg-gray-50 py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 text-center">
			<h2 class="text-3xl font-bold text-[#0586AB]">Our Process</h2>
		</div>
		<div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
			{#each processSteps as item}
				<div class="text-center">
					<div
						class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-teal-100 text-3xl font-bold text-teal-700"
					>
						{item.step}
					</div>
					<h3 class="mb-2 text-lg font-semibold">{item.title}</h3>
					<p class="text-sm text-gray-600">{item.description}</p>
				</div>
			{/each}
		</div>
	</div>
</section>

<Subscribe />
