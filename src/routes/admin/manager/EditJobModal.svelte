<script lang="ts">
	import type { FormData } from '../../api/v1/jobs/types';

	// // incase of resets
	// type FormData = {
	// 	_id: string;
	// 	title: string;
	// 	companyName: string;
	// 	companyDesc?: string;
	// 	jobType: 'on-site' | 'remote' | 'hybrid';
	// 	jobDesc: string;
	// 	jobField: 'medical' | 'care' | 'industrial' | 'unspecified';
	// 	jobLocation: string;
	// 	skills: string;
	// 	salary: string;
	// 	deadline: string;
	// };

	let {
		isFetching = $bindable(false),
		itemToEdit = $bindable()
	}: { isFetching: boolean; itemToEdit: FormData } = $props();

	let formRef: HTMLFormElement | undefined = $state();
	let closeButton: HTMLButtonElement | undefined = $state();
	let errorMessage = $state('');

	const createJob = async () => {
		if (isFetching || !itemToEdit) {
			return;
		}
		try {
			const response = await fetch(`/api/v1/manager/jobs/${itemToEdit._id}`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(itemToEdit)
			});

			if (response.ok) {
				closeButton?.click();
				window.location.reload();
			} else {
				const responseBody = await response.json();
				errorMessage = responseBody.error;
			}
		} catch {
			errorMessage = 'Something went wrong';
		} finally {
			isFetching = false;
		}
	};

	const onSubmit = async () => {
		if (formRef?.checkValidity()) {
			createJob();
			isFetching = true;
		} else {
			formRef?.reportValidity();
			isFetching = false;
		}
	};
</script>

<div
	class="modal fade"
	id="editJobModal"
	tabindex="-1"
	aria-labelledby="editJobModalLabel"
	aria-hidden="true"
>
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<p>{errorMessage}</p>
				<h5 class="modal-title" id="addJobModalLabel">Edit Job Listing</h5>
				<button
					type="button"
					class="btn-close"
					bind:this={closeButton}
					data-bs-dismiss="modal"
					aria-label="Close"
				></button>
			</div>
			<div class="modal-body">
				<form bind:this={formRef}>
					<div class="row mb-3">
						<div class="col-md-6">
							<label for="jobTitle" class="form-label">Job Title</label>
							<input
								minlength="5"
								type="text"
								class="form-control"
								id="jobTitle"
								placeholder="e.g. Care Worker"
								required
								disabled={isFetching}
								bind:value={itemToEdit.title}
							/>
						</div>
						<div class="col-md-6">
							<label for="company" class="form-label">Company</label>
							<input
								type="text"
								class="form-control"
								id="company"
								placeholder="e.g. Window United"
								required
								disabled={isFetching}
								bind:value={itemToEdit.companyName}
							/>
						</div>
					</div>

					<div class="row mb-3">
						<div class="col-md-6">
							<label for="jobType" class="form-label">Job Type</label>
							<select
								disabled={isFetching}
								class="form-select"
								id="jobType"
								required
								bind:value={itemToEdit.jobType}
							>
								<option value="" selected disabled>Select job type</option>
								<option value="on-site">On Site</option>
								<option value="remote">Remote</option>
								<option value="hybrid">Hybrid</option>
							</select>
						</div>

						<div class="col-md-6">
							<label for="jobField" class="form-label">Job Field</label>
							<select
								disabled={isFetching}
								class="form-select"
								id="jobField"
								required
								bind:value={itemToEdit.jobField}
							>
								<option value="" selected disabled>Select job type</option>

								<option value="medical">Medical</option>
								<option value="care">Care</option>
								<option value="industrial">Industrial</option>
								<option value="unspecified">Unspecified</option>
							</select>
						</div>
					</div>

					<div class="row mb-3">
						<div class="col-md-4">
							<label for="location" class="form-label">Job Location</label>
							<input
								type="text"
								class="form-control"
								id="location"
								placeholder="e.g. London, Uk"
								disabled={isFetching}
								required
								bind:value={itemToEdit.jobLocation}
							/>
						</div>
						<div class="col-md-4">
							<label for="salary" class="form-label">Salary</label>
							<input
								type="text"
								class="form-control"
								id="salary"
								placeholder="e.g. $20/hr"
								disabled={isFetching}
								required
								bind:value={itemToEdit.salary}
							/>
						</div>
						<div class="col-md-4">
							<label for="deadline" class="form-label">Deadline</label>
							<input
								type="date"
								class="form-control"
								id="deadline"
								disabled={isFetching}
								required
								bind:value={itemToEdit.deadline}
							/>
						</div>
					</div>

					<div class="mb-3">
						<label for="description" class="form-label">Job Description</label>
						<textarea
							disabled={isFetching}
							class="form-control"
							id="description"
							rows="5"
							placeholder="Enter detailed job description..."
							required
							bind:value={itemToEdit.jobDesc}
						></textarea>
					</div>

					<div class="mb-3">
						<label for="companyDesc" class="form-label">Company Description</label>
						<textarea
							disabled={isFetching}
							class="form-control"
							id="companyDesc"
							rows="3"
							placeholder="Enter company description"
							required
							bind:value={itemToEdit.companyDesc}
						></textarea>
					</div>

					<div class="mb-3">
						<label for="skills" class="form-label">Job skills</label>
						<textarea
							disabled={isFetching}
							class="form-control"
							id="skills"
							rows="3"
							placeholder="Enter job skills"
							required
							bind:value={itemToEdit.skills}
						></textarea>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button
					type="button"
					disabled={isFetching}
					class="btn btn-secondary"
					data-bs-dismiss="modal"
				>
					Cancel
				</button>
				<button onclick={onSubmit} disabled={isFetching} type="submit" class="btn btn-primary"
					>Save Edit</button
				>
			</div>
		</div>
	</div>
</div>
