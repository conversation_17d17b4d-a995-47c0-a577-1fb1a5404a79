<script lang="ts">
	import { goto } from '$app/navigation';
	import AddJobModal from './AddJobModal.svelte';
	import DataTable from './DataTable.svelte';
	import { page } from '$app/state';
	import { watch } from 'runed';
	import ManageExternalLinks from './ManageExternalLinks.svelte';

	let isFetching = $state(false);
	let { data } = $props(); // This data is for jobs view

	let currentView = $state<'jobs' | 'links'>('jobs');

	// ... (rest of your existing script for job filters and logic)
	let searchQuery = $state(page.url.searchParams.get('title') || '');
	let jobTypeFilter = $state(page.url.searchParams.get('jobType') || '');
	let jobFieldFilter = $state(page.url.searchParams.get('jobField') || '');

	const jobFields = ['medical', 'care', 'industrial', 'unspecified'];
	const jobTypes = ['on-site', 'remote', 'hybrid'];

	function debounce<F extends (...args: any[]) => any>(func: F, waitFor: number) {
		let timeout: ReturnType<typeof setTimeout> | null = null;
		const debounced = (...args: Parameters<F>) => {
			if (timeout !== null) {
				clearTimeout(timeout);
				timeout = null;
			}
			timeout = setTimeout(() => func(...args), waitFor);
		};
		return debounced as (...args: Parameters<F>) => void;
	}

	function updateFilters() {
		const params = new URLSearchParams(window.location.search);
		if (searchQuery) params.set('title', searchQuery);
		else params.delete('title');
		if (jobTypeFilter) params.set('jobType', jobTypeFilter);
		else params.delete('jobType');
		if (jobFieldFilter) params.set('jobField', jobFieldFilter);
		else params.delete('jobField');
		params.delete('page');
		const newQueryString = params.toString();
		if (newQueryString !== new URL(page.url).searchParams.toString()) {
			isFetching = true;
			goto(`?${newQueryString}`, { invalidateAll: true, noScroll: true, keepFocus: true });
		}
	}
	const debouncedUpdateFilters = debounce(updateFilters, 500);
	watch(() => [searchQuery, jobTypeFilter, jobFieldFilter], debouncedUpdateFilters);
	function resetAllFilters() {
		searchQuery = '';
		jobTypeFilter = '';
		jobFieldFilter = '';
	}

	let externalLinksData = $state({ items: [], message: '', error: null }); // State for external links
	let isLoadingLinks = $state(false);

	async function fetchExternalLinks() {
		if (currentView !== 'links') return;
		isLoadingLinks = true;
		externalLinksData.error = null;
		try {
			const response = await fetch('/api/v1/manager/external-links');
			const result = await response.json();
			if (response.ok && result.status === 'Success') {
				externalLinksData.items = result.data;
			} else {
				externalLinksData.items = [];
				externalLinksData.error = result.message || 'Failed to load links.';
			}
		} catch (err: any) {
			externalLinksData.items = [];
			externalLinksData.error = err.message || 'An error occurred.';
		} finally {
			isLoadingLinks = false;
		}
	}

	// Fetch links when the view changes to 'links'
	watch(
		() => currentView,
		() => {
			if (currentView === 'links') {
				fetchExternalLinks();
			}
		}
	);
</script>

<svelte:head>
	<link
		href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
		rel="stylesheet"
	/>
	<link
		rel="stylesheet"
		href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
	/>
	<script
		src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
	></script>
</svelte:head>

<AddJobModal bind:isFetching />
<div class="body w-full">
	<div class="container">
		{#if currentView === 'jobs' && data.error}
			<div class="alert alert-danger m-3" role="alert">{data.error}</div>
		{/if}
		{#if currentView === 'links' && externalLinksData.error}
			<div class="alert alert-danger m-3" role="alert">{externalLinksData.error}</div>
		{/if}

		<main class="ms-sm-auto col-md-12 main-content">
			<div
				class="d-flex justify-content-between flex-md-nowrap align-items-center border-bottom mb-3 flex-wrap pt-3 pb-2"
			>
				<h1 class="h2">
					{currentView === 'jobs' ? 'Job Listings Management' : 'External Links Management'}
				</h1>
				<div class="btn-toolbar mb-md-0 mb-2">
					{#if currentView === 'jobs'}
						<button
							class="btn btn-primary me-2"
							data-bs-toggle="modal"
							data-bs-target="#addJobModal"
						>
							<i class="fas fa-plus"></i> Add New Job
						</button>
					{/if}
					<button
						class="btn btn-info"
						onclick={() => (currentView = currentView === 'jobs' ? 'links' : 'jobs')}
					>
						<i class="fas {currentView === 'jobs' ? 'fa-link' : 'fa-briefcase'}"></i>
						{currentView === 'jobs' ? 'Manage Links' : 'View Jobs'}
					</button>
				</div>
			</div>

			{#if currentView === 'jobs'}
				<div class="card mb-4">
					<div class="card-header">
						<div class="row gy-2 gx-3 align-items-center">
							<div class="col-md-5">
								<div class="search-box">
									<i class="fas fa-search search-icon"></i>
									<input
										type="text"
										class="form-control"
										placeholder="Search by job title..."
										bind:value={searchQuery}
									/>
								</div>
							</div>
							<div class="col-md-3">
								<select
									class="form-select"
									bind:value={jobFieldFilter}
									aria-label="Filter by Job Field"
								>
									<option value="" selected disabled>Filter by Field</option>
									{#each jobFields as field (field)}<option class="text-capitalize" value={field}
											>{field}</option
										>{/each}
								</select>
							</div>
							<div class="col-md-3">
								<select
									class="form-select"
									bind:value={jobTypeFilter}
									aria-label="Filter by Job Type"
								>
									<option value="" selected disabled>Filter by Type</option>
									{#each jobTypes as type (type)}<option class="text-capitalize" value={type}
											>{type}</option
										>{/each}
								</select>
							</div>
							<div class="col-md-1 d-flex justify-content-end">
								<button
									class="btn btn-outline-secondary btn-sm"
									title="Reset Filters"
									onclick={resetAllFilters}><i class="fas fa-times"></i></button
								>
							</div>
						</div>
					</div>
				</div>
				<DataTable {data} />
			{:else if currentView === 'links'}
				<ManageExternalLinks
					bind:links={externalLinksData.items}
					onUpdate={fetchExternalLinks}
					isLoading={isLoadingLinks}
				/>
			{/if}
		</main>
	</div>
</div>

<style>
	:root {
		--primary-color: #3498db;
		--secondary-color: #2c3e50;
		--light-color: #ecf0f1;
		--dark-color: #34495e;
		--success-color: #2ecc71;
		--danger-color: #e74c3c;
		--warning-color: #f39c12;
		--placeholder-color: #6c757d; /* Bootstrap's placeholder color for form elements */
	}

	.body {
		font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
		background-color: #f5f7fa;
	}

	.main-content {
		padding: 20px;
	}

	.card {
		border-radius: 10px;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		margin-bottom: 20px;
		border: none;
	}

	.card-header {
		background-color: white;
		border-bottom: 1px solid #eee;
		/* font-weight: bold; */ /* Optional: Bootstrap might handle this */
		border-radius: 10px 10px 0 0 !important;
		padding: 1rem;
	}

	.btn-primary {
		background-color: var(--primary-color);
		border-color: var(--primary-color);
	}

	.btn-primary:hover {
		opacity: 0.9;
	}

	.search-box {
		position: relative;
	}

	.search-box .search-icon {
		position: absolute;
		top: 50%;
		left: 12px;
		transform: translateY(-50%);
		color: #aaa;
		pointer-events: none; /* So it doesn't interfere with input focus */
	}

	.search-box input.form-control {
		padding-left: 35px; /* Make space for the icon */
	}

	/* Styling for select placeholder */
	select.form-select {
		color: var(--bs-body-color); /* Default Bootstrap text color for selects */
	}

	select.form-select:invalid,
	select.form-select option[value=''] {
		/* color: var(--placeholder-color) !important; */ /* Style the select itself when placeholder is active */
	}
	/* This targets the select when its value is empty (our placeholder) */
	select.form-select:has(option[value='']:checked) {
		color: var(--placeholder-color);
	}

	/* Ensure options in dropdown always have normal color */
	select.form-select option {
		color: var(--bs-body-color);
	}
	/* Hide the disabled placeholder option from the dropdown list if desired, though not strictly necessary */
	/* select.form-select option[value=""][disabled] {
        display: none;
    } */

	.text-capitalize {
		text-transform: capitalize;
	}

	/* --- Pagination Overflow --- */
	/* Apply this to the direct container of your pagination buttons/links inside DataTable.svelte */
	/* Example:
       <div class="pagination-wrapper">
           <nav aria-label="Page navigation">
               <ul class="pagination"> ... </ul>
           </nav>
       </div>
    */
	:global(.pagination-wrapper) {
		/* Use :global if DataTable is a separate component */
		display: flex; /* Helps if pagination itself is a flex container */
		justify-content: center; /* Or start, end, depending on desired alignment */
		width: 100%;
		overflow-x: auto; /* Allows horizontal scrolling on smaller screens */
		padding-bottom: 8px; /* Space for scrollbar not to overlap content */
	}

	/* Ensure pagination items don't shrink too much, and let them scroll if they overflow */
	:global(.pagination-wrapper .pagination) {
		flex-shrink: 0; /* Prevents pagination items from shrinking if they are flex items */
		white-space: nowrap; /* If you want all pagination items on one line to scroll */
	}
	/* For Bootstrap pagination, you might want to ensure the ul.pagination itself can scroll if needed */
	:global(nav[aria-label='Page navigation']) {
		overflow-x: auto;
		padding-bottom: 5px; /* for scrollbar visibility */
	}
	:global(nav[aria-label='Page navigation'] ul.pagination) {
		white-space: nowrap; /* Keep pagination items in a single line */
	}
</style>
