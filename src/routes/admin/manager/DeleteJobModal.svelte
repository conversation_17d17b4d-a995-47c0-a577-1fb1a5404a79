<script lang="ts">
	import type { FormData } from '../../api/v1/jobs/types';

	let errorMessage = $state('');

	let {
		isDeleting = $bindable(false),
		itemToDeleteID = $bindable()
	}: { isDeleting: boolean; itemToDeleteID: FormData } = $props();

	const deleteJob = async () => {
		if (!isDeleting || !itemToDeleteID) {
			return;
		}
		try {
			const response = await fetch(`/api/v1/manager/jobs/${itemToDeleteID._id}`, {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(itemToDeleteID)
			});

			if (response.ok) {
				window.location.reload();
				isDeleting = false;
			} else {
				const responseBody = await response.json();
				errorMessage = responseBody.error;
			}
		} catch {
			errorMessage = 'Something went wrong';
		} finally {
			isDeleting = false;
		}
	};

	const onSubmit = async () => {
		if (itemToDeleteID) {
			isDeleting = true;
			deleteJob();
		} else {
			// formRef?.reportValidity();
			isDeleting = false;
		}
	};
</script>

<div
	class="modal fade"
	id="deleteJobModal"
	tabindex="-1"
	aria-labelledby="deleteJobModalLabel"
	aria-hidden="true"
>
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="deleteJobModalLabel">Confirm Deletion</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				{#if !errorMessage}
					<p>
						Are you sure you want to delete the job listing for
						<strong>{itemToDeleteID.title}</strong> at
						<strong>{itemToDeleteID.companyName}</strong>?
					</p>
					<p class="text-danger">
						This action cannot be undone and all applications for this job will also be deleted.
					</p>
				{:else}
					<p class="text-danger">{errorMessage}</p>
				{/if}
			</div>
			<div class="modal-footer">
				<button
					type="button"
					disabled={isDeleting}
					class="btn btn-secondary"
					data-bs-dismiss="modal"
				>
					Cancel
				</button>
				<button type="submit" class="btn btn-danger" onclick={onSubmit} disabled={isDeleting}
					>Delete Job</button
				>
			</div>
		</div>
	</div>
</div>
