<script lang="ts">
	import type { FormData } from '../../api/v1/jobs/types';

	export let viewData: FormData;
</script>

<div
	class="modal fade"
	id="viewJobModal"
	tabindex="-1"
	aria-labelledby="viewJobModalLabel"
	aria-hidden="true"
>
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="viewJobModalLabel">{viewData.title}</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="row mb-4">
					<div class="col-md-6">
						<p><strong>Company:</strong> {viewData.companyName}</p>
						<p><strong>Location:</strong> {viewData.jobLocation}</p>
						<p>
							<strong>Type:</strong>
							<span class="badge bg-primary">{viewData.jobType}</span>
						</p>
					</div>
					<div class="col-md-6">
						<p><strong>Salary:</strong>{viewData.salary}</p>
						<!-- <p><strong>Experience:</strong> Senior Level</p> -->
						<p>
							<strong>Status:</strong>
							<span class="status-active">Active</span>
						</p>
					</div>
				</div>

				<div class="mb-4">
					<h6>Qualifications</h6>
					<p>
						We are looking for an experienced Senior Frontend Developer to join our team. You will
						be responsible for building user interfaces and implementing interactive web
						applications using modern JavaScript frameworks.
					</p>
					<p>
						Key responsibilities include developing new user-facing features, building reusable
						components and front-end libraries, optimizing applications for maximum performance, and
						collaborating with the design team to implement visual elements.
					</p>
				</div>

				<div class="mb-4">
					<h6>Job Description</h6>
					<p>{viewData.jobDesc}</p>
				</div>

				<div class="mb-3">
					<h6>Additional Information</h6>
					<p><strong>Posted:</strong> May 12, 2023</p>
					<p><strong>Deadline:</strong> {viewData.deadline}</p>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal"> Close </button>
				<!-- <button type="button" class="btn btn-primary" onclick={() => viewData}>Edit Job</button> -->
			</div>
		</div>
	</div>
</div>
