<script lang="ts">
	import ViewJobModal from './ViewJobModal.svelte';
	import EditJobModal from './EditJobModal.svelte';
	import DeleteJobModal from './DeleteJobModal.svelte';
	import type { FormData } from '../../api/v1/jobs/types';
	import { page as pageStore } from '$app/stores';
	import { goto } from '$app/navigation';

	let isFetching = $state(false);
	let isDeleting = $state(false);

	let { data } = $props<{
		items: any[];
		page: number;
		pages: number;
		limit: number;
		total: number;
		initialSortBy?: string;
		initialSortOrder?: 'asc' | 'desc';
	}>();

	let sortBy = $state<string>(
		data.initialSortBy || $pageStore.url.searchParams.get('sortBy') || ''
	);
	let sortOrder = $state<'asc' | 'desc'>(
		data.initialSortOrder ||
			($pageStore.url.searchParams.get('sortOrder') as 'asc' | 'desc') ||
			'asc'
	);

	function goToPage(p: number) {
		if (p < 1 || p > data.pages || p === data.page) return;
		const params = new URLSearchParams($pageStore.url.searchParams);
		params.set('page', String(p));
		goto(`?${params.toString()}`, { noScroll: true, keepFocus: true });
	}

	function handleSort(column: string) {
		const newSortOrder = sortBy === column && sortOrder === 'asc' ? 'desc' : 'asc';
		sortBy = column;
		sortOrder = newSortOrder;

		const params = new URLSearchParams($pageStore.url.searchParams);
		params.set('sortBy', sortBy);
		params.set('sortOrder', sortOrder);
		params.delete('page');
		goto(`?${params.toString()}`, { noScroll: true, keepFocus: true });
	}

	let selectedData = $state<any>();
	let itemToEdit = $state<any>();
	let itemToDeleteID = $state<any>();

	let pagesToDisplay = $derived.by(() => {
		const currentPage = data.page;
		const totalPages = data.pages;
		const contextSize = 1;

		if (!totalPages || totalPages <= 0) return [];
		if (totalPages === 1) return [1];

		const uniquePages = new Set<number>();

		uniquePages.add(1);

		for (
			let i = Math.max(2, currentPage - contextSize);
			i <= Math.min(totalPages - 1, currentPage + contextSize);
			i++
		) {
			uniquePages.add(i);
		}

		if (totalPages > 1) {
			uniquePages.add(totalPages);
		}

		const result: (number | string)[] = [];
		let lastPagePushed = 0;
		const sortedUniquePages = Array.from(uniquePages).sort((a, b) => a - b);

		for (const p of sortedUniquePages) {
			if (lastPagePushed !== 0 && p > lastPagePushed + 1) {
				result.push('...');
			}
			result.push(p);
			lastPagePushed = p;
		}
		return result;
	});

	const getSerialNumber = (index: number) => {
		return (data.page - 1) * (data.limit || 10) + index + 1;
	};
</script>

<div class="card">
	{#if data.items && data.items.length > 0 && !isFetching}
		<div class="card-header d-flex justify-content-between align-items-center">
			<h5 class="mb-0">All Job Listings ({data.total || '0'})</h5>
			{#if data.pages > 0}
				<small class="text-muted">Page {data.page} of {data.pages}</small>
			{/if}
		</div>
		<div class="card-body">
			<div class="table-responsive">
				<table class="table-hover table" id="jobDataTable">
					<thead>
						<tr>
							<th style="cursor: pointer;" onclick={() => handleSort('_id')}>
								#
								{#if sortBy === '_id'}<span>{sortOrder === 'asc' ? '▲' : '▼'}</span>{/if}
							</th>
							<th style="cursor: pointer;" onclick={() => handleSort('title')}>
								Job Title
								{#if sortBy === 'title'}<span>{sortOrder === 'asc' ? '▲' : '▼'}</span>{/if}
							</th>
							<th style="cursor: pointer;" onclick={() => handleSort('companyName')}>
								Company
								{#if sortBy === 'companyName'}<span>{sortOrder === 'asc' ? '▲' : '▼'}</span>{/if}
							</th>
							<th style="cursor: pointer;" onclick={() => handleSort('jobType')}>
								Type
								{#if sortBy === 'jobType'}<span>{sortOrder === 'asc' ? '▲' : '▼'}</span>{/if}
							</th>
							<th style="cursor: pointer;" onclick={() => handleSort('jobLocation')}>
								Location
								{#if sortBy === 'jobLocation'}<span>{sortOrder === 'asc' ? '▲' : '▼'}</span>{/if}
							</th>
							<th>Actions</th>
						</tr>
					</thead>
					<tbody>
						{#each data.items as job, i (job._id)}
							<tr>
								<td>{getSerialNumber(i)}</td>
								<td class="cell-truncate">
									<div class="job-title" title={job.title}>{job.title}</div>
								</td>
								<td class="cell-truncate" title={job.companyName}>{job.companyName}</td>
								<td><span class="badge bg-primary text-capitalize">{job.jobType}</span></td>
								<td class="cell-truncate text-capitalize" title={job.jobLocation}
									>{job.jobLocation}</td
								>
								<td class="action-btns">
									<button
										onclick={() => {
											selectedData = job;
										}}
										aria-label="View Job Details"
										class="btn btn-sm btn-outline-primary"
										data-bs-toggle="modal"
										data-bs-target="#viewJobModal"
									>
										<i class="fas fa-eye"></i>
									</button>
									<button
										onclick={() => {
											itemToEdit = job;
										}}
										aria-label="Edit Job Details"
										class="btn btn-sm btn-outline-success"
										data-bs-toggle="modal"
										data-bs-target="#editJobModal"
									>
										<i class="fas fa-edit"></i>
									</button>
									<button
										onclick={() => {
											itemToDeleteID = job;
										}}
										aria-label="Delete Job Details"
										class="btn btn-sm btn-outline-danger"
										data-bs-toggle="modal"
										data-bs-target="#deleteJobModal"
									>
										<i class="fas fa-trash"></i>
									</button>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>

			{#if data.pages > 1}
				<nav aria-label="Page navigation" class="pagination-container mt-4">
					<ul class="pagination justify-content-center">
						<li class="page-item {data.page <= 1 ? 'disabled' : ''}">
							<button
								class="page-link"
								tabindex="-1"
								aria-disabled={data.page <= 1}
								onclick={() => goToPage(data.page - 1)}
							>
								Previous
							</button>
						</li>

						{#each pagesToDisplay as pageNum, index (pageNum === '...' ? `ellipsis-${index}` : pageNum)}
							{#if typeof pageNum === 'string'}
								<li class="page-item disabled"><span class="page-link">{pageNum}</span></li>
							{:else}
								<li class="page-item {data.page === pageNum ? 'active' : ''}">
									<button
										type="button"
										class="page-link"
										onclick={() => goToPage(pageNum)}
										aria-current={data.page === pageNum ? 'page' : undefined}
									>
										{pageNum}
									</button>
								</li>
							{/if}
						{/each}

						<li class="page-item {data.page >= data.pages ? 'disabled' : ''}">
							<button
								type="button"
								class="page-link"
								aria-disabled={data.page >= data.pages}
								onclick={() => goToPage(data.page + 1)}
							>
								Next
							</button>
						</li>
					</ul>
				</nav>
			{/if}
		</div>
	{:else if isFetching}
		<div class="d-flex justify-content-center align-items-center" style="height: 300px;">
			<div class="spinner-border text-primary" role="status">
				<span class="visually-hidden">Loading...</span>
			</div>
		</div>
	{:else}
		<div class="card-body text-center">
			<p class="text-muted fs-5 py-5">No job listings found matching your criteria.</p>
		</div>
	{/if}
</div>

{#if selectedData}
	<ViewJobModal bind:viewData={selectedData} onclose={() => (selectedData = undefined)} />
{/if}

{#if itemToEdit}
	<EditJobModal
		bind:isFetching
		bind:itemToEdit
		onclose={() => (itemToEdit = undefined)}
		onjobUpdated={() => {
			itemToEdit = undefined;
		}}
	/>
{/if}

{#if itemToDeleteID}
	<DeleteJobModal
		bind:isDeleting
		bind:itemToDeleteID
		onclose={() => (itemToDeleteID = undefined)}
		onjobDeleted={() => {
			itemToDeleteID = undefined;
		}}
	/>
{/if}

<style>
	.table th {
		background-color: var(--light-color, #f8f9fa);
		vertical-align: middle;
	}
	.table td {
		vertical-align: middle;
	}
	.table th span {
		font-size: 0.8em;
		margin-left: 4px;
	}

	.job-title {
		font-weight: 600;
		color: var(--secondary-color, #2c3e50);
	}

	.company-name {
		color: var(--dark-color, #34495e);
		font-size: 0.9rem;
	}

	.action-btns .btn {
		padding: 0.25rem 0.5rem;
		font-size: 0.8rem;
		margin-right: 0.25rem;
	}
	.action-btns .btn:last-child {
		margin-right: 0;
	}

	.cell-truncate {
		max-width: 200px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	.cell-truncate:hover {
		overflow: visible;
		white-space: normal;
		word-break: break-all;
	}

	.pagination-container {
		display: flex;
		justify-content: center;
		width: 100%;
		overflow-x: auto;
		padding-bottom: 8px;
	}

	.pagination-container .pagination {
		flex-shrink: 0;
		white-space: nowrap;
	}

	.text-capitalize {
		text-transform: capitalize;
	}

	th,
	td {
		text-transform: capitalize;
	}
</style>
