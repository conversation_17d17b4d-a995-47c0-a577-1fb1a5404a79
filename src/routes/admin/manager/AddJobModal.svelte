<script lang="ts">
	import type { FormData } from '../../api/v1/jobs/types';
	let {
		isFetching = $bindable(false)
	}: {
		isFetching: boolean;
	} = $props();

	const testData: FormData[] = Array.from({ length: 20 }, (_, i) => ({
		title: `Job Title ${i + 1}`,
		companyName: `Company ${i + 1}`,
		companyDesc: `Description for Company ${i + 1}`,
		jobType: ['on-site', 'remote', 'hybrid'][i % 3] as FormData['jobType'],
		jobDesc: `Detailed job description for job ${i + 1}`,
		jobField: ['medical', 'care', 'industrial', 'unspecified'][i % 4] as FormData['jobField'],
		jobLocation: `Location ${i + 1}`,
		skills: `Skill${i + 1}, Skill${i + 2}`,
		salary: `$${20 + i}/hr`,
		deadline: `2024-07-${String((i % 28) + 1).padStart(2, '0')}`
	}));

	// incase of resets
	let localFormData: FormData = {
		title: '',
		companyName: '',
		companyDesc: '',
		jobType: 'on-site',
		jobDesc: '',
		jobField: 'medical',
		jobLocation: '',
		skills: '',
		salary: '',
		deadline: ''
	};

	let reactiveFormData = $state({
		...localFormData
	});

	let formRef: HTMLFormElement | undefined = $state();
	let closeButton: HTMLButtonElement | undefined = $state();
	let errorMessage = $state('');

	const createJob = async () => {
		if (isFetching) {
			return;
		}
		try {
			const response = await fetch('/api/v1/manager/jobs', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(reactiveFormData)
			});

			if (response.ok) {
				closeButton?.click();
				window.location.reload();
			} else {
				const responseBody = await response.json();
				errorMessage = responseBody.error;
			}
		} catch {
			errorMessage = 'Something went wrong';
		} finally {
			isFetching = false;
		}
	};

	const createTestJob = async (data: FormData) => {
		if (isFetching) {
			return;
		}
		try {
			const response = await fetch('/api/v1/manager/jobs', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(data)
			});

			if (response.ok) {
				closeButton?.click();
				window.location.reload();
			} else {
				const responseBody = await response.json();
				errorMessage = responseBody.error;
			}
		} catch {
			errorMessage = 'Something went wrong';
		} finally {
			isFetching = false;
		}
	};

	// for (let index = 0; index < testData.length; index++) {
	// 	createTestJob(testData[index]);
	// }

	const onSubmit = async () => {
		if (formRef?.checkValidity()) {
			createJob();
			isFetching = true;
		} else {
			formRef?.reportValidity();
			isFetching = false;
		}
	};
</script>

<div
	class="modal fade"
	id="addJobModal"
	tabindex="-1"
	aria-labelledby="addJobModalLabel"
	aria-hidden="true"
>
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="addJobModalLabel">Add New Job Listing</h5>
				<button
					type="button"
					class="btn-close"
					bind:this={closeButton}
					data-bs-dismiss="modal"
					aria-label="Close"
				></button>
			</div>
			{#if errorMessage}
				<div class="alert alert-danger m-3">{errorMessage}</div>
			{/if}
			{#if isFetching}
				<div
					class="modal-body d-flex justify-content-center align-items-center"
					style="min-height: 200px;"
				>
					<div class="spinner-border text-primary" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
				</div>
			{:else}
				<div class="modal-body">
					<form bind:this={formRef}>
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="jobTitle" class="form-label">Job Title</label>
								<input
									minlength="5"
									type="text"
									class="form-control"
									id="jobTitle"
									placeholder="e.g. Care Worker"
									required
									disabled={isFetching}
									bind:value={reactiveFormData.title}
								/>
							</div>
							<div class="col-md-6">
								<label for="company" class="form-label">Company</label>
								<input
									type="text"
									class="form-control"
									id="company"
									placeholder="e.g. Window United"
									required
									disabled={isFetching}
									bind:value={reactiveFormData.companyName}
								/>
							</div>
						</div>

						<div class="row mb-3">
							<div class="col-md-6">
								<label for="jobType" class="form-label">Job Type</label>
								<select
									disabled={isFetching}
									class="form-select"
									id="jobType"
									required
									bind:value={reactiveFormData.jobType}
								>
									<option value="" selected disabled>Select job type</option>
									<option value="on-site">On Site</option>
									<option value="remote">Remote</option>
									<option value="hybrid">Hybrid</option>
								</select>
							</div>

							<div class="col-md-6">
								<label for="jobField" class="form-label">Job Field</label>
								<select
									disabled={isFetching}
									class="form-select"
									id="jobField"
									required
									bind:value={reactiveFormData.jobField}
								>
									<option value="" selected disabled>Select job type</option>

									<option value="medical">Medical</option>
									<option value="care">Care</option>
									<option value="industrial">Industrial</option>
									<option value="unspecified">Unspecified</option>
								</select>
							</div>
						</div>

						<div class="row mb-3">
							<div class="col-md-4">
								<label for="location" class="form-label">Job Location</label>
								<input
									type="text"
									class="form-control"
									id="location"
									placeholder="e.g. London, Uk"
									disabled={isFetching}
									required
									bind:value={reactiveFormData.jobLocation}
								/>
							</div>
							<div class="col-md-4">
								<label for="salary" class="form-label">Salary</label>
								<input
									type="text"
									class="form-control"
									id="salary"
									placeholder="e.g. $20/hr"
									disabled={isFetching}
									required
									bind:value={reactiveFormData.salary}
								/>
							</div>
							<div class="col-md-4">
								<label for="deadline" class="form-label">Deadline</label>
								<input
									type="date"
									class="form-control"
									id="deadline"
									disabled={isFetching}
									required
									bind:value={reactiveFormData.deadline}
								/>
							</div>
						</div>

						<div class="mb-3">
							<label for="description" class="form-label">Job Description</label>
							<textarea
								disabled={isFetching}
								class="form-control"
								id="description"
								rows="5"
								placeholder="Enter detailed job description..."
								required
								bind:value={reactiveFormData.jobDesc}
							></textarea>
						</div>

						<div class="mb-3">
							<label for="companyDesc" class="form-label">Company Description</label>
							<textarea
								disabled={isFetching}
								class="form-control"
								id="companyDesc"
								rows="3"
								placeholder="Enter company description"
								required
								bind:value={reactiveFormData.companyDesc}
							></textarea>
						</div>

						<div class="mb-3">
							<label for="skills" class="form-label">Job skills</label>
							<textarea
								disabled={isFetching}
								class="form-control"
								id="skills"
								rows="3"
								placeholder="Enter job skills"
								required
								bind:value={reactiveFormData.skills}
							></textarea>
						</div>
					</form>
				</div>
			{/if}

			<div class="modal-footer">
				<button
					type="button"
					disabled={isFetching}
					class="btn btn-secondary"
					data-bs-dismiss="modal"
				>
					Cancel
				</button>
				<button onclick={onSubmit} disabled={isFetching} type="submit" class="btn btn-primary"
					>Save Job</button
				>
			</div>
		</div>
	</div>
</div>
