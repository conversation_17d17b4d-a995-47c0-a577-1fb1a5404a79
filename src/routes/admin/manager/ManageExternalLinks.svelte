<script lang="ts">
	import type { IExternalLink } from '$lib/server/db/models/externalLinks';

	let {
		links = $bindable([]),
		onUpdate,
		isLoading = $bindable(false)
	}: {
		links: IExternalLink[];
		onUpdate: () => Promise<void>; // Callback to refresh links list after an update
		isLoading: boolean;
	} = $props();

	let editingLink = $state<IExternalLink | null>(null);
	let currentUrl = $state('');
	let currentDescription = $state('');
	let isSubmitting = $state(false);
	let submitError = $state<string | null>(null);
	let submitSuccess = $state<string | null>(null);

	function startEdit(link: IExternalLink) {
		editingLink = link;
		currentUrl = link.url;
		currentDescription = link.description || '';
		submitError = null;
		submitSuccess = null;
	}

	function cancelEdit() {
		editingLink = null;
		currentUrl = '';
		currentDescription = '';
	}

	async function handleUpdateSubmit(e) {
		e?.preventDefault();
		if (!editingLink || isSubmitting) return;
		isSubmitting = true;
		submitError = null;
		submitSuccess = null;

		try {
			const response = await fetch(`/api/v1/manager/external-links/${editingLink.name}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ url: currentUrl, description: currentDescription })
			});
			const result = await response.json();
			if (response.ok && result.status === 'Success') {
				submitSuccess = 'Link updated successfully!';
				await onUpdate(); // Refresh the list
				cancelEdit();
				setTimeout(() => (submitSuccess = null), 3000);
			} else {
				submitError = result.message || 'Failed to update link.';
			}
		} catch (err: any) {
			submitError = err.message || 'An unexpected error occurred.';
		} finally {
			isSubmitting = false;
		}
	}
</script>

<div class="card">
	<div class="card-header">
		<h5 class="mb-0">Manage External Links</h5>
	</div>
	<div class="card-body">
		{#if isLoading}
			<div class="d-flex justify-content-center align-items-center" style="height: 200px;">
				<div class="spinner-border text-primary" role="status">
					<span class="visually-hidden">Loading links...</span>
				</div>
			</div>
		{:else if links.length === 0}
			<p class="text-muted py-4 text-center">
				No external links found or defined. Please seed them.
			</p>
		{:else}
			{#if editingLink}
				<div class="edit-form bg-light mb-4 rounded border p-3">
					<h5>Editing: {editingLink.name}</h5>
					{#if submitError}
						<div class="alert alert-danger" role="alert">{submitError}</div>
					{/if}
					{#if submitSuccess}
						<div class="alert alert-success" role="alert">{submitSuccess}</div>
					{/if}
					<form onsubmit={handleUpdateSubmit}>
						<div class="mb-3">
							<label for="linkNameView" class="form-label">Link Name (Identifier)</label>
							<input
								type="text"
								id="linkNameView"
								class="form-control"
								value={editingLink.name}
								readonly
								disabled
							/>
						</div>
						<div class="mb-3">
							<label for="linkUrlEdit" class="form-label">URL</label>
							<input
								type="url"
								id="linkUrlEdit"
								class="form-control"
								bind:value={currentUrl}
								required
							/>
						</div>
						<button type="submit" class="btn btn-primary btn-sm me-2" disabled={isSubmitting}>
							{isSubmitting ? 'Saving...' : 'Save Changes'}
						</button>
						<button
							type="button"
							class="btn btn-secondary btn-sm"
							onclick={cancelEdit}
							disabled={isSubmitting}>Cancel</button
						>
					</form>
				</div>
			{/if}

			<div class="table-responsive">
				<table class="table-hover table">
					<thead>
						<tr>
							<th>Name (Identifier)</th>
							<!-- <th>Description</th> -->
							<th>Current URL</th>
							<th>Actions</th>
						</tr>
					</thead>
					<tbody>
						{#each links as link (link._id)}
							<tr>
								<td class="align-middle"><code>{link.name}</code></td>
								<!-- <td class="align-middle">{link.description || '-'}</td> -->
								<td class="align-middle">
									<a
										href={link.url}
										target="_blank"
										rel="noopener noreferrer"
										title={link.url}
										class="d-inline-block text-truncate"
										style="max-width: 300px;"
									>
										{link.url}
									</a>
								</td>
								<td class="align-middle">
									<button
										class="btn btn-outline-primary btn-sm"
										onclick={() => startEdit(link)}
										disabled={!!editingLink}
									>
										<i class="fas fa-edit"></i> Edit
									</button>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		{/if}
	</div>
</div>

<style>
	.edit-form h5 {
		margin-bottom: 1rem;
	}
	.table td.align-middle,
	.table th.align-middle {
		vertical-align: middle;
	}
</style>
