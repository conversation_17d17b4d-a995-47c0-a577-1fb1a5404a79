<script lang="ts">
	import { slide } from 'svelte/transition';
	import { z } from 'zod';

	// ShadCN-Svelte Imports (ensure paths are correct)
	import * as Card from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import * as InputOTP from '$lib/components/ui/input-otp/index.js';
	import HCaptcha from './HCaptcha.svelte'; // Your local HCaptcha component

	// Ensure this is VITE_PUBLIC_HCAPTCHA_SITE_KEY in your .env file
	const PUBLIC_HCAPTCHA_SITE_KEY = import.meta.env.VITE_PUBLIC_HCAPTCHA_SITE_KEY;

	const emailSchema = z.object({
		email: z.string().email({ message: 'Invalid email address.' })
	});
	let form = {
		email: '',
		otp: '' // This will be bound to InputOTP.Root's value
	};

	let errors = {
		email: '' as string | undefined,
		otp: '' as string | undefined,
		api: '' as string | undefined
	};

	let fetching = false;
	let otpScreen = false;
	let passedCaptcha = false;
	let passToken: string | undefined = undefined;

	const clearErrors = () => {
		errors.email = undefined;
		errors.otp = undefined;
		errors.api = undefined;
	};

	const handleRequestOTP = async () => {
		clearErrors();
		if (fetching || !passedCaptcha || !passToken) {
			if (!passedCaptcha) errors.api = 'Please complete the CAPTCHA.';
			return;
		}

		const result = emailSchema.safeParse({ email: form.email });

		if (!result.success) {
			errors.email = result.error.formErrors.fieldErrors.email?.[0];
			return;
		}
		fetching = true;
		try {
			const response = await fetch('/api/v1/auth', {
				// Your API endpoint
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ email: form.email, passToken })
			});

			const data = await response.json();
			if (response.ok) {
				otpScreen = true;
			} else {
				errors.api = data.message || 'Failed to request OTP. Please try again.';
			}
		} catch (e) {
			console.error('Request OTP error:', e);
			errors.api = 'Network error or unexpected issue. Please try again.';
		} finally {
			fetching = false;
			// Reset CAPTCHA state for potential re-attempts
			// Consider calling a reset method on your HCaptcha component if it has one
			passedCaptcha = false;
			// passToken = undefined; // Token is single-use, clear it
		}
	};

	const emailOtpSchema = z.object({
		otp: z.string().length(6, 'OTP must be 6 digits.')
	});

	const handleVerifyOTP = async () => {
		clearErrors();
		if (fetching) return;

		// form.otp is already bound to the InputOTP.Root value
		const result = emailOtpSchema.safeParse({ otp: form.otp });
		if (!result.success) {
			errors.otp = result.error.formErrors.fieldErrors.otp?.[0];
			return;
		}
		fetching = true;
		try {
			const response = await fetch('/api/v1/auth', {
				// Your API endpoint
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ email: form.email, otp: form.otp })
			});

			const data = await response.json();
			if (response.ok) {
				window.location.href = '/admin/manager'; // Or your desired redirect path
			} else {
				errors.otp = data.message || data.otp || 'Invalid OTP or server error.';
			}
		} catch (e) {
			console.error('Verify OTP error:', e);
			errors.api = 'Network error or unexpected issue. Please try again.';
		} finally {
			fetching = false;
		}
	};

	// Reactive statement to clear OTP error when OTP input changes
	// The oninput on InputOTP.Root will handle clearing the error.
	// $: if (form.otp && errors.otp) errors.otp = undefined; // Can be removed if oninput handles it
	$: if (form.email && errors.email) errors.email = undefined;
</script>

<div class="flex h-full w-full items-center justify-center">
	<Card.Root class="w-full max-w-md shadow-xl">
		<Card.Header class="text-center">
			<Card.Title class="text-2xl font-bold tracking-tight">
				{#if !otpScreen}
					Login In
				{:else}
					Enter Your Code
				{/if}
			</Card.Title>
			<Card.Description class="text-muted-foreground">
				{#if !otpScreen}
					Enter your email to receive an OTP.
				{:else}
					An OTP has been sent to {form.email || 'your email'}.
				{/if}
			</Card.Description>
		</Card.Header>

		<Card.Content class="space-y-6 p-6">
			{#if errors.api}
				<div
					class="border-destructive bg-destructive/10 text-destructive mb-4 rounded-md border p-3 text-center text-sm"
					role="alert"
				>
					{errors.api}
				</div>
			{/if}

			{#if !otpScreen}
				<form on:submit|preventDefault={handleRequestOTP} class="space-y-6">
					<div class="space-y-2">
						<label for="email" class="sr-only">Email</label>
						<Input
							type="email"
							id="email"
							placeholder="<EMAIL>"
							required
							autocomplete="email"
							bind:value={form.email}
							disabled={fetching}
							aria-invalid={!!errors.email}
							aria-describedby={errors.email ? 'email-error' : undefined}
							oninput={() => (errors.email = undefined)}
							class={errors.email ? 'border-destructive focus-visible:ring-destructive' : ''}
						/>
						{#if errors.email}
							<p id="email-error" class="text-destructive text-sm">{errors.email}</p>
						{/if}
					</div>

					<div class="flex flex-col items-center space-y-3">
						<HCaptcha
							sitekey={PUBLIC_HCAPTCHA_SITE_KEY}
							on:success={(e) => {
								passToken = e.detail.token;
								passedCaptcha = true;
								if (errors.api === 'Please complete the CAPTCHA.') errors.api = undefined;
							}}
							on:error={(e) => {
								console.error('HCaptcha error event:', e.detail);
								passToken = undefined;
								passedCaptcha = false;
								errors.api = e.detail?.error || 'CAPTCHA verification failed. Please try again.';
							}}
							on:expired={() => {
								passToken = undefined;
								passedCaptcha = false;
								errors.api = 'CAPTCHA token expired. Please try again.';
							}}
						/>
					</div>

					<Button type="submit" class="w-full" disabled={fetching || !passedCaptcha}>
						{fetching ? 'Sending OTP...' : 'Request Access'}
					</Button>
				</form>
			{:else}
				<div transition:slide|local class="space-y-6">
					<form on:submit|preventDefault={handleVerifyOTP} class="space-y-6">
						<div class="flex flex-col items-center space-y-2">
							<label for="otp-input" class="sr-only">One-Time Password</label>
							<InputOTP.Root
								bind:value={form.otp}
								maxlength={6}
								required
								disabled={fetching}
								oninput={() => {
									if (form.otp.length <= 6 && errors.otp) errors.otp = undefined;
								}}
								id="otp-input"
								aria-label="One-Time Password"
								aria-describedby={errors.otp ? 'otp-error' : undefined}
							>
								{#snippet children({ cells })}
									<InputOTP.Group class="flex justify-center gap-2">
										{#each cells as cell, i (i)}
											{#if i < 3}
												<InputOTP.Slot
													{cell}
													class={errors.otp
														? 'border-destructive focus:border-destructive focus:ring-destructive'
														: ''}
												/>
											{/if}
										{/each}
									</InputOTP.Group>
									{#if cells.length > 3}
										<InputOTP.Separator />
									{/if}
									<InputOTP.Group class="flex justify-center gap-2">
										{#each cells as cell, i (i)}
											{#if i >= 3}
												<InputOTP.Slot
													{cell}
													class={errors.otp
														? 'border-destructive focus:border-destructive focus:ring-destructive'
														: ''}
												/>
											{/if}
										{/each}
									</InputOTP.Group>
								{/snippet}
							</InputOTP.Root>
							{#if errors.otp}
								<p id="otp-error" class="text-destructive text-center text-sm">{errors.otp}</p>
							{/if}
						</div>
						<Button type="submit" class="w-full" disabled={fetching || form.otp.length < 6}>
							{fetching ? 'Authenticating...' : 'Verify OTP'}
						</Button>
					</form>
					<Button
						variant="link"
						onclick={() => {
							// Changed from onclick to on:click for Svelte convention
							otpScreen = false;
							clearErrors();
							form.otp = ''; // Clear OTP value
							passedCaptcha = false;
							passToken = undefined;
							// You might need a method to reset the HCaptcha component instance if it doesn't reset automatically
						}}
						class="text-muted-foreground w-full"
						disabled={fetching}
					>
						Back to email
					</Button>
				</div>
			{/if}
		</Card.Content>
		<Card.Footer class="text-muted-foreground p-6 pt-0 text-center text-sm">
			<!-- Need help? <a href="/contact" class="hover:text-primary underline">Contact support</a> -->
		</Card.Footer>
	</Card.Root>
</div>
