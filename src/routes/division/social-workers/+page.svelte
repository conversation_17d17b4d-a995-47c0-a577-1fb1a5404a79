<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	import SplitSection from '$lib/components/shared/SplitSection.svelte';
	import { getContext } from 'svelte';
	import type { ExternalLinksConfig } from '../../../app';

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;

	const h1 = 'Empowering People. <br /> Protecting Lives. <br /> Supporting Change.';
	const p =
		'At Central Staffing, we recruit and place qualified and experienced Social Workers who are committed to improving lives and safeguarding vulnerable individuals and families.';

	const listItems: ListItem[] = [
		{ text: 'Conduct assessments and develop support or care plans' },
		{ text: 'Safeguard at-risk children, adults, and families' },
		{ text: 'Provide crisis intervention and advocacy services' },
		{ text: 'Collaborate with multi-agency teams including healthcare, education, housing, and legal services' },
		{ text: 'Support individuals with disabilities, mental health issues, substance misuse, or domestic abuse' }
	];

	const keyAreas: ListItem[] = [
		{ text: 'Adult Services (Elderly care, Physical Disabilities, Learning Disabilities)' },
		{ text: 'Mental Health and Substance Misuse' },
		{ text: 'Hospital Discharge and Community Integration' },
		{ text: 'Youth Offending Teams' },
		{ text: 'Court and Legal Report Writing' }
	];
</script>

<svelte:head>
	<title>Social Workers | Central Staffing</title>
	<meta
		name="description"
		content="Join Central Staffing as a Social Worker. We recruit qualified professionals for adult services, mental health, safeguarding, and community integration roles across the UK."
	/>
</svelte:head>

{#snippet Image()}
	<img
		src="/fold/social-1.avif"
		alt="Social worker supporting a family"
		class="special h-full w-full object-cover"
	/>
{/snippet}

{#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm link={externalLinks.applicationFormNursesAHP?.url} />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet ImagesSection()}
	<div class="absolute inset-0 flex gap-4">
		<div class="relative w-[45%] flex-shrink-0">
			<img
				src="/fold/social-2.avif"
				alt="Social worker with client"
				class="absolute top-0 left-0 z-10 h-[calc(50%_-_theme(space.2))] w-full rounded-lg border-2 border-white object-cover shadow-lg"
			/>
			<img
				src="/fold/social-3.avif"
				alt="Team meeting"
				class="absolute bottom-0 left-0 z-10 h-[calc(50%_-_theme(space.2))] w-full rounded-lg border-2 border-white object-contain object-cover object-left shadow-lg"
			/>
			<svg
				class="pointer-events-none absolute inset-0 z-5"
				viewBox="0 0 100 100"
				preserveAspectRatio="none"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path d="M95 48 C 70 48, 70 95, 45 95" stroke="#FDBA74" stroke-width="1.5" />
			</svg>
		</div>

		<div class="relative w-[55%] flex-grow">
			<img
				src="/fold/social-4.avif"
				alt="Social worker with documentation"
				class="z-0 h-full w-full rounded-none border border-dashed border-gray-300 object-cover"
			/>
			<div class="absolute top-4 right-4 z-20 rounded-full bg-white p-1.5 shadow-md">
				<svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
					<path
						fill-rule="evenodd"
						d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
						clip-rule="evenodd"
					/>
				</svg>
			</div>
		</div>
	</div>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

<SplitSection
	introParagraph="Whether working in adult services, children's safeguarding, or mental health settings, our social workers bring professionalism, empathy, and accountability to every case. We work closely with local authorities, healthcare trusts, private care organisations, and community-based services to supply highly capable professionals across all social work disciplines."
	{listItems}
	buttomCTA={BottomFooter}
	images={ImagesSection}
	imagePosition="right"
/>

<!-- Key Areas Section -->
<section class="bg-gray-50 py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 max-w-3xl text-center">
			<h2 class="text-3xl font-bold text-[#5D28B6] sm:text-4xl">Key Areas of Placement</h2>
			<p class="mt-6 text-lg text-gray-600">
				Our social workers are placed across diverse settings to meet the varied needs of communities and individuals.
			</p>
		</div>
		<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
			{#each keyAreas as area}
				<div class="rounded-lg bg-white p-6 shadow-md transition-shadow hover:shadow-xl">
					<div class="flex items-center">
						<div class="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
							<svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
						</div>
						<h3 class="text-lg font-semibold text-gray-900">{area.text}</h3>
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<Subscribe />

<style>
	.special {
		z-index: -20;
	}
</style>
