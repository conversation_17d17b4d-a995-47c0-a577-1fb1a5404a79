<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import Vacancies from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import MedicalServicesRequestCall from '$lib/components/shared/MedicalServicesRequestCall.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import ServicesRequestCall from '$lib/components/shared/ServicesRequestCall.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	import SplitSection from '$lib/components/shared/SplitSection.svelte';
	const h1 = `Strengthening Health <br /> Care Teams With <br /> Expert Staffing  <br /> Solutions`;

	const p =
		'Providing skilled medical professionals to support hospitals, care homes, and healthcare facilities with efficiency and reliability.';

	const listItems: ListItem[] = [
		{ text: 'Human Resources, Recruitment Consultant, Fiance/Account' },
		{
			text: 'Sales & Retail, Call Centres, Marketing, Medical Administrators/Front Desk Personnel, Banking'
		},
		{
			text: 'Office Administrators/Front Desk Personnel/PA’s, Junior/Senior Management Roles, IT/Project Managers '
		}
	];
</script>

{#snippet Image()}
	<img
		src="/medical/medical-hero.avif"
		alt="Healthcare worker assisting an elderly patient"
		class="special h-full w-full object-cover object-center"
	/>
{/snippet}

{#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet ImagesSection()}
	<div class="relative aspect-square w-full">
		<div class="relative z-0 h-full">
			<img
				src="/medical/img-35.avif"
				alt="Team of medical professionals"
				class="h-full w-full rounded-[40px] object-contain object-center"
			/>
		</div>
	</div>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

<section class="bg-white py-16 md:pt-8 md:pb-4">
	<div class="central-p mx-auto px-4">
		<div class="grid grid-cols-1 gap-10 lg:grid-cols-2 lg:gap-12 xl:gap-16">
			<div>{@render ImagesSection()}</div>

			<div class="flex h-full flex-col gap-[26px]">
				<p class="mt-auto mb-3 text-base font-semibold tracking-wider text-orange-600 uppercase">
					MEDICAL & HEALTHCARE
				</p>

				<p class="max-w-[54ch] text-lg font-bold text-[#04091B]">
					We work with our clients to understand their day to day staffing challenges
				</p>

				<p class="intro max-w-[54ch] text-justify text-base text-[#151414]">
					Our experienced consultants understand the increasing demand for medical and healthcare
					professionals and the stress understaffed ward and units could impose to managers and
					front line professionals. With ad-hoc staffing support services we are committed to
					efficiency and consistency in our staff placement. We recruit, vet and manage a pool of
					medical and healthcare professionals for roles with private hospitals, NHS hospitals,
					local authorities, learning disability units, nursing and residential settings.
				</p>

				<a
					href="/ongoing-jobs"
					class="mb-auto inline-flex max-w-[54ch] items-center justify-center rounded-full border border-transparent bg-[#5D28B6] px-7 py-3 text-base font-medium text-white transition duration-200 hover:bg-purple-800"
				>
					Apply Now
					<span
						class="ml-2 inline-flex items-center justify-center rounded-full bg-white p-1 text-purple-700"
					>
						<svg
							width="33"
							height="33"
							viewBox="0 0 33 33"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<circle cx="16.5" cy="16.5" r="16.5" fill="white" />
							<path
								d="M10 24L23 11M23 11V23.48M23 11H10.52"
								stroke="#5D28B6"
								stroke-width="1.5"
								stroke-linecap="round"
								stroke-linejoin="round"
							/>
						</svg>
					</span>
				</a>
			</div>
		</div>
	</div>
</section>

<!-- <Vacancies /> -->

<MedicalServicesRequestCall />

<Subscribe />

<style>
	.special {
		z-index: -20;
		/* border: solid 1px#FFFAFF; */
		/* border-radius: 20px; */
	}
</style>
