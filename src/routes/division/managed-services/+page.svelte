<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	import SplitSection from '$lib/components/shared/SplitSection.svelte';
	import { getContext } from 'svelte';
	import type { ExternalLinksConfig } from '../../../app';

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;

	const h1 = 'Managed Services <br /> Complete Workforce <br /> Solutions';
	const p =
		'Comprehensive workforce management solutions tailored to your organization\'s needs. From complete staffing oversight to specialized project management, we deliver results that matter.';

	const listItems: ListItem[] = [
		{ text: 'Complete workforce management and oversight' },
		{ text: 'Dedicated account management and support' },
		{ text: 'Customized staffing solutions for your specific needs' },
		{ text: 'Performance monitoring and quality assurance' },
		{ text: 'Cost-effective resource allocation' },
		{ text: 'Compliance management and regulatory support' },
		{ text: 'Scalable solutions that grow with your business' },
		{ text: '24/7 support and emergency coverage' }
	];

	const serviceAreas = [
		{
			title: 'Healthcare Workforce Management',
			description: 'Complete management of your healthcare staffing needs, from recruitment to ongoing support and quality assurance.',
			features: [
				'Nurse and healthcare professional placement',
				'Compliance and credential management',
				'Shift scheduling and coverage',
				'Performance monitoring and feedback'
			]
		},
		{
			title: 'Social Care Management',
			description: 'Comprehensive social care staffing solutions with dedicated support workers and care coordinators.',
			features: [
				'Social worker placement and management',
				'Care coordinator services',
				'Training and development programs',
				'Quality assurance and monitoring'
			]
		},
		{
			title: 'Project-Based Solutions',
			description: 'Specialized workforce solutions for specific projects, initiatives, or temporary requirements.',
			features: [
				'Project team assembly',
				'Temporary workforce scaling',
				'Specialized skill matching',
				'Project completion guarantee'
			]
		},
		{
			title: 'Compliance & Quality Assurance',
			description: 'Ensuring all staff meet regulatory requirements and maintain the highest standards of care.',
			features: [
				'DBS checks and background verification',
				'Training compliance monitoring',
				'Regular quality audits',
				'Regulatory reporting and documentation'
			]
		}
	];
</script>

<svelte:head>
	<title>Managed Services | Central Staffing</title>
	<meta
		name="description"
		content="Comprehensive workforce management solutions from Central Staffing. Complete staffing oversight, project management, and quality assurance services."
	/>
</svelte:head>

{#snippet Image()}
	<img
		src="/services/managed-services.avif"
		alt="Professional team managing workforce solutions"
		class="special h-full w-full object-cover"
	/>
{/snippet}

{#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm link={externalLinks.applicationFormNursesAHP?.url} />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet ImagesSection()}
	<div class="absolute inset-0 flex gap-4">
		<div class="relative w-[45%] flex-shrink-0">
			<img
				src="/services/managed-1.avif"
				alt="Team collaboration"
				class="absolute top-0 left-0 z-10 h-[calc(50%_-_theme(space.2))] w-full rounded-lg border-2 border-white object-cover shadow-lg"
			/>
			<img
				src="/services/managed-2.avif"
				alt="Quality assurance meeting"
				class="absolute bottom-0 left-0 z-10 h-[calc(50%_-_theme(space.2))] w-full rounded-lg border-2 border-white object-cover object-left shadow-lg"
			/>
			<svg
				class="pointer-events-none absolute inset-0 z-5"
				viewBox="0 0 100 100"
				preserveAspectRatio="none"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path d="M95 48 C 70 48, 70 95, 45 95" stroke="#FDBA74" stroke-width="1.5" />
			</svg>
		</div>

		<div class="relative w-[55%] flex-grow">
			<img
				src="/services/managed-3.avif"
				alt="Professional consultation"
				class="z-0 h-full w-full rounded-none border border-dashed border-gray-300 object-cover"
			/>
			<div class="absolute top-4 right-4 z-20 rounded-full bg-white p-1.5 shadow-md">
				<svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
					<path
						fill-rule="evenodd"
						d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
						clip-rule="evenodd"
					/>
				</svg>
			</div>
		</div>
	</div>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

<SplitSection
	introParagraph="Our Managed Services division provides end-to-end workforce solutions that take the complexity out of staffing management. Whether you need complete oversight of your healthcare workforce or specialized project-based support, we deliver comprehensive solutions that ensure quality, compliance, and cost-effectiveness."
	{listItems}
	buttomCTA={BottomFooter}
	images={ImagesSection}
	imagePosition="right"
/>

<!-- Service Areas Section -->
<section class="bg-gray-50 py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 max-w-3xl text-center">
			<h2 class="text-3xl font-bold text-[#5D28B6] sm:text-4xl">Our Service Areas</h2>
			<p class="mt-6 text-lg text-gray-600">
				Comprehensive workforce management across multiple sectors and specializations.
			</p>
		</div>
		<div class="grid grid-cols-1 gap-8 md:grid-cols-2">
			{#each serviceAreas as area}
				<div class="rounded-lg bg-white p-8 shadow-lg">
					<h3 class="mb-4 text-xl font-bold text-gray-900">{area.title}</h3>
					<p class="mb-6 text-gray-700">{area.description}</p>
					
					<h4 class="mb-3 text-lg font-semibold text-purple-700">Key Features:</h4>
					<ul class="space-y-2">
						{#each area.features as feature}
							<li class="flex items-start">
								<svg class="mr-2 mt-1 h-4 w-4 flex-shrink-0 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
								</svg>
								<span class="text-sm text-gray-700">{feature}</span>
							</li>
						{/each}
					</ul>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Why Choose Our Managed Services -->
<section class="bg-white py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 max-w-3xl text-center">
			<h2 class="text-3xl font-bold text-[#5D28B6] sm:text-4xl">Why Choose Our Managed Services?</h2>
		</div>
		<div class="grid grid-cols-1 gap-8 md:grid-cols-3">
			<div class="text-center">
				<div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
					<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
					</svg>
				</div>
				<h3 class="mb-2 text-xl font-semibold">Proven Expertise</h3>
				<p class="text-gray-600">Years of experience in workforce management across healthcare and social care sectors.</p>
			</div>
			<div class="text-center">
				<div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
					<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
					</svg>
				</div>
				<h3 class="mb-2 text-xl font-semibold">Rapid Response</h3>
				<p class="text-gray-600">Quick deployment and scaling of workforce solutions to meet urgent requirements.</p>
			</div>
			<div class="text-center">
				<div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
					<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
					</svg>
				</div>
				<h3 class="mb-2 text-xl font-semibold">Cost Effective</h3>
				<p class="text-gray-600">Optimized resource allocation and management to deliver maximum value for your investment.</p>
			</div>
		</div>
	</div>
</section>

<Subscribe />

<style>
	.special {
		z-index: -20;
	}
</style>
