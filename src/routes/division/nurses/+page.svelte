<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	import SplitSection from '$lib/components/shared/SplitSection.svelte';
	import { getContext } from 'svelte';
	import type { ExternalLinksConfig } from '../../../app';

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;

	const h1 =
		'Empowering Nurses <br /> With Flexible Schedules <br /> And Expert Support <br /> Accross The UK';
	const p =
		'Join a trusted that values your time, supports your growth, and connects you with rewarding roles that fit your life.';

	const listItems: ListItem[] = [
		{ text: 'Exclusive one to one support' },
		{ text: 'On the job search' },
		{ text: 'Improve and develop skills and competencies on the job, trainings, workshnops' },
		{ text: 'Flexible shift pattern to suit your life style and commitment ' },
		{ text: 'Full time or part time work' },
		{ text: 'Earn fantastic rates and competitively' },
		{ text: 'Work in a variety of different clinical settings with regular shifts' },
		{ text: '24hrs on call support services' }
	];
</script>

{#snippet Image()}
	<img
		src="/medical/nur01.png"
		alt="Healthcare worker assisting an elderly patient"
		class="special h-full w-full object-cover"
	/>
{/snippet}

{#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm link={externalLinks.applicationFormNursesAHP?.url} />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet ImagesSection()}
	<div class="absolute inset-0 flex gap-4">
		<div class="relative w-[45%] flex-shrink-0">
			<img
				src="/medical/01.avif"
				alt="Nurse with patient"
				class="absolute top-0 left-0 z-10 h-[calc(50%_-_theme(space.2))] w-full rounded-lg border-2 border-white object-cover shadow-lg"
			/>
			<img
				src="/medical/02.avif"
				alt="Medical team discussion"
				class="absolute bottom-0 left-0 z-10 h-[calc(50%_-_theme(space.2))] w-full rounded-lg border-2 border-white object-contain object-cover object-left shadow-lg"
			/>
			<svg
				class="pointer-events-none absolute inset-0 z-5"
				viewBox="0 0 100 100"
				preserveAspectRatio="none"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path d="M95 48 C 70 48, 70 95, 45 95" stroke="#FDBA74" stroke-width="1.5" />
			</svg>
		</div>

		<div class="relative w-[55%] flex-grow">
			<img
				src="/medical/03.avif"
				alt="Nurse smiling with tablet"
				class="z-0 h-full w-full rounded-none border border-dashed border-gray-300 object-cover"
			/>
			<div class="absolute top-4 right-4 z-20 rounded-full bg-white p-1.5 shadow-md">
				<svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
					<path
						fill-rule="evenodd"
						d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
						clip-rule="evenodd"
					/>
				</svg>
			</div>
		</div>
	</div>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

<SplitSection
	introParagraph={`At central staffing we have exciting opportunities for Nurses within the UK. Join our team and our experienced consultants will manage your availabilities to suit your life commitment and you will benefit from the following: - `}
	{listItems}
	buttomCTA={BottomFooter}
	images={ImagesSection}
	imagePosition="right"
/>

<Subscribe />

<style>
	.special {
		z-index: -20;
	}
</style>
