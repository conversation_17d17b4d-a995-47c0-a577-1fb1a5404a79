<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	import SplitSection from '$lib/components/shared/SplitSection.svelte';
	import { getContext } from 'svelte';
	import type { ExternalLinksConfig } from '../../../app';

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;

	const h1 = '🩺 Nurses';
	const p =
		'At Central Staffing, we are proud to provide highly skilled and compassionate nurses across multiple care settings. Our nursing teams play a critical role in maintaining high standards of care, supporting NHS trusts, private hospitals, care homes, and community health services across the UK.';

	const listItems: ListItem[] = [
		{ text: 'Exclusive one to one support' },
		{ text: 'On the job search' },
		{ text: 'Improve and develop skills and competencies on the job, trainings, workshops' },
		{ text: 'Flexible shift pattern to suit your lifestyle and commitment' },
		{ text: 'Full time or part time work' },
		{ text: 'Earn fantastic rates competitively' },
		{ text: 'Work in a variety of different clinical settings with regular shifts' },
		{ text: '24hrs on call support services' }
	];

	const nurseTypes = [
		{
			title: '👩🏽‍⚕️ General Nurses',
			description:
				'Our General Nurses (RGNs) are experienced in providing day-to-day care across various healthcare settings, from hospitals to residential homes and community services. They are trained to respond to complex medical needs while maintaining a person centred approach.',
			whatTheyDo: [
				'Administer medications and monitor vital signs',
				'Manage chronic illnesses and acute health issues',
				'Provide wound care, IV therapy, and post operative support',
				'Offer emotional support and health education to patients and families',
				'Collaborate with doctors, specialists, and care teams'
			],
			whereTheyWork: [
				'Hospitals (NHS and private)',
				'GP surgeries',
				'Residential care homes',
				'Community nursing roles'
			],
			note: 'Our RGNs are compassionate, adaptable, and fully compliant with all regulatory and clinical governance standards.'
		},
		{
			title: '🚑 Frontline Nurses',
			description:
				'Frontline Nurses are often the first responders in high pressure, fast paced environments. These nurses are equipped to handle emergencies, urgent care situations, and multidisciplinary support roles in both acute and primary care settings.',
			whatTheyDo: [
				'Triage and stabilise patients in A&E and urgent care units',
				'Provide trauma informed care in crisis situations',
				'Manage rapid response units and field care',
				'Deliver care under time sensitive and resource limited conditions'
			],
			keySkills: [
				'Critical thinking and quick decision making',
				'Resilience under pressure',
				'Teamwork in multidisciplinary teams'
			],
			note: "Frontline nurses are at the heart of the UK's healthcare response, and we ensure that our talent pool is not only clinically excellent but also mentally prepared to operate in demanding roles."
		},
		{
			title: '🧠 Mental Health Nurses',
			description:
				'Our Mental Health Nurses (RMNs) specialise in supporting individuals facing psychological, emotional, and behavioural challenges. Whether in community services or inpatient settings, our RMNs deliver holistic care with compassion and discretion.',
			whatTheyDo: [
				'Assess mental health conditions and develop care plans',
				'Administer psychiatric medications and monitor side effects',
				'Work closely with psychologists, social workers, and families',
				'Support individuals with conditions such as depression, anxiety, bipolar disorder, schizophrenia, and dementia',
				'Manage safeguarding, risk, and crisis interventions'
			],
			whereTheyWork: [
				'Psychiatric units',
				'Community mental health teams',
				'Crisis intervention and recovery homes',
				'Schools, prisons, and supported living environments'
			],
			note: 'Our RMNs are trained in de-escalation, trauma informed care, and Positive Behaviour Support (PBS), ensuring that service users receive safe and respectful care.'
		}
	];
</script>

<svelte:head>
	<title>Nurses | Central Staffing</title>
	<meta
		name="description"
		content="Join Central Staffing as a nurse. We recruit General Nurses, Frontline Nurses, and Mental Health Nurses for diverse healthcare settings across the UK."
	/>
</svelte:head>

{#snippet Image()}
	<img
		src="/medical/nur01.png"
		alt="Healthcare worker assisting an elderly patient"
		class="special h-full w-full object-cover"
	/>
{/snippet}

{#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm link={externalLinks.applicationFormNursesAHP?.url} />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet ImagesSection()}
	<div class="absolute inset-0 flex gap-4">
		<div class="relative w-[45%] flex-shrink-0">
			<img
				src="/medical/01.avif"
				alt="Nurse with patient"
				class="absolute top-0 left-0 z-10 h-[calc(50%_-_theme(space.2))] w-full rounded-lg border-2 border-white object-cover shadow-lg"
			/>
			<img
				src="/medical/02.avif"
				alt="Medical team discussion"
				class="absolute bottom-0 left-0 z-10 h-[calc(50%_-_theme(space.2))] w-full rounded-lg border-2 border-white object-cover object-left shadow-lg"
			/>
			<svg
				class="pointer-events-none absolute inset-0 z-5"
				viewBox="0 0 100 100"
				preserveAspectRatio="none"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path d="M95 48 C 70 48, 70 95, 45 95" stroke="#FDBA74" stroke-width="1.5" />
			</svg>
		</div>

		<div class="relative w-[55%] flex-grow">
			<img
				src="/medical/03.avif"
				alt="Nurse smiling with tablet"
				class="z-0 h-full w-full rounded-none border border-dashed border-gray-300 object-cover"
			/>
			<div class="absolute top-4 right-4 z-20 rounded-full bg-white p-1.5 shadow-md">
				<svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
					<path
						fill-rule="evenodd"
						d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
						clip-rule="evenodd"
					/>
				</svg>
			</div>
		</div>
	</div>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

<SplitSection
	introParagraph="We recruit and deploy General Nurses, Frontline Nurses, and Mental Health Nurses each bringing a unique skill set to meet diverse patient and client needs. At central staffing we have exciting opportunities for Nurses within the UK. Join our team and our experienced consultants will manage your availabilities to suit your life commitment and you will benefit from the following:"
	{listItems}
	buttomCTA={BottomFooter}
	images={ImagesSection}
	imagePosition="right"
/>

<!-- Nurse Types Section -->
<section class="bg-gray-50 py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 max-w-3xl text-center">
			<h2 class="text-3xl font-bold text-[#5D28B6] sm:text-4xl">Our Nursing Specialties</h2>
			<p class="mt-6 text-lg text-gray-600">
				We recruit and deploy three main types of nurses, each bringing unique skills to meet
				diverse patient and client needs.
			</p>
		</div>
		<div class="space-y-12">
			{#each nurseTypes as nurse}
				<div class="rounded-lg bg-white p-8 shadow-lg">
					<h3 class="mb-4 text-2xl font-bold text-gray-900">{nurse.title}</h3>
					<p class="mb-6 text-gray-700">{nurse.description}</p>

					<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
						<div>
							<h4 class="mb-3 text-lg font-semibold text-purple-700">What They Do:</h4>
							<ul class="space-y-2">
								{#each nurse.whatTheyDo as task}
									<li class="flex items-start">
										<svg
											class="mt-1 mr-2 h-4 w-4 flex-shrink-0 text-purple-600"
											fill="currentColor"
											viewBox="0 0 20 20"
										>
											<path
												fill-rule="evenodd"
												d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
												clip-rule="evenodd"
											/>
										</svg>
										<span class="text-sm text-gray-700">{task}</span>
									</li>
								{/each}
							</ul>
						</div>

						{#if nurse.whereTheyWork}
							<div>
								<h4 class="mb-3 text-lg font-semibold text-purple-700">Where They Work:</h4>
								<ul class="space-y-2">
									{#each nurse.whereTheyWork as location}
										<li class="flex items-start">
											<svg
												class="mt-1 mr-2 h-4 w-4 flex-shrink-0 text-purple-600"
												fill="currentColor"
												viewBox="0 0 20 20"
											>
												<path
													fill-rule="evenodd"
													d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
													clip-rule="evenodd"
												/>
											</svg>
											<span class="text-sm text-gray-700">{location}</span>
										</li>
									{/each}
								</ul>
							</div>
						{/if}

						{#if nurse.keySkills}
							<div>
								<h4 class="mb-3 text-lg font-semibold text-purple-700">Key Skills:</h4>
								<ul class="space-y-2">
									{#each nurse.keySkills as skill}
										<li class="flex items-start">
											<svg
												class="mt-1 mr-2 h-4 w-4 flex-shrink-0 text-purple-600"
												fill="currentColor"
												viewBox="0 0 20 20"
											>
												<path
													fill-rule="evenodd"
													d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
													clip-rule="evenodd"
												/>
											</svg>
											<span class="text-sm text-gray-700">{skill}</span>
										</li>
									{/each}
								</ul>
							</div>
						{/if}
					</div>

					<div class="mt-6 rounded-lg bg-purple-50 p-4">
						<p class="text-sm text-purple-800">{nurse.note}</p>
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Register Section -->
<section class="bg-[#5D28B6] py-16 text-white">
	<div class="central-p mx-auto px-4 text-center">
		<h2 class="mb-6 text-3xl font-bold">Register With Us Right Here</h2>
		<p class="mb-8 text-lg">
			Ready to take the next step in your nursing career? Join Central Staffing today and discover
			opportunities that match your skills and aspirations.
		</p>
		<div class="flex flex-col gap-4 sm:flex-row sm:justify-center">
			<a
				href="/candidates"
				class="rounded-full bg-white px-8 py-3 font-semibold text-[#5D28B6] transition hover:bg-gray-100"
			>
				Apply Now
			</a>
			<a
				href="/contact"
				class="rounded-full border-2 border-white px-8 py-3 font-semibold text-white transition hover:bg-white hover:text-[#5D28B6]"
			>
				Contact Us
			</a>
		</div>
	</div>
</section>

<Subscribe />

<style>
	.special {
		z-index: -20;
	}
</style>
