<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	import SplitSection from '$lib/components/shared/SplitSection.svelte';
	const h1 = `Flexible HealthCare <br /> Roles Managed To <br /> Suit Your Availability `;

	const p =
		'Join a firm that values your time, supports your growth, and connects you with rewarding roles that fit your life.';

	const listItems: ListItem[] = [
		{ text: 'Personalized One-on-One Support: Get exclusive guidance from your consultant.' },
		{
			text: 'Skill Enhancement: Access training programs, workshops, and practical on-the-job experiences.'
		},
		{
			text: 'Job Search Assistance: Receive tailored support to find roles that match your skills and availability.'
		},
		{ text: 'Flexible shift pattern to suit your life style and commitment ' },
		{ text: 'Full time or part time work' },
		{ text: 'Competitive Pay: Earn attractive rates and benefits.' },
		{
			text: 'Diverse Work Settings: Gain experience across various clinical and community environments.'
		},
		{ text: '24/7 On-Call Support: Stay connected for assistance and guidance whenever needed.' }
	];
</script>

{#snippet Image()}
	<img
		src="/medical/2.avif"
		alt="Healthcare worker assisting an elderly patient"
		class="special h-full w-full object-cover object-left-top"
	/>
{/snippet}

{#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm link="/contact#join-us" text="Register With Us Today" />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet ImagesSection()}
	<div class="relative mx-auto max-w-xl">
		<div class="relative z-0">
			<img
				src="/medical/3.avif"
				alt="Team of medical professionals"
				class="h-auto w-full rounded-md object-cover shadow-lg"
			/>
		</div>

		<div
			class="absolute top-[67%] right-[-3%] z-10 w-[85%] sm:right-[-8%] sm:bottom-[-10%] sm:w-[75%] md:right-[10%] md:bottom-[-15%] md:w-[70%]"
		>
			<img
				src="/medical/4.avif"
				alt="Medical professional in hallway"
				class="h-auto w-full rounded-md border-4 border-white object-cover shadow-xl"
			/>
		</div>

		<div class="hidden md:absolute md:bottom-[-35%] md:left-0 md:block">
			<svg
				width="79"
				height="78"
				viewBox="0 0 79 78"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path
					d="M4.46706 35.5715C4.25238 39.7756 5.76183 44.043 8.26978 47.3696C9.4396 48.9004 10.7666 50.2658 12.3819 51.3276C13.8882 52.3385 15.7352 52.9907 17.4966 53.4567C18.3366 53.6773 19.2015 53.8165 20.0387 53.9295C21.1187 54.0631 22.1987 54.1968 23.2759 54.2229C25.349 54.2502 27.4027 53.5245 29.2081 52.5629C31.1197 51.5448 32.834 50.1818 34.3841 48.7154C36.0915 47.0835 37.7396 45.2379 39.0069 43.2405C40.2991 41.1617 41.016 38.6941 40.4419 36.2597C39.93 34.1467 38.3794 32.4641 36.2401 31.954C34.3173 31.4922 32.2029 31.9504 30.5327 32.9354C28.7039 34.0321 27.4848 35.8129 26.9457 37.8723C25.8193 42.2076 27.6144 47.1135 30.2065 50.5725C31.5446 52.3682 33.2551 53.9928 35.2993 54.9898C37.5063 56.0364 39.9063 56.2168 42.2869 55.6441C44.8275 55.0135 47.2177 53.7677 49.5017 52.5785C51.9718 51.3037 54.3855 49.9228 56.6873 48.3832C61.2924 45.3579 65.6161 41.8553 69.5536 37.9859C70.5245 37.0189 71.4416 36.0533 72.3573 35.0339C73.1682 34.1249 71.7888 32.8147 70.9779 33.7237C64.0685 41.33 55.6969 47.5474 46.4451 51.9843C44.4245 52.9515 42.244 53.9765 39.9861 54.0885C37.9972 54.1936 36.095 53.489 34.5308 52.3181C32.8837 51.0687 31.5498 49.4344 30.562 47.6566C29.6032 45.9588 28.854 44.0403 28.6151 42.0817C28.3777 40.1769 28.5692 38.2073 29.5483 36.5134C30.3467 35.1202 31.8832 34.1656 33.461 33.775C34.9865 33.4397 36.581 33.6947 37.6321 34.798C38.933 36.1909 38.9346 38.344 38.3872 40.0806C37.6909 42.3055 35.9629 44.1801 34.4459 45.8879C33.1895 47.2659 31.769 48.5406 30.2367 49.6566C28.7031 50.7188 27.0315 51.6501 25.186 52.1013C23.4206 52.5236 21.5557 52.2217 19.7736 51.9985C18.0991 51.7725 16.4977 51.2486 14.9459 50.5618C13.4769 49.9537 12.3775 49.067 11.245 47.9388C8.75783 45.4192 6.9726 41.9394 6.47702 38.3726C6.3445 37.4071 6.32095 36.4926 6.3498 35.523C6.36353 35.0113 5.86831 34.5934 5.38418 34.6059C4.84626 34.6197 4.4808 35.0598 4.46706 35.5715Z"
					fill="black"
				/>
				<path
					d="M52.9293 39.3561C57.3263 37.6549 61.8405 36.3275 66.4431 35.2938C67.5672 35.0496 68.7451 34.8039 69.8706 34.6134C70.2996 34.5485 70.8071 34.4009 71.2664 34.4698C71.5906 34.5153 71.5933 34.6229 71.5479 34.947C71.5065 35.4326 71.3838 35.8933 71.2611 36.354C71.1412 36.9222 70.9675 37.4919 70.7937 38.0616C70.4987 39.1457 70.1223 40.2051 69.6921 41.2658C68.7296 43.6052 67.523 45.8701 66.1232 47.9517C65.8378 48.3627 66.0972 49.0289 66.533 49.233C67.0241 49.4895 67.5303 49.288 67.8143 48.8232C69.1879 46.7693 70.3697 44.5857 71.306 42.2739C71.7872 41.1042 72.2408 39.9083 72.5869 38.7151C72.9082 37.6034 73.2818 36.4365 73.4141 35.3027C73.5698 34.0337 73.029 32.8903 71.7296 32.6008C70.5393 32.3623 69.2042 32.7735 68.0525 32.9916C65.5353 33.4871 63.021 34.0901 60.5631 34.7993C57.8115 35.5969 55.0889 36.4745 52.4242 37.512C51.9442 37.6858 51.634 38.1783 51.7816 38.6858C51.8755 39.1948 52.4769 39.5561 52.9293 39.3561Z"
					fill="black"
				/>
			</svg>
		</div>
	</div>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

<SplitSection
	title="Support Workers"
	label1="Support Workers"
	introParagraph="Join Central Staffing as a Support Worker and make a positive impact in the lives of individuals across the UK. Whether you’re seeking full-time or part-time roles, we provide rewarding opportunities that align with your availability and career goals."
	listItemsExplainer="You will benefit from the following:"
	{listItems}
	buttomCTA={BottomFooter}
	images={ImagesSection}
	imagePosition="right"
/>

<Subscribe />

<style>
	.special {
		z-index: -20;
		border: solid 1px#FFFAFF;
		border-radius: 20px;
	}
</style>
