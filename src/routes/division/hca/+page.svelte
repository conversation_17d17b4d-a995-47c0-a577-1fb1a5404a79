<script lang="ts">
	import { getContext } from 'svelte';
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	import type { ExternalLinksConfig } from '../../../app';

	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;

	import SplitSection from '$lib/components/shared/SplitSection.svelte';

	const h1 = `Flexible HealthCare <br /> Roles Managed To <br /> Suit Your Availability `;

	const p =
		'Join a trusted firm that values your time, supports your growth, and connects you with rewarding roles that fit your life.';

	const listItems: ListItem[] = [
		{ text: 'Exclusive one to one support with your consultant' },
		{ text: 'On the job search' },
		{ text: 'Improve and develop skills and competencies on the job, trainings, workshnops' },
		{ text: 'Flexible shift pattern to suit your life style and commitment ' },
		{ text: 'Full time or part time work' },
		{ text: 'Earn fantastic rates and competitively' },
		{ text: 'Work in a variety of different clinical settings with regular shifts' },
		{ text: '24hrs on call support services' }
	];
</script>

{#snippet Image()}
	<img
		src="/medical/hca-hero.avif"
		alt="Healthcare worker assisting an elderly patient"
		class="special h-full w-full object-cover"
	/>
{/snippet}

{#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm link={externalLinks?.applicationFormHCA?.url} />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet ImagesSection()}
	<div class="special-grid h-full w-full gap-3 md:gap-4">
		<img
			src="/medical/04.avif"
			alt="Healthcare scene 1"
			class="o1 h-full max-h-[311.64px] w-full rounded-lg object-cover"
		/>
		<img
			src="/medical/05.avif"
			alt="Healthcare scene 2"
			class="o2 h-full max-h-[311.64px] w-full rounded-lg object-cover"
		/>
		<img
			src="/medical/06.avif"
			alt="Healthcare scene 3"
			class="o3 h-full max-h-[311.64px] w-full rounded-lg object-cover"
		/>
		<img
			src="/medical/07.avif"
			alt="Healthcare scene 4"
			class="o4 h-full max-h-[311.64px] w-full rounded-lg object-cover"
		/>
	</div>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

<SplitSection
	label1="Healthcare Assistant"
	introParagraph="At central staffing we have exciting firm opportunities for Healthcare Assistants within
					the UK. Join our team and our experienced consultants will manage your availabilities to
					suit your life commitment and you will benefit from the following: -"
	{listItems}
	buttomCTA={BottomFooter}
	images={ImagesSection}
	imagePosition="left"
/>

<Subscribe />

<style>
	.special {
		z-index: -20;
		border: solid 1px#FFFAFF;
		border-radius: 20px;
	}

	.special-grid {
		/* make a 2 x 2 grid */
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-template-rows: repeat(2, 1fr);
		gap: 20.9px;
	}

	.o1 {
		border-top-left-radius: 133.86px;
		object-position: left center;
		object-fit: cover;
	}

	.o2 {
		border-radius: 133.86px;

		object-position: center center;
		object-fit: cover;
	}

	.o3 {
		border-bottom-right-radius: 133.86px;
		border-bottom-left-radius: 133.86px;
		object-position: center center;
		object-fit: cover;
	}

	.o4 {
		border-top-right-radius: 133.86px;
		border-bottom-left-radius: 133.86px;
		border-bottom-right-radius: 133.86px;
		object-position: center center;
	}
</style>
