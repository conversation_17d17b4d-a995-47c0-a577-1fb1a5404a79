<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import ServicesRequestCall from '$lib/components/shared/ServicesRequestCall.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	import SplitSection from '$lib/components/shared/SplitSection.svelte';
	const h1 = `Tailored Staffing<br /> Solutions For <br /> Your Business <br /> Success`;

	const p =
		'Providing skilled medical professionals to support hospitals, care homes, and healthcare facilities with efficiency and reliability.';

	const listItems: ListItem[] = [
		{ text: 'Human Resources, Recruitment Consultant, Fiance/Account' },
		{
			text: 'Sales & Retail, Call Centres, Marketing, Medical Administrators/Front Desk Personnel, Banking'
		},
		{
			text: 'Office Administrators/Front Desk Personnel/PA’s, Junior/Senior Management Roles, IT/Project Managers '
		}
	];
</script>

{#snippet Image()}
	<img
		src="/commercial/01.avif"
		alt="Healthcare worker assisting an elderly patient"
		class="special h-full w-full object-cover object-left"
	/>
{/snippet}

{#snippet BottomFooter()}
	<div class="mt-2 flex w-full flex-col justify-between gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm link="/contact#join-us" text="Register With Us Today" />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet ImagesSection()}
	<div class="relative mx-auto h-full">
		<div class="relative z-0 h-full">
			<img
				src="/commercial/02.avif"
				alt="Team of medical professionals"
				class="h-full w-full rounded-[40px] object-cover object-center"
			/>
		</div>
	</div>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

<section class="bg-white py-16 md:mt-20 md:pt-8 md:pb-4">
	<div class="central-p mx-auto px-4">
		<div class="grid grid-cols-1 gap-10 lg:grid-cols-2 lg:gap-12 xl:gap-16">
			<div class="flex h-full flex-col gap-[26px]">
				<p class="mt-auto mb-3 text-base font-semibold tracking-wider text-orange-600 uppercase">
					COMMERCIAL
				</p>

				<p class="intro text-justify text-base text-[#151414]">
					Central staffing specialises in various areas of commercial recruitment. Our team of
					experience consultants delivers a professional and personalised services.  Our main
					objective is  partnering with businesses to understand their business needs  that enables
					us to tailor a personalised approach  to their staffing solutions, ensuring we match the
					right candidate that meet client’s business needs.  Whatever your staffing needs, temp to
					permanent, contract or to manage your entire recruitment/hire process or recruitment
					advice, our specialist recruitment consultants will work with you to achieve your goals.
				</p>

				<p class="text-xl font-bold text-[#04091B]">Check Out The Job Categories</p>

				<ul class="staffing-ul mb-6 flex flex-col gap-[23px] space-y-3">
					{#each listItems as item (item)}
						<li class="flex max-w-[54ch] items-start space-x-3">
							<span class="flex h-5 w-5 flex-shrink-0 items-center justify-center bg-[#240951]">
								<svg class="h-[21px] w-[21px] text-white" fill="currentColor" viewBox="0 0 20 20"
									><path
										fill-rule="evenodd"
										d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
										clip-rule="evenodd"
									></path></svg
								>
							</span>
							<span class="font-xs font-medium text-[#151414]">{item.text}</span>
						</li>
					{/each}
				</ul>
			</div>
			<div>{@render ImagesSection()}</div>
		</div>
		{@render BottomFooter()}
	</div>
</section>

<ServicesRequestCall />

<Subscribe />

<style>
	.special {
		z-index: -20;
		border: solid 1px#FFFAFF;
		border-radius: 20px;
	}
</style>
