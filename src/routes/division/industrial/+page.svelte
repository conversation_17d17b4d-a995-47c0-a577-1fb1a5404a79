<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import ServicesRequestCall from '$lib/components/shared/ServicesRequestCall.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	const h1 = `Powering Industrial<br /> Workforces With<br />Precision And <br /> Compliance`;

	const p =
		'Providing skilled medical professionals to support hospitals, care homes, and healthcare facilities with efficiency and reliability.';

	const listItems: ListItem[] = [
		{ text: 'Warehousing' },
		{
			text: 'Cleaners/House Keepers'
		},
		{
			text: 'Kitchen Assistant'
		},
		{
			text: 'Potters/Laundry Assistants'
		}
	];
</script>

{#snippet Image()}
	<div class="mx-auto w-full max-w-xl">
		<div class="flex items-start gap-4 md:gap-6">
			<div class="flex w-1/2 flex-col gap-4 md:gap-6">
				<img
					src="/industrial/01.avif"
					alt="Industrial workers shaking hands"
					class="aspect-square h-auto w-full rounded-2xl object-cover shadow-md"
				/>
				<img
					src="/industrial/03.avif"
					alt="Workers with clipboard"
					class="aspect-square h-auto w-full rounded-2xl object-cover shadow-md"
				/>
			</div>

			<div class="mt-8 flex w-1/2 flex-col gap-4 md:mt-12 md:gap-6 lg:mt-16">
				<img
					src="/industrial/02.avif"
					alt="Warehouse meeting"
					class="aspect-square h-auto w-full rounded-2xl object-cover shadow-md"
				/>
				<img
					src="/industrial/04.avif"
					alt="Safety check discussion"
					class="aspect-square h-auto w-full rounded-2xl object-cover shadow-md"
				/>
			</div>
		</div>
	</div>
{/snippet}

{#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet Icon()}
	<span
		class="ml-2 inline-flex items-center justify-center rounded-full text-white transition-transform duration-200 group-hover:translate-x-1"
	>
		<svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<circle cx="16.5" cy="16.5" r="16.5" fill="#5D28B6" />
			<path
				d="M10 24L23 11M23 11V23.48M23 11H10.52"
				stroke="white"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
		</svg>
	</span>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

<div class="py-16 text-white md:py-24">
	<h2 class="text-center text-3xl font-bold text-[#0586AB] md:text-4xl lg:text-5xl">
		Register With Us Right Here
	</h2>

	<section class="mt-[48px] bg-[#5D28B6]">
		<div class="central-p container mx-auto">
			<div class="grid grid-cols-1 items-start gap-10 lg:grid-cols-2 lg:gap-12 xl:gap-16">
				<div class="flex flex-col gap-[24px]">
					<p class="mb-2 text-sm font-bold tracking-wider uppercase">INDUSTRIAL</p>
					<p class="mb-6 max-w-[54ch] text-sm leading-relaxed">
						We offer honest and transparent services and we are committed to deliver what we promise
					</p>

					<p class="mb-3 text-base font-semibold text-white">Job Categories</p>
					<ul class="staffing-ul mb-6 flex flex-col gap-[23px] space-y-3">
						{#each listItems as item (item)}
							<li class="flex items-start space-x-3">
								<span class="flex h-5 w-5 flex-shrink-0 items-center justify-center">
									<svg
										width="22"
										height="22"
										viewBox="0 0 22 22"
										fill="none"
										xmlns="http://www.w3.org/2000/svg"
									>
										<path
											d="M1.66667 0.5H20.3333C20.6428 0.5 20.9395 0.622916 21.1583 0.841709C21.3771 1.0605 21.5 1.35725 21.5 1.66667V20.3333C21.5 20.6428 21.3771 20.9395 21.1583 21.1583C20.9395 21.3771 20.6428 21.5 20.3333 21.5H1.66667C1.35725 21.5 1.0605 21.3771 0.841709 21.1583C0.622916 20.9395 0.5 20.6428 0.5 20.3333V1.66667C0.5 1.35725 0.622916 1.0605 0.841709 0.841709C1.0605 0.622916 1.35725 0.5 1.66667 0.5ZM9.83683 15.6667L18.0852 7.41717L16.4367 5.7675L9.83683 12.3673L6.53633 9.06683L4.88667 10.7165L9.83683 15.6667Z"
											fill="white"
										/>
									</svg>
								</span>
								<span class="font-xs font-medium text-white">{item.text}</span>
							</li>
						{/each}
					</ul>

					<div>
						<a
							href="/contact#refer-a-friend"
							class="group inline-flex items-center justify-center rounded-full border border-white bg-white px-7 py-3 text-base font-semibold text-[#5D28B6] transition duration-200"
						>
							Refer A Friend
							{@render Icon()}
						</a>
					</div>
				</div>
				<div class="flex flex-col gap-[24px]">
					<h3 class="mb-5 text-2xl leading-snug font-semibold text-white">
						To Be The First To Know About New Job Offers, Please Click Below To Register With Us
						Today
					</h3>

					<div class="mb-8">
						<a
							href="/contact#join-us"
							class="group inline-flex items-center justify-center rounded-full border border-transparent bg-white px-7 py-3 text-base font-medium text-purple-900 transition duration-200 hover:bg-gray-100"
						>
							Register With Us Today
							{@render Icon()}
						</a>
					</div>

					<div class="relative mt-6">
						<img
							src="/industrial/05.avif"
							alt="Industrial team standing in warehouse"
							class="absolute h-auto w-full rounded-lg object-cover shadow-md"
						/>
					</div>
				</div>
			</div>
		</div>
	</section>
</div>

<ServicesRequestCall />

<Subscribe />

<style>
	/* .special {
		z-index: -20;
		border: solid 1px#FFFAFF;
		border-radius: 20px;
	} */
</style>
