<script lang="ts">
	import Subscribe from '$lib/components/home/<USER>';
	import DownloadApplicationForm from '$lib/components/shared/DownloadApplicationForm.svelte';
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ReferAFriend from '$lib/components/shared/ReferAFriend.svelte';
	import ServiceSplitScreen from '$lib/components/shared/ServiceSplitScreen.svelte';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';
	const h1 = `End-To-End <br /> WorkForce Solutions <br />Tailored To Your Care<br /> Settings`;

	const p =
		'From permanent hires to flexible staffing and managed services, we support your workforce needs with precision and care.';

	const secondItems: ListItem[] = [
		{
			text: '24/7 On-Call Availability: Immediate support with our round-the-clock on-call team ready to handle your urgent ad-hoc staffing requirements'
		},
		{
			text: 'Vetted & Compliant Professionals: Access a dedicated pool of specialized medical, allied, and healthcare staff, all fully compliant and vetted for quality.'
		},
		{
			text: 'Rapid & Efficient Placement: Committed to providing exceptional service and consistent staff placement within a short timeframe to meet your critical needs.'
		},
		{
			text: `
"Can-Do" Attitude: Our professionals are selected not only for their skills but also for their proactive and supportive "can-do" approach.`
		},
		{
			text: 'Tailored to Your Needs: We focus on understanding and meeting the specific ad-hoc demands of both NHS Trusts and private healthcare settings.'
		}
	];

	const first = {
		topLabel: 'STAFFING SOLUTION',
		mainHeading: 'Permanent, Temporary, Contract Service',
		subHeadingParagraph: `These services are designed to support NHS hospitals and private health sectors to fill their vacant positions on a short or long term.`,
		items: [
			{
				text: 'Accelerated Hiring: Swiftly connect with ideal candidates from our extensive and pre-vetted talent pool.'
			},
			{
				text: 'Specialized Expertise: Access professionals possessing the precise skills and industry experience your roles demand.'
			},
			{
				text: 'Flexible & Scalable Solutions: From permanent placements to urgent ad-hoc support, we adapt to your evolving needs.'
			},
			{
				text: 'Quality-Assured Talent: Benefit from our rigorous screening process, ensuring reliable and high-caliber professionals.'
			},
			{
				text: 'Dedicated Partnership: We work closely with you, offering personalized support to achieve your strategic staffing goals.'
			}
		]
	};

	const lorem = `We recruit, vet and place medical, healthcare professionals, on a temporary, permanent or contract roles within NHS Trusts, private hospitals, local authorities, learning disability units and nursing / residential settings.  
We acheive this through our candidate database screening, we vet and interview suitable candidates on behalf of clients to fill vacant roles.`;
</script>

<svelte:head>
	<title>Our Staffing & Care Services | Domiciliary, Supported Living & More</title>
	<meta
		name="description"
		content="Explore a full range of services from Central Staffing, including Domiciliary Care, Supported Living, and permanent or temporary staffing for nurses, support workers, and more."
	/>
	<meta
		name="keywords"
		content="domiciliary care services, supported living providers, temporary staffing, permanent recruitment, nurse staffing, support worker agency"
	/>
</svelte:head>

{#snippet Image()}
	<div class="mx-auto aspect-square w-full max-w-xl">
		<div class="grid h-full grid-cols-2 grid-rows-2 gap-4 md:gap-6">
			<img
				src="/service/011.avif"
				alt="Industrial workers shaking hands"
				class="h-full w-full rounded-2xl object-cover shadow-md"
			/>

			<img
				src="/service/012.avif"
				alt="Warehouse meeting"
				class="h-full w-full rounded-2xl object-cover shadow-md"
			/>

			<img
				src="/service/013.avif"
				alt="Workers with clipboard"
				class="h-full w-full rounded-2xl object-cover shadow-md"
			/>

			<img
				src="/service/014.avif"
				alt="Safety check discussion"
				class="h-full w-full rounded-2xl object-cover shadow-md"
			/>
		</div>
	</div>
{/snippet}

<!-- {#snippet Image()}
	<div class="mx-auto w-full max-w-xl">
		<div class="flex items-start gap-4 md:gap-6">
			<div class="flex w-1/2 flex-col gap-4 md:gap-6">
				<img
					src="/industrial/01.avif"
					alt="Industrial workers shaking hands"
					class="aspect-square h-auto w-full rounded-2xl object-cover shadow-md"
				/>
				<img
					src="/industrial/03.avif"
					alt="Workers with clipboard"
					class="aspect-square h-auto w-full rounded-2xl object-cover shadow-md"
				/>
			</div>

			<div class="mt-8 flex w-1/2 flex-col gap-4 md:mt-12 md:gap-6 lg:mt-16">
				<img
					src="/industrial/02.avif"
					alt="Warehouse meeting"
					class="aspect-square h-auto w-full rounded-2xl object-cover shadow-md"
				/>
				<img
					src="/industrial/04.avif"
					alt="Safety check discussion"
					class="aspect-square h-auto w-full rounded-2xl object-cover shadow-md"
				/>
			</div>
		</div>
	</div>
{/snippet} -->

{#snippet BottomFooter()}
	<div class="mt-auto flex w-full flex-col justify-around gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm />
		<ReferAFriend />
	</div>
{/snippet}

{#snippet Icon()}
	<span
		class="ml-2 inline-flex items-center justify-center rounded-full text-white transition-transform duration-200 group-hover:translate-x-1"
	>
		<svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<circle cx="16.5" cy="16.5" r="16.5" fill="#5D28B6" />
			<path
				d="M10 24L23 11M23 11V23.48M23 11H10.52"
				stroke="white"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
		</svg>
	</span>
{/snippet}

<PoorCarousel image={Image} baseClass="bg-[#FFFAFF]" {h1} {p} />

{#snippet FirstImageSection()}
	<div class="h-full w-full">
		<img
			src="/service/nurses.avif"
			alt="Healthcare scene 1"
			class="o1 h-full w-full object-cover"
		/>
	</div>
{/snippet}

{#snippet ImageCollageThree({
	imgLeftSrc = '/medical/04.avif',
	imgLeftAlt = 'Main image',
	imgTopRightSrc = '/medical/04.avif',
	imgTopRightAlt = 'Top right image',
	imgBottomRightSrc = '/medical/04.avif',
	imgBottomRightAlt = 'Bottom right image',
	// Optional classes for customization
	containerClass = 'w-full max-w-2xl mx-auto', // Example container class
	borderClass = '', // Example border
	gapClass = 'gap-2 md:gap-3' // Example gap
} = {})}
	<div class="{containerClass} {borderClass} aspect-[4/3] p-1 md:p-2">
		<div class="flex h-full w-full {gapClass}">
			<div class="h-full w-[45%] flex-shrink-0">
				<img src={imgLeftSrc} alt={imgLeftAlt} class="h-full w-full object-cover" />
			</div>

			<div class="flex h-full w-[55%] flex-grow flex-col {gapClass}">
				<img
					src={imgTopRightSrc}
					alt={imgTopRightAlt}
					class="h-1/2 w-full flex-grow object-cover"
				/>
				<img
					src={imgBottomRightSrc}
					alt={imgBottomRightAlt}
					class="h-1/2 w-full flex-grow object-cover"
				/>
			</div>
		</div>
	</div>
{/snippet}
{#snippet ImageCollageTopWide({
	imgTopSrc = '/service/04.png',
	imgTopAlt = 'Top wide image',
	imgBottomLeftSrc = '/service/05.png',
	imgBottomLeftAlt = 'Bottom left image',
	imgBottomRightSrc = '/service/06.png',
	imgBottomRightAlt = 'Bottom right image',

	containerClass = 'w-full max-w-2xl mx-auto',
	borderClass = '',
	gapClass = 'gap-2 md:gap-3',
	bottomRowHeight = '12rem' // Default height for the bottom row (e.g., h-48 in Tailwind)
	// You can pass Tailwind theme values like theme('spacing.48') if configured,
	// or simple CSS values like '192px', '12rem', etc.
} = {})}
	<div class="{containerClass} {borderClass} p-1 md:p-2">
		<div
			class="grid grid-cols-2 {gapClass} w-full"
			style="grid-template-rows: auto {bottomRowHeight};"
		>
			<img src={imgTopSrc} alt={imgTopAlt} class="col-span-2 h-auto w-full object-cover" />

			<img
				src={imgBottomLeftSrc}
				alt={imgBottomLeftAlt}
				class="col-span-1 h-full w-full object-cover"
			/>

			<img
				src={imgBottomRightSrc}
				alt={imgBottomRightAlt}
				class="col-span-1 h-full w-full object-cover"
			/>
		</div>
	</div>
{/snippet}

{#snippet FirstFooter()}
	<div class="flex w-full flex-col justify-between gap-4 pt-6 sm:flex-row">
		<DownloadApplicationForm link="/contact#callback" text="Request A Callback" />
		<ReferAFriend />
	</div>
{/snippet}

<ServiceSplitScreen
	topLabel={first.topLabel}
	mainHeading={first.mainHeading}
	subHeadingParagraph={first.subHeadingParagraph}
	textParagraph1={lorem}
	textParagraph2=""
	listHeading="Opportunities await you"
	listItems={first.items}
	imagePosition="left"
	imageSnippet={FirstImageSection}
	id="staffing"
	bottomFooter={FirstFooter}
/>

{#snippet FifthImageSection()}
	<div class="h-full w-full">
		<img src="/service/11.png" alt="Healthcare scene 1" class="o1 h-full w-full object-cover" />
	</div>
{/snippet}

<ServiceSplitScreen
	topLabel="Domiciliary Care"
	mainHeading="Domiciliary Care"
	subHeadingParagraph="Our Domiciliary Care staffing solutions are designed to provide high-quality, compassionate care professionals who support individuals in the comfort and familiarity of their own homes."
	textParagraph1="We understand the critical importance of maintaining independence and dignity, and our roster of carers is meticulously selected, trained, and vetted to offer a comprehensive range of personalized support"
	textParagraph2="Whether your organization requires staff for assistance with daily living, personal care, medication reminders, companionship, or more complex needs, we connect you with reliable and skilled care professionals. We are dedicated to matching the right staff to your specific service user requirements, ensuring peace of mind and the delivery of exceptional, person-centered care."
	listHeading="Expect:"
	listItems={[
		{
			text: 'Access to a network of highly trained, fully vetted, and compassionate domiciliary care professionals.'
		},
		{
			text: 'Personalized staffing solutions designed to precisely match specific individual needs and care plan preferences.'
		},
		{
			text: 'Flexible support options, providing staff for personal care, daily assistance, companionship, and specialized conditions.'
		},
		{
			text: 'Our candidates are dedicated to promoting independence, dignity, and enhancing the well-being of individuals in their own homes.'
		},
		{
			text: 'Reliable and consistent care staff available for short-term, long-term, or emergency cover assignments.'
		}
	]}
	imagePosition="left"
	imageSnippet={FifthImageSection}
	id="domiciliary-care"
	bottomFooter={FirstFooter}
/>

{#snippet SixthImageSection({
	imgTopSrc = '/service/2.avif',
	imgTopAlt = '',
	imgBottomSrc = '/service/4.avif',
	imgBottomAlt = '',

	// Optional classes for styling the container and gap
	containerClass = 'w-full max-w-3xl mx-auto', // Adjust max-width as needed
	imageClass = 'w-full h-auto object-cover rounded-lg shadow-md', // Common styling for both images
	gapClass = 'gap-4 md:gap-6' // Gap between the two images
} = {})}
	<div class="{containerClass} p-2 md:p-4">
		<div class="flex flex-col {gapClass}">
			{#if imgTopSrc}
				<div>
					<img src={imgTopSrc} alt={imgTopAlt} class={imageClass} />
				</div>
			{/if}
			{#if imgBottomSrc}
				<div>
					<img src={imgBottomSrc} alt={imgBottomAlt} class={imageClass} />
				</div>
			{/if}
		</div>
	</div>
{/snippet}

<ServiceSplitScreen
	topLabel="SUPPORTED LIVING"
	mainHeading="SUPPORTED LIVING"
	subHeadingParagraph="Our Supported Living staffing services focus on connecting you with skilled and empathetic support workers who are dedicated to empowering individuals with diverse needs to live as independently and fully as possible."
	textParagraph1="We specialize in providing staff who excel in fostering person-centered environments, enabling service users to achieve their personal goals, develop life skills, and integrate into their communities."
	textParagraph2="From providing assistance with daily tasks, household management, and financial budgeting, to offering support in accessing education, employment, and social activities, our support workers are committed to promoting choice, control, and inclusion. We ensure you receive dedicated professionals who help create safe, nurturing, and empowering living experiences for the individuals you support."
	listHeading="Expect:"
	listItems={[
		{
			text: 'Access to empathetic and skilled support workers, rigorously vetted and trained in promoting independence.'
		},
		{
			text: 'Person-centered staffing matched to the unique needs, preferences, and aspirations of each individual.'
		},
		{
			text: 'Provision of staff experienced in supporting daily living skills, community engagement, and personal development.'
		},
		{
			text: 'Our candidates champion choice, control, and an enhanced quality of life for individuals in supported living settings.'
		},
		{
			text: 'Flexible and reliable staffing solutions to meet varied support requirements and ensure consistent, high-quality assistance.'
		}
	]}
	imagePosition="right"
	imageSnippet={SixthImageSection}
	id="supported-living"
	bottomFooter={FirstFooter}
/>

<!-- {#snippet SecondImageSection()}
	{@render ImageCollageThree({
		imgLeftSrc: '/service/01.png',
		imgTopRightSrc: '/service/02.png',
		imgBottomRightSrc: '/service/03.png'
	})}
{/snippet} -->

<!-- <ServiceSplitScreen
	topLabel="AD-HOC SUPPORT SERVICES"
	mainHeading="Ad-hoc Staff Support Services"
	subHeadingParagraph="We have a pool of vetted specialised professionals who have met all compliance with a can do attitude ready to support our client’s needs."
	textParagraph1="We understand the increasing demand of medical, allied and healthcare professionals with the Trust and private health sitting, with ad-hoc staffing support we are committed to efficiency and consistence in staff placement."
	textParagraph2="We pride ourselves in providing an exceptional service within a short time frame to meeting every client's needs. To support this service, we have 24hr on call staff at hand to deal with any ad-hoc staffing need."
	listHeading="Opportunities await you"
	listItems={secondItems}
	imagePosition="right"
	imageSnippet={SecondImageSection}
	bottomFooter={FirstFooter}
	id="support"
/> -->
<!-- 
<ServiceSplitScreen
	topLabel="PERMANENT, TEMPORARY, CONTRACT"
	mainHeading="Recruitment Outsourced Services"
	subHeadingParagraph="This service is designed to support all business sectors to fill their permanent, temporary and contract positions."
	textParagraph1="We support you to manage the entire recruitment/hire process or part of your recruitment process, through partnership approach."
	textParagraph2="First we will meet with you to review your business, processes to understand your staffing requirements. We support you to manage your candidate search through advertising and database search to attract the best candidate that meets your requirements."
	listHeading="The following are services we could support clients depending on their requirements:"
	listItems={[
		{ text: 'Candidate Search' },
		{ text: 'Selecting / Shortlisting and Interviewing' },
		{ text: 'Interviewing' },
		{ text: 'Compliant tracking (Screening / vetting)' },
		{
			text: 'Candidate Search and Shortlisting only? We will schedule interviews with candidates for you'
		},
		{ text: 'Just Shortlisting? We are at your service' },
		{
			text: 'Work with your Resourcing department to improve recruitment process and recruiting results'
		}
	]}
	imagePosition="left"
	imageSnippet={ImageCollageTopWide}
	id="outsource"
	bottomFooter={FirstFooter}
/> -->

<!-- {#snippet ThirdImageSection()}
	<div class="h-full w-full">
		<img src="/service/07.png" alt="Healthcare scene 1" class="o1 h-full w-full object-cover" />
	</div>
{/snippet} -->

<!-- <ServiceSplitScreen
	topLabel="OPEN DAY SERVICES"
	mainHeading="Open Day Services"
	subHeadingParagraph="We also specialise in hosting recruitment open days, where a pool of pre-screened candidates are invited to meet with the employer to compare candidate's skills and competencies."
	textParagraph1="We will facilitate the day’s activities making sure you have a wide range of skills to choose from."
	textParagraph2="Whichever service you choose we will be ready to support you to achieve the following benefits:"
	listHeading=""
	listItems={[
		{ text: 'Reduced recruitment cost' },
		{ text: 'Increased candidate quality' },
		{ text: 'Efficiency and Consistency in your recruit to hire process' },
		{ text: 'Increased satisfaction and central recruitment point for all departments' },
		{ text: 'Measurable Recruitment capacity' }
	]}
	imagePosition="right"
	imageSnippet={ThirdImageSection}
	bottomFooter={FirstFooter}
	id="open-day"
/> -->

{#snippet FourthImageSection()}
	{@render ImageCollageThree({
		imgLeftSrc: '/service/08.png',
		imgTopRightSrc: '/service/09.png',
		imgBottomRightSrc: '/service/10.png'
	})}
{/snippet}

<ServiceSplitScreen
	topLabel="MANAGED SERVICES"
	mainHeading="Managed Services"
	subHeadingParagraph="Our managed services support clients in finding staffing solution through partnering and fully understanding client day to day challenging staff needs."
	textParagraph1="This ranges from long or short time sickness absence, unplanned leave that leaves units and departments short staffed, to reduced time spent in recruiting and hiring new staff."
	textParagraph2={`We support client to bridge this through ad-hoc, contracts, Temp to Perm or bank staffing support on an agreed fill rate with our vest experience and knowledge of main stream recruitment in attracting and retaining the best talent.

Your Staffing needs will be professionally managed and tailored to your specific requirement, giving you the same satisfaction and safety as you would your permanent staff
We have on our database professionals who have gone through our robust recruitment process and are compliant ready to be placed on ad-hoc, contract, or temporary positions.

`}
	listHeading="In partnering with you, we would provide you with following services:"
	listItems={[
		{ text: 'Create a recruitment plan and delivery schedule.' },
		{ text: 'Headhunting/selecting candidates using our extensive candidate database/network.' },
		{ text: 'A dedicated account manager as client liaison' },
		{ text: 'Regular contact with your HR and Hiring Manager' },
		{ text: 'Responses to vacancies within agreed time scales' },
		{ text: 'Regular review meetings to monitor performance' }
	]}
	imagePosition="right"
	imageSnippet={FourthImageSection}
	bottomFooter={FirstFooter}
	id="managed"
/>

<Subscribe />

<style>
	/* .special {
		z-index: -20;
		border: solid 1px#FFFAFF;
		border-radius: 20px;
	} */
</style>
