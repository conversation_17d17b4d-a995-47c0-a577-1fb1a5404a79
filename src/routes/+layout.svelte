<script lang="ts">
	import { page } from '$app/state';
	import MainLayout from '$lib/components/shared/Layout.svelte';
	import DomiciliaryLayout from '$lib/components/tenants/DomiciliaryLayout.svelte';
	import SupportedLayout from '$lib/components/tenants/SupportedLayout.svelte';
	import Footer from '$lib/components/home/<USER>';
	import '../app.css';
	import { setContext, type Snippet } from 'svelte';
	import type { PageServerData } from './$types';

	let {
		children,
		data
	}: {
		children: Snippet<unknown[]>;
		data: PageServerData;
	} = $props();

	let routeId = $derived(page.url.pathname);
	let pageIsAdmin = $derived(page.url.pathname.startsWith('/admin'));
	let tenant = $derived(data.tenant);

	setContext('appConfig', data?.appConfig);
</script>

{#if tenant === 'domiciliary'}
	<DomiciliaryLayout>
		{@render children()}
	</DomiciliaryLayout>
{:else if tenant === 'supported'}
	<SupportedLayout>
		{@render children()}
	</SupportedLayout>
{:else}
	<MainLayout {routeId}>
		{@render children()}
		{#if !pageIsAdmin}
			<Footer />
		{/if}
	</MainLayout>
{/if}
