<script lang="ts">
	import { page } from '$app/state';
	import { menuItems } from '$lib/menuItems.svelte';
	import Logo from './Logo.svelte';
	import MobileMenu from './MobileMenu.svelte';
	import DesktopMenuItem from './DesktopMenuItem.svelte';
	import { getContext } from 'svelte';

	let { routeId = $bindable(''), children }: { routeId: string; children: any } = $props();
	let appConfig = getContext('appConfig');
	let externalLinks = $derived(appConfig.externalLinks);
	let pageIsAdmin = $derived(page.url.pathname.startsWith('/admin'));

	let isAdded = $state(false);
	$effect(() => {
		if (!externalLinks.privacyPolicy?.url) {
			return;
		}

		if (isAdded) {
			return;
		}

		$menuItems = [
			...$menuItems,
			{
				label: 'About Us',
				href: '/about-us',
				children: [
					{
						label: 'Privacy Policy',
						href: externalLinks.privacyPolicy?.url,
						routeId: 'privacy'
					},
					{
						label: 'Terms of Use',
						href: externalLinks.termsOfUse?.url,
						routeId: 'terms'
					},
					{
						label: 'Cookie Policy',
						href: externalLinks.cookiesPolicy?.url,
						routeId: 'cookies'
					},
					{
						label: 'Feedback Form',
						href: '/contact#feedback',
						routeId: 'about'
					},
					{
						label: 'Modern Slavery',
						href: externalLinks.modernSlaveryStatement?.url,
						routeId: 'slavery-prevention'
					}
				],

				routeId: 'about-us'
			}
		];

		isAdded = true;
	});
</script>

<div id="layout" class="central-p">
	<a href="/" class="logo">
		<Logo />
	</a>

	{#if !pageIsAdmin}
		<div class="mobile">
			<MobileMenu {routeId} />
		</div>

		<div class="web">
			<ul>
				{#each $menuItems as menu (menu.routeId)}
					<DesktopMenuItem {menu} {routeId} />
				{/each}
				<li><button class="join-button"> Join Now </button></li>
			</ul>
		</div>
	{/if}
</div>

<div class="main-wrapper">
	{@render children()}
</div>

<style>
	.main-wrapper {
		margin: 0 auto;
		max-width: 100vw;
	}

	#layout {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid transparent; /* Start transparent */
	}

	.logo {
		max-width: 140px;
	}

	.mobile {
		display: block;
		@media (min-width: 900px) {
			display: none;
		}
	}

	.web {
		display: none;
		font-family: 'Mukta', sans-serif;
		font-weight: 400;
		/* Fluid Font Size: Target 18px (1.125rem) at 1440px, min 16px (1rem), max 20px (1.25rem) */
		font-size: clamp(1rem, 0.75rem + 0.42vw, 1.25rem);
		color: #828282;
		@media (min-width: 900px) {
			display: block;
		}
	}

	ul {
		display: flex;
		/* Fluid Gap: Target 1.68rem at 1440px? Min 1rem? Max 2rem? */
		gap: clamp(1rem, 0.5rem + 1.2vw, 2rem);
		list-style: none;
		margin: 0;
		padding: 0;
	}
	li {
		position: relative;
		cursor: pointer;

		transition: color 0.3s ease;
	}

	/* Base styles for the underline pseudo-element */
	li::after {
		content: '';
		display: block;
		position: absolute;
		bottom: -0.5rem;
		left: 0;
		width: 0;
		height: 2px;
		background-color: var(--brand-purple);
		margin-top: 0.5rem;
		transition: width 0.3s ease-in-out;
	}

	li:hover::after {
		width: 100%;
	}

	li a {
		text-decoration: none;
		color: inherit;
	}

	.active {
		font-weight: 600;
		color: var(--brand-purple);
	}

	/* Styles for the active underline */
	.active::after {
		width: 100%;
		background-color: var(--brand-purple);
	}

	button {
		margin-left: clamp(1.5rem, -0.5rem + 3vw, 4rem);
		background-color: var(--brand-purple);
		color: #fff;

		height: 38px;
		border-radius: 40px;
		border: none;

		padding: clamp(4px, 0.1rem + 0.2vw, 6px) clamp(15px, 0.5rem + 1vw, 25px);
		font-family: 'Mukta', sans-serif;
		font-weight: 500;

		font-size: clamp(1rem, 0.75rem + 0.42vw, 1.25rem);

		line-height: 1.4;
		letter-spacing: 0%;
		cursor: pointer;
	}
</style>
