<script lang="ts">
	import type { Snippet } from 'svelte';

	type Types = {
		baseClass?: string;
		image?: Snippet;
		h1: string;
		p: string;
		ctaText?: string;
		ctaLink?: string;
		showSecond?: boolean;
	};
	let {
		baseClass = $bindable('bg-white'),
		image,
		h1,
		p,
		ctaText = 'Apply Now',
		ctaLink = '/contact#job-application',
		showSecond = true
	}: Types = $props();
</script>

<section class={['relative m-2', baseClass]}>
	<div class="call">
		<span> Call Us Now -</span>
		<!-- <span
			><svg
				width="18"
				height="18"
				viewBox="0 0 18 18"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path
					d="M14.9625 15.75C13.4 15.75 11.8562 15.4095 10.3312 14.7285C8.80625 14.0475 7.41875 13.0817 6.16875 11.8312C4.91875 10.5807 3.95325 9.19325 3.27225 7.66875C2.59125 6.14425 2.2505 4.6005 2.25 3.0375C2.25 2.8125 2.325 2.625 2.475 2.475C2.625 2.325 2.8125 2.25 3.0375 2.25H6.075C6.25 2.25 6.40625 2.3095 6.54375 2.4285C6.68125 2.5475 6.7625 2.688 6.7875 2.85L7.275 5.475C7.3 5.675 7.29375 5.84375 7.25625 5.98125C7.21875 6.11875 7.15 6.2375 7.05 6.3375L5.23125 8.175C5.48125 8.6375 5.778 9.08425 6.1215 9.51525C6.465 9.94625 6.84325 10.362 7.25625 10.7625C7.64375 11.15 8.05 11.5095 8.475 11.841C8.9 12.1725 9.35 12.4755 9.825 12.75L11.5875 10.9875C11.7 10.875 11.847 10.7907 12.0285 10.7347C12.21 10.6788 12.388 10.663 12.5625 10.6875L15.15 11.2125C15.325 11.2625 15.4688 11.3532 15.5812 11.4847C15.6937 11.6163 15.75 11.763 15.75 11.925V14.9625C15.75 15.1875 15.675 15.375 15.525 15.525C15.375 15.675 15.1875 15.75 14.9625 15.75ZM4.51875 6.75L5.75625 5.5125L5.4375 3.75H3.76875C3.83125 4.2625 3.91875 4.76875 4.03125 5.26875C4.14375 5.76875 4.30625 6.2625 4.51875 6.75ZM11.2313 13.4625C11.7188 13.675 12.2157 13.8438 12.7222 13.9688C13.2287 14.0938 13.738 14.175 14.25 14.2125V12.5625L12.4875 12.2062L11.2313 13.4625Z"
					fill="#5D28B6"
				/>
			</svg>
		</span> -->
		<span>01908 915 180</span>
	</div>
	<div class="p0 central-p relative">
		<div class="flex w-full">
			<div class="mx-auto">
				<svg
					width="78"
					height="78"
					viewBox="0 0 78 78"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					class="hidden md:block"
				>
					<path
						d="M3.87714 37.5066C4.20331 33.3097 6.24749 29.2711 9.16129 26.2936C10.5178 24.9254 12.0089 23.7414 13.747 22.8955C15.3705 22.0861 17.2859 21.6761 19.0925 21.4398C19.9539 21.3288 20.8294 21.3016 21.6742 21.2969C22.7624 21.3028 23.8507 21.3088 24.9224 21.421C26.9818 21.6597 28.9255 22.6428 30.5927 23.828C32.3579 25.0828 33.8833 26.6543 35.2326 28.3074C36.7167 30.1447 38.1145 32.1865 39.1152 34.3299C40.1301 36.5572 40.5248 39.0964 39.6433 41.4371C38.8646 43.467 37.1111 44.9369 34.924 45.1684C32.9579 45.3799 30.9197 44.6544 29.3896 43.4633C27.7165 42.1411 26.7358 40.2187 26.4652 38.1072C25.9041 33.6633 28.3134 29.028 31.3276 25.93C32.8849 24.3207 34.7896 22.9289 36.9448 22.2022C39.2678 21.4472 41.6711 21.5761 43.9587 22.4493C46.3974 23.4005 48.6081 24.9425 50.7207 26.4147C53.0071 27.9957 55.2237 29.6747 57.3091 31.4968C61.4883 35.0876 65.3272 39.1157 68.7359 43.458C69.5748 44.5415 70.3605 45.6167 71.138 46.7452C71.8257 47.7507 70.2896 48.8732 69.6019 47.8676C63.7249 39.4382 56.2197 32.1986 47.6132 26.612C45.7333 25.3938 43.7022 24.0976 41.4773 23.697C39.5183 23.3378 37.5415 23.7926 35.84 24.7532C34.0464 25.7812 32.5139 27.2309 31.3063 28.8674C30.1377 30.4282 29.1487 32.2348 28.6607 34.1466C28.1809 36.0052 28.1183 37.9832 28.8721 39.7886C29.4853 41.2727 30.8866 42.4165 32.4014 43.0061C33.8713 43.5343 35.4853 43.4859 36.6692 42.5265C38.138 41.3118 38.4156 39.1767 38.0955 37.3843C37.6902 35.0885 36.2168 33.0077 34.9313 31.1195C33.862 29.5917 32.6167 28.1455 31.2401 26.8422C29.8554 25.592 28.317 24.4542 26.5446 23.77C24.8479 23.1249 22.9597 23.1851 21.1637 23.178C19.4741 23.1874 17.8187 23.5017 16.1917 23.9837C14.6568 24.3984 13.4528 25.1369 12.1849 26.1105C9.39522 28.2905 7.17853 31.5126 6.22968 34.9864C5.97445 35.9269 5.83383 36.8308 5.73811 37.7962C5.68611 38.3054 5.1414 38.6563 4.66287 38.5819C4.13116 38.4992 3.82514 38.0159 3.87714 37.5066Z"
						fill="black"
					/>
					<path
						d="M52.4247 39.9673C56.5672 42.2183 60.8741 44.1136 65.3061 45.7289C66.3896 46.1153 67.5263 46.5099 68.618 46.8432C69.0351 46.9625 69.5196 47.174 69.9839 47.1646C70.3112 47.161 70.3278 47.0547 70.3242 46.7274C70.3455 46.2406 70.2829 45.7679 70.2203 45.2953C70.1742 44.7163 70.0749 44.1291 69.9757 43.5419C69.8221 42.4288 69.5846 41.33 69.294 40.2228C68.6394 37.7793 67.7332 35.3783 66.6119 33.1345C66.3815 32.6902 66.7242 32.0628 67.1826 31.9163C67.7025 31.7249 68.1787 31.9896 68.4008 32.487C69.4997 34.7001 70.3917 37.0172 71.0238 39.43C71.3511 40.6518 71.6477 41.896 71.8379 43.1236C72.0139 44.2674 72.2348 45.4726 72.2206 46.614C72.2124 47.8924 71.5294 48.957 70.2037 49.0775C68.9925 49.1614 67.7212 48.5824 66.607 48.2185C64.1741 47.4043 61.7578 46.4839 59.4112 45.4653C56.7846 44.3215 54.197 43.1021 51.6873 41.7314C51.2336 41.4975 50.9891 40.9693 51.2006 40.4849C51.3589 39.9922 52.0017 39.7109 52.4247 39.9673Z"
						fill="black"
					/>
				</svg>
			</div>
		</div>
		<div class="relative z-10 grid grid-cols-1 gap-8 md:grid-cols-12 lg:gap-16">
			<div class="order-2 pt-10 text-center md:col-span-7 md:text-left lg:order-1">
				<h1
					class="text-4xl leading-tight font-bold text-[#04091B] lg:text-[64px] lg:leading-[75px]"
				>
					{@html h1}
				</h1>
				<p class="mt-4 text-lg text-[#4A4F61]">
					{p}
				</p>

				<div class="mt-8 flex flex-col gap-4 sm:flex-row sm:justify-center md:justify-start">
					<a
						href={ctaLink}
						class="inline-flex items-center justify-center rounded-full border border-transparent bg-[#5D28B6] px-7 py-3 text-base font-medium text-white transition duration-200 hover:bg-purple-800"
					>
						{ctaText}
						<span
							class="ml-2 inline-flex items-center justify-center rounded-full bg-white p-1 text-purple-700"
						>
							<svg
								width="33"
								height="33"
								viewBox="0 0 33 33"
								fill="none"
								xmlns="http://www.w3.org/2000/svg"
							>
								<circle cx="16.5" cy="16.5" r="16.5" fill="white" />
								<path
									d="M10 24L23 11M23 11V23.48M23 11H10.52"
									stroke="#5D28B6"
									stroke-width="1.5"
									stroke-linecap="round"
									stroke-linejoin="round"
								/>
							</svg>
						</span>
					</a>
					{#if showSecond}
						<a
							href="/contact#callback"
							class="inline-flex items-center justify-center rounded-full border border-[#5D28B6] bg-white px-7 py-3 text-base font-medium text-[#5D28B6] transition duration-200 hover:bg-purple-50"
						>
							Request A Call Back
							<span
								class="ml-2 inline-flex items-center justify-center rounded-full bg-[#5D28B6] p-1 text-white"
							>
								<svg
									width="33"
									height="33"
									viewBox="0 0 33 33"
									fill="none"
									xmlns="http://www.w3.org/2000/svg"
								>
									<circle cx="16.5" cy="16.5" r="16.5" fill="#5D28B6" />
									<path
										d="M10 24L23 11M23 11V23.48M23 11H10.52"
										stroke="white"
										stroke-width="1.5"
										stroke-linecap="round"
										stroke-linejoin="round"
									/>
								</svg>
							</span>
						</a>
					{/if}
				</div>
			</div>

			{#if image}
				<div class="relative order-1 hidden md:col-span-5 md:block lg:order-2">
					{@render image()}
				</div>
			{/if}
		</div>
	</div>
</section>

<style>
	section {
		font-family: Montserrat;
	}
	.call {
		display: none;
		position: absolute;
		top: 0;
		right: 0;
		padding: 2px 11px;
		margin-top: 2px;
		color: #fff;
		border-radius: 4px;
		z-index: 30;
		align-items: center;
		gap: 4px;
		background-color: #5d28b6;
		@media screen and (min-width: 900px) {
			display: flex;
		}
	}
</style>
