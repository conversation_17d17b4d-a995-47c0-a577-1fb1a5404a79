<script lang="ts">
	import { getContext, onMount } from 'svelte';

	import { menuItems } from '$lib/menuItems.svelte';
	import MobileMenuItem from './MobileMenuItem.svelte';

	let { routeId = $bindable('home') }: { routeId: string } = $props();

	import { toggleStore, clickOutsideAction } from '@sveltelegos-blue/svelte-legos';

	const { toggle, off, value } = toggleStore();
	let appConfig = getContext('appConfig');

	onMount(() => {
		// Ensure the menu is closed when the component mounts
		off();
	});

	// Function to handle navigation and closing
	function handleCloseDrawer() {
		off();
		// Add any other logic needed on nav click if necessary
	}
</script>

<button class="icon-button" class:active={$value} onclick={toggle}>
	<span class="hidden">menu button</span>
	<svg
		class="icon-hamburger"
		xmlns="http://www.w3.org/2000/svg"
		width="24"
		height="24"
		viewBox="0 0 24 24"
		style="fill: rgba(0, 0, 0, 1);transform: ;msFilter:;"
		><path d="M4 6h16v2H4zm4 5h12v2H8zm5 5h7v2h-7z"></path></svg
	>
</button>

{#if $value}
	<div class="drawer" class:active={$value} use:clickOutsideAction onclickoutside={off}>
		<nav class="side-nav" aria-label="Mobile navigation">
			<ul>
				{#each $menuItems as menu (menu.routeId)}
					<MobileMenuItem {menu} {routeId} onNav={handleCloseDrawer} />
				{/each}
				<li>
					<button class="join-button-mobile" onclick={handleCloseDrawer}> Join Now </button>
				</li>
			</ul>
		</nav>
	</div>
{/if}

<style>
	.hidden {
		position: absolute;
		width: 1px;
		height: 1px;
		padding: 0;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border-width: 0;
	}

	.icon-button {
		background: none;
		border: none;
		padding: 5px;
		cursor: pointer;
		position: relative;
		width: 34px;
		height: 34px;
		overflow: hidden;
		color: #333;
	}

	.icon-button svg {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		transition:
			opacity 0.3s ease-in-out,
			transform 0.3s ease-in-out;
		width: 24px;
		height: 24px;
		fill: currentColor;
	}

	.icon-hamburger {
		opacity: 1;
		transform: translate(-50%, -50%) rotate(0deg) scale(1);
	}

	.icon-close {
		opacity: 0;
		transform: translate(-50%, -50%) rotate(-90deg) scale(0.5);
		pointer-events: none; /* Prevent interaction when hidden */
	}

	.icon-button.active .icon-hamburger {
		opacity: 0;
		transform: translate(-50%, -50%) rotate(90deg) scale(0.5);
		pointer-events: none;
	}

	.icon-button.active .icon-close {
		opacity: 1;
		transform: translate(-50%, -50%) rotate(0deg) scale(1);
		pointer-events: auto;
	}

	/* --- Drawer Styles --- */
	.drawer {
		width: 80%;
		max-width: 320px;
		height: 100svh;
		position: fixed;
		left: 0;
		top: 0;
		background: #fff;
		border-right: 1px solid #e0e0e0;
		box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
		transform: translateX(-100%);
		opacity: 0;
		visibility: hidden;
		transition:
			transform 0.3s ease-in-out,
			opacity 0.3s ease-in-out,
			visibility 0s linear 0.3s;
		will-change: transform, opacity, visibility;
		z-index: 1000;
		box-sizing: border-box;
		overflow-y: auto;
	}

	.drawer.active {
		transform: translateX(0);
		opacity: 1;
		visibility: visible;
		transition:
			transform 0.3s ease-in-out,
			opacity 0.3s ease-in-out,
			visibility 0s linear 0s;
	}

	.side-nav {
		padding: 1.5rem 0;
		font-family: 'Mukta', sans-serif;
		font-weight: 400;
	}

	.side-nav ul {
		list-style: none;
		padding: 0;
		margin: 0;
		display: flex;
		flex-direction: column;
	}

	.side-nav li {
	}

	.side-nav li a {
		display: block;
		padding: 0.9rem 1.5rem; /* Generous padding for touch targets */
		text-decoration: none;
		color: #333; /* Standard link color */
		font-size: 1rem; /* 16px base */
		font-weight: 500; /* Slightly bolder than normal text */
		border-left: 4px solid transparent; /* Indicator space for active */
		transition:
			background-color 0.2s ease,
			border-left-color 0.2s ease,
			color 0.2s ease;
	}

	/* Hover state for links */
	.side-nav li a:hover {
		background-color: #f5f5f5;
		color: #111;
	}

	.side-nav li.active a {
		color: var(--brand-purple);
		font-weight: 600;
		border-left-color: var(--brand-purple);
		background-color: rgba(128, 0, 128, 0.05);
	}

	.join-button-mobile {
		display: block;
		box-sizing: border-box;
		width: calc(100% - 3rem);
		margin: 2rem 1.5rem 1.5rem;
		background-color: var(--brand-purple);
		color: #fff;
		height: 44px;
		border-radius: 22px;
		border: none;
		padding: 0 20px;
		font-family: 'Mukta', sans-serif;
		font-weight: 500;
		font-size: 1rem;
		line-height: 44px;
		cursor: pointer;
		text-align: center;
		transition: background-color 0.2s ease;
	}

	.join-button-mobile:hover,
	.join-button-mobile:focus {
		background-color: color-mix(in srgb, var(--brand-purple), black 10%);
		outline: none;
	}

	/* Optional: Overlay style */
	/*
    .overlay {
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999; // Below drawer, above content
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0s linear 0.3s;
    }
    .drawer.active + .overlay { // If overlay is sibling AFTER drawer
        opacity: 1;
        visibility: visible;
        transition-delay: 0s;
    }
    */

	.hidden {
		/* Duplicated for completeness */
		position: absolute;
		width: 1px;
		height: 1px;
		padding: 0;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border-width: 0;
	}
	.icon-button {
		background: none;
		border: none;
		padding: 5px;
		cursor: pointer;
		position: relative;
		width: 34px;
		height: 34px;
		overflow: hidden;
		color: #333;
	}
	.icon-button svg {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		transition:
			opacity 0.3s ease-in-out,
			transform 0.3s ease-in-out;
		width: 24px;
		height: 24px;
		fill: currentColor;
	}
	.icon-hamburger {
		opacity: 1;
		transform: translate(-50%, -50%) rotate(0deg) scale(1);
	}
	.icon-close {
		opacity: 0;
		transform: translate(-50%, -50%) rotate(-90deg) scale(0.5);
		pointer-events: none;
	}
	.icon-button.active .icon-hamburger {
		opacity: 0;
		transform: translate(-50%, -50%) rotate(90deg) scale(0.5);
		pointer-events: none;
	}
	.icon-button.active .icon-close {
		opacity: 1;
		transform: translate(-50%, -50%) rotate(0deg) scale(1);
		pointer-events: auto;
	}
	.drawer {
		width: 80%;
		max-width: 320px;
		height: 100svh;
		position: fixed;
		left: 0;
		top: 0;
		background: #fff;
		border-right: 1px solid #e0e0e0;
		box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
		transform: translateX(-100%);
		opacity: 0;
		visibility: hidden;
		transition:
			transform 0.3s ease-in-out,
			opacity 0.3s ease-in-out,
			visibility 0s linear 0.3s;
		will-change: transform, opacity, visibility;
		z-index: 1000;
		box-sizing: border-box;
		overflow-y: auto;
	}
	.drawer.active {
		transform: translateX(0);
		opacity: 1;
		visibility: visible;
		transition:
			transform 0.3s ease-in-out,
			opacity 0.3s ease-in-out,
			visibility 0s linear 0s;
	}
	.side-nav {
		padding: 1.5rem 0;
		font-family: 'Mukta', sans-serif;
		font-weight: 400;
	}
	.side-nav ul {
		list-style: none;
		padding: 0;
		margin: 0;
		display: flex;
		flex-direction: column;
	}
	/* NOTE: Base li/a styles are now primarily in MobileMenuItem.svelte */
	/* Ensure styles for the direct children (like the button li) are handled */
	.side-nav > ul > li:last-child {
		/* Target the button li */
		margin-top: 1rem; /* Example spacing */
	}

	.join-button-mobile {
		display: block;
		box-sizing: border-box;
		width: calc(100% - 3rem);
		margin: 1.5rem 1.5rem 1.5rem;
		background-color: var(--brand-purple);
		color: #fff;
		height: 44px;
		border-radius: 22px;
		border: none;
		padding: 0 20px;
		font-family: 'Mukta', sans-serif;
		font-weight: 500;
		font-size: 1rem;
		line-height: 44px;
		cursor: pointer;
		text-align: center;
		transition: background-color 0.2s ease;
	}
	.join-button-mobile:hover,
	.join-button-mobile:focus {
		background-color: color-mix(in srgb, var(--brand-purple), black 10%);
		outline: none;
	}
</style>
