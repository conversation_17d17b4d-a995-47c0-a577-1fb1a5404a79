<script lang="ts">
	let { link, text }: { link?: string; text?: string } = $props();
</script>

<a
	href={link ?? '#'}
	class="mt-4 inline-flex items-center justify-center rounded-full border border-transparent bg-[#5D28B6] px-4 py-2 text-base font-medium text-white transition duration-200 hover:bg-purple-800"
>
	{text ?? 'Download Application Form'}
	<span class="ml-2 inline-flex items-center justify-center rounded-full p-1"
		><svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<circle cx="16.5" cy="16.5" r="16.5" fill="white" />
			<path
				d="M10 24L23 11M23 11V23.48M23 11H10.52"
				stroke="#5D28B6"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
		</svg>
	</span>
</a>
