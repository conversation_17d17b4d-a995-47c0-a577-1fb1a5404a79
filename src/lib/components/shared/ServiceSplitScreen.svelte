<script lang="ts" module>
	// Define props for this specific component layout
	export type ListItem = { text: string };
	export type AlternatingInfoProps = {
		topLabel?: string;
		mainHeading: string;
		subHeadingParagraph?: string;
		imageSnippet: Snippet;
		textParagraph1?: string;
		textParagraph2?: string;
		listHeading?: string;
		listItems?: ListItem[];
		imagePosition?: 'left' | 'right'; // Control image side
		baseClass?: string;
		id?: string;
		bottomFooter?: Snippet;
	};
</script>

<script lang="ts">
	import type { Snippet } from 'svelte'; // Ensure Snippet is imported

	// Default props can be added here if desired
	let {
		topLabel,
		mainHeading,
		subHeadingParagraph,
		imageSnippet, // Changed from imageSrc to imageSnippet
		textParagraph1,
		textParagraph2,
		listHeading,
		listItems = [],
		imagePosition = 'right', // Default image on right
		baseClass = 'bg-white',
		id = '',
		bottomFooter
	}: AlternatingInfoProps = $props();

	// Determine order classes based on imagePosition
	// On medium screens (md), text is always first unless image is 'left'
	// Swaps visual order using grid's order capability
	let textOrderClasses = $derived(imagePosition === 'right' ? 'md:order-1' : 'md:order-2');
	let imageOrderClasses = $derived(imagePosition === 'right' ? 'md:order-2' : 'md:order-1');
</script>

<section {id} class="{baseClass} py-16 md:py-20">
	<div class="container mx-auto px-4">
		<div class="mx-auto mb-12 max-w-3xl text-center md:mb-16">
			{#if topLabel}
				<p class="mb-2 text-sm font-semibold tracking-wider text-[#FF6F00] uppercase">
					{topLabel}
				</p>
			{/if}
			<h2
				class="text-3xl leading-tight font-bold text-[#0586AB] capitalize md:text-4xl lg:text-[40px]"
			>
				{@html mainHeading}
			</h2>
			{#if subHeadingParagraph}
				<p class="mx-auto mt-4 max-w-xl text-base text-[#737373]">{subHeadingParagraph}</p>
			{/if}
		</div>

		<div class="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-12 xl:gap-16">
			<div class="{textOrderClasses} flex flex-col">
				{#if textParagraph1}
					<p class="mb-4 text-base leading-relaxed text-gray-700">
						{textParagraph1}
					</p>
				{/if}
				{#if textParagraph2}
					<p class="mb-6 text-base leading-relaxed text-gray-700">
						{textParagraph2}
					</p>
				{/if}

				{#if listHeading}
					<h3 class="mt-2 mb-4 text-lg font-semibold text-gray-900">
						{listHeading}
					</h3>
				{/if}

				{#if listItems.length > 0}
					<ul class="staffing-ul flex flex-col gap-[23px] space-y-3">
						{#each listItems as item (item)}
							<li class="flex items-start space-x-3">
								<span class="flex h-5 w-5 flex-shrink-0 items-center justify-center bg-[#240951]">
									<svg class="h-[21px] w-[21px] text-white" fill="currentColor" viewBox="0 0 20 20"
										><path
											fill-rule="evenodd"
											d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
											clip-rule="evenodd"
										></path></svg
									>
								</span>
								<span class="text-sm font-medium text-[#151414]">{item.text}</span>
							</li>
						{/each}
					</ul>

					<!-- 
					<ul class="space-y-3">
						{#each listItems as item (item.text)}
							<li class="flex items-start space-x-3">
								<span
									class="mt-1 flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-sm border border-purple-600 p-0.5"
								>
									<svg
										class="h-full w-full text-purple-600"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path
											fill-rule="evenodd"
											d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
											clip-rule="evenodd"
										></path>
									</svg>
								</span>
								<span class="text-sm text-gray-800">{item.text}</span>
							</li>
						{/each}
					</ul> -->
				{/if}

				{#if bottomFooter}
					{@render bottomFooter()}
				{/if}
			</div>

			<div class={imageOrderClasses}>
				{#if imageSnippet}
					{@render imageSnippet()}
				{:else}
					<div
						class="flex aspect-video items-center justify-center rounded-md bg-gray-100 text-gray-400"
					>
						No Image Provided
					</div>
				{/if}
			</div>
		</div>
	</div>
</section>

<style>
	/* Component-specific styles if needed */
</style>
