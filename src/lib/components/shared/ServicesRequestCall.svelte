<script lang="ts">
	let {
		title = 'Staffing Solutions',
		subtitle = 'Connecting Business With The Right Talents',
		description = `Lorem ipsum dolor sit amet consectetur. Adipiscing nec ipsum mauris porttitor adipiscing
				lorem praesent massa. Purus velit nunc dolor in posuere netus.`
	} = $props();
</script>

<section class="bg-white py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 max-w-3xl text-center md:mb-16">
			<p class="mb-2 text-sm font-bold tracking-wider text-[#FF6F00] uppercase">
				{title}
			</p>
			<h2 class="text-3xl leading-tight font-bold text-[#0586AB] md:text-4xl lg:text-5xl">
				{subtitle}
			</h2>
			<p class="mt-4 text-base text-[#737373]">
				{description}
			</p>
		</div>

		<div class="grid grid-cols-1 items-start gap-10 lg:grid-cols-12 lg:gap-12 xl:gap-16">
			<div class="flex flex-col lg:col-span-4">
				<svg
					width="91"
					height="83"
					viewBox="0 0 91 83"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						d="M4.89945 47.4294C4.20545 41.7653 7.86246 36.403 11.968 32.8778C16.5135 28.9571 22.1382 26.4847 28.0622 25.5062C40.0453 23.466 51.9806 27.2984 62.6965 32.3798C69.3688 35.5401 75.695 39.3489 81.7094 43.6607C82.2679 44.0624 82.2166 45.0886 81.8044 45.5292C81.2848 46.0981 80.4943 46.0259 79.9359 45.6241C74.8371 41.9912 69.4891 38.7606 63.8917 35.9324C58.7694 33.3707 53.5052 31.083 47.9954 29.5062C37.2776 26.5395 25.2619 26.6107 15.8475 33.2217C11.4193 36.3242 6.87123 41.5518 7.60348 47.3789C7.6664 48.0861 6.98388 48.6933 6.32176 48.7284C5.46896 48.7568 4.96237 48.1366 4.89945 47.4294Z"
						fill="#062126"
					/>
					<path
						d="M78.9928 23.5175C80.1452 30.3288 81.2977 37.1401 82.4501 43.9514C82.6938 45.3553 83.2602 47.1818 81.902 48.2055C81.2368 48.7399 80.384 48.7682 79.5207 48.6787C78.6574 48.5892 77.8392 48.4719 76.9481 48.3374C73.5123 47.9065 70.0937 47.4029 66.6578 46.972C62.7851 46.4375 58.9401 45.948 55.0674 45.4135C54.367 45.2857 53.94 44.4922 54.0955 43.8368C54.2683 43.1087 54.9718 42.7372 55.6722 42.865C61.9719 43.7063 68.2716 44.5476 74.4985 45.3716C76.1072 45.5611 77.6986 45.8234 79.3073 46.0129C79.498 46.0197 79.9694 45.9778 80.1706 46.1025C80.344 46.1821 80.1879 46.0296 80.1533 46.1753C80.1052 46.7023 80.0182 46.2586 80.0182 46.2586C79.9904 46.2135 79.9275 45.5064 79.8997 45.4613C79.8127 45.0176 79.7535 44.619 79.6665 44.1753C79.1304 41.0868 78.6393 37.9706 78.0754 34.8371C77.4523 31.3049 76.8742 27.745 76.2511 24.2128C76.1154 23.4883 76.4338 22.7948 77.1755 22.5862C77.9728 22.4678 78.8848 22.8381 78.9928 23.5175Z"
						fill="#062126"
					/>
				</svg>

				<h3 class="mb-4 text-2xl leading-snug font-semibold text-gray-900">
					To Be The First To Know About New Jobs Offers, Please Click Below To Register With Us
					Today.
				</h3>
				<p class="mb-3 text-sm leading-relaxed text-gray-600">
					Central staffing specialises in various areas of commercial recruitment. Our team of
					experience consultants delivers a professional and personalised services.
				</p>
				<p class="mb-8 text-sm leading-relaxed text-gray-600">
					Our main objective is partnering with businesses to understand their
				</p>

				<div class="mt-auto">
					<a
						href="/contact#callback"
						class="group inline-flex items-center justify-center rounded-full border border-transparent bg-purple-700 px-7 py-3 text-base font-medium text-white transition duration-200 hover:bg-purple-800"
					>
						Request A Call Back
						<span
							class="ml-2 inline-flex items-center justify-center rounded-full bg-white p-1 text-purple-700 transition-transform duration-200 group-hover:-translate-x-1"
						>
							<svg
								width="33"
								height="33"
								viewBox="0 0 33 33"
								fill="none"
								xmlns="http://www.w3.org/2000/svg"
							>
								<circle cx="16.5" cy="16.5" r="16.5" fill="white" />
								<path
									d="M10 24L23 11M23 11V23.48M23 11H10.52"
									stroke="#5D28B6"
									stroke-width="1.5"
									stroke-linecap="round"
									stroke-linejoin="round"
								/>
							</svg>
						</span>
					</a>
				</div>

				<div class="mt-8 text-cyan-300 opacity-50">
					<pre class="text-xs leading-none">
 • • • • • • • • • • • •
 • • • • • • • • • • • •
 • • • • • • • • • • • •
 • • • • • • • • • • • •
 • • • • • • • • • • • •
 • • • • • • • • • • • •</pre>
				</div>
			</div>
			<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:gap-8 lg:col-span-8">
				<div
					class="rounded-xl border border-gray-200 bg-gray-50 p-6 transition-shadow duration-300 hover:shadow-lg"
				>
					<div class="mb-4 inline-block rounded-full">
						<svg
							width="100"
							height="100"
							viewBox="0 0 100 100"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
							<path
								d="M34.9997 61.6667C34.5275 61.6667 34.1319 61.5068 33.813 61.1868C33.4941 60.8668 33.3341 60.4712 33.333 60.0001C33.3319 59.529 33.4919 59.1334 33.813 58.8134C34.1341 58.4934 34.5297 58.3334 34.9997 58.3334H64.9997C65.4719 58.3334 65.868 58.4934 66.188 58.8134C66.508 59.1334 66.6675 59.529 66.6663 60.0001C66.6652 60.4712 66.5052 60.8673 66.1863 61.1884C65.8675 61.5095 65.4719 61.669 64.9997 61.6667H34.9997ZM34.9997 56.6667V55.0001C34.9997 51.4445 36.0902 48.3056 38.2713 45.5834C40.4525 42.8612 43.2508 41.139 46.6663 40.4168V40.0001C46.6663 39.0834 46.993 38.299 47.6463 37.6468C48.2997 36.9945 49.0841 36.6679 49.9997 36.6668C50.9152 36.6656 51.7002 36.9923 52.3547 37.6468C53.0091 38.3012 53.3352 39.0856 53.333 40.0001V40.4168C56.7775 41.139 59.583 42.8612 61.7497 45.5834C63.9163 48.3056 64.9997 51.4445 64.9997 55.0001V56.6667H34.9997ZM38.4163 53.3334H61.583C61.1941 50.4445 59.9025 48.0556 57.708 46.1668C55.5136 44.2779 52.9441 43.3334 49.9997 43.3334C47.0552 43.3334 44.493 44.2779 42.313 46.1668C40.133 48.0556 38.8341 50.4445 38.4163 53.3334Z"
								fill="#0586AB"
							/>
						</svg>
					</div>
					<h4 class="mb-2 text-lg font-semibold text-gray-900">Permanent Service</h4>
					<p class="text-sm text-gray-600">
						Secure top talent for long-term roles with our thorough recruitment process, ensuring
						the right fit for your organisation’s ongoing needs.
					</p>
				</div>

				<div
					class="rounded-xl border border-gray-200 bg-gray-50 p-6 transition-shadow duration-300 hover:shadow-lg"
				>
					<div class="mb-4 inline-block rounded-full">
						<svg
							width="100"
							height="100"
							viewBox="0 0 100 100"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
							<path
								d="M41.6663 40V35H58.333V40H61.6247L66.6663 51.5833V63.3333H33.333V51.5833L38.3747 40H41.6663ZM44.9997 40H54.9997V38.3333H44.9997V40ZM41.6663 50V48.3333H44.9997V50H54.9997V48.3333H58.333V50H62.333L59.4997 43.3333H40.4997L37.6663 50H41.6663ZM41.6663 53.3333H36.6663V60H63.333V53.3333H58.333V55H54.9997V53.3333H44.9997V55H41.6663V53.3333Z"
								fill="#0586AB"
							/>
						</svg>
					</div>
					<h4 class="mb-2 text-lg font-semibold text-gray-900">Temporary Service</h4>
					<p class="text-sm text-gray-600">
						Quickly fill temporary vacancies or short-term projects with experienced professionals
						who can step in and maintain productivity.
					</p>
				</div>

				<div
					class="rounded-xl border border-gray-200 bg-gray-50 p-6 transition-shadow duration-300 hover:shadow-lg"
				>
					<div class="mb-4 inline-block rounded-full">
						<svg
							width="100"
							height="100"
							viewBox="0 0 100 100"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
							<path
								d="M40 66.6666C38.6111 66.6666 37.4306 66.1805 36.4583 65.2083C35.4861 64.236 35 63.0555 35 61.6666V56.6666H40V33.3333H65V61.6666C65 63.0555 64.5139 64.236 63.5417 65.2083C62.5694 66.1805 61.3889 66.6666 60 66.6666H40ZM60 63.3333C60.4722 63.3333 60.8683 63.1733 61.1883 62.8533C61.5083 62.5333 61.6678 62.1377 61.6667 61.6666V36.6666H43.3333V56.6666H58.3333V61.6666C58.3333 62.1388 58.4933 62.5349 58.8133 62.8549C59.1333 63.1749 59.5289 63.3344 60 63.3333ZM45 44.9999V41.6666H60V44.9999H45ZM45 49.9999V46.6666H60V49.9999H45Z"
								fill="#0586AB"
							/>
						</svg>
					</div>
					<h4 class="mb-2 text-lg font-semibold text-gray-900">Contract Services</h4>
					<p class="text-sm text-gray-600">
						Access a pool of qualified medical staff, including locum doctors, nurses, and allied
						health professionals, available to cover shifts at short notice.
					</p>
				</div>

				<div
					class="rounded-xl border border-gray-200 bg-gray-50 p-6 transition-shadow duration-300 hover:shadow-lg"
				>
					<div class="mb-4 inline-block rounded-full">
						<svg
							width="100"
							height="100"
							viewBox="0 0 100 100"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
							<path
								d="M44.6663 48.2499L48.2497 44.6249L45.9163 42.2499L44.083 44.0833L41.7497 41.7499L43.5413 39.9166L41.6663 38.0416L38.0413 41.6666L44.6663 48.2499ZM58.333 61.9583L61.958 58.3333L60.083 56.4583L58.2497 58.2499L55.9163 55.9166L57.708 54.0833L55.333 51.7499L51.7497 55.3333L58.333 61.9583ZM42.083 64.9999H34.9997V57.9166L42.2913 50.6249L33.333 41.6666L41.6663 33.3333L50.6663 42.3333L59.3747 33.5833L66.3747 40.6666L57.708 49.3749L66.6663 58.3333L58.333 66.6666L49.3747 57.7083L42.083 64.9999ZM38.333 61.6666H40.6663L56.9997 45.3749L54.6247 42.9999L38.333 59.3333V61.6666ZM55.833 44.2083L54.6247 42.9999L56.9997 45.3749L55.833 44.2083Z"
								fill="#0586AB"
							/>
						</svg>
					</div>
					<h4 class="mb-2 text-lg font-semibold text-gray-900">Ad-hoc Staff Support Service</h4>
					<p class="text-sm text-gray-600">
						On-demand staffing for urgent needs, unexpected absences, or peak business periods –
						ensuring seamless operations without delays.
					</p>
				</div>
			</div>
		</div>
	</div>
</section>
