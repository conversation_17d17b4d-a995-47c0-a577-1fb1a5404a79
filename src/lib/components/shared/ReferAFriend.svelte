<script lang="ts">
	let { title = 'Refer a friend', link = '/contact#refer-a-friend' } = $props();
</script>

<a
	href={link}
	class="mt-4 inline-flex items-center justify-center rounded-full border border-[#5D28B6] bg-white px-4 py-2 text-base font-semibold text-[#5D28B6] transition duration-200 hover:bg-purple-800 hover:text-white"
>
	{title}
	<span
		class="ml-2 inline-flex items-center justify-center rounded-full bg-white p-1 text-purple-700"
	>
		<svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<circle cx="16.5" cy="16.5" r="16.5" fill="#5D28B6" />
			<path
				d="M10 24L23 11M23 11V23.48M23 11H10.52"
				stroke="white"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
		</svg>
	</span>
</a>
