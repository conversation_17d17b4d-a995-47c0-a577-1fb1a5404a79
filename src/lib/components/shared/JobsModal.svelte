<script lang="ts">
	// For Svelte 5 state management
	// let { open = false, jobDetails = {}, onClose = () => {} } = $props();
	// The above $props() is for when this component is a .svelte.js or .svelte.ts file (runes mode experimental)
	// For a standard .svelte file in Svelte 5, you define props with `export let` and use runes for internal state.
	// However, the request implies full Svelte 5 style, so $props is fine if the project is set up for it.
	// For broader compatibility with typical Svelte 5 setups, let's use export let for props for now
	// and demonstrate how onClose would be called.

	let { open, jobDetails, onClose, onApplyNow } = $props();

	// export let open: boolean = false;
	// export let jobDetails: Record<string, any> = {}; // Or a more specific type for your job details
	// export let onClose: () => void = () => {}; // Callback for closing the modal

	// Actions from Svelte Legos
	import { portalAction } from '@sveltelegos-blue/svelte-legos';

	function handleApplyNow() {
		if (onApplyNow) {
			onApplyNow();
		}
	}

	function handleCloseModal() {
		if (onClose) {
			onClose();
		}
	}
</script>

{#if open}
	<div use:portalAction={'body'}>
		<div class="modal-backdrop" role="presentation">
			<div
				class="modal-content"
				role="dialog"
				aria-labelledby="modal-title"
				aria-describedby="modal-description"
			>
				<button class="modal-close-button" onclick={handleCloseModal} aria-label="Close modal">
					&times;
				</button>

				<h2 id="modal-title" class="modal-title">Job Details</h2>

				<div class="job-header">
					<div class="company-info">
						<p class="company-name">{jobDetails.companyName}</p>
						<h3>{jobDetails.title}</h3>
						<div class="job-meta">
							<span>{jobDetails.jobType}</span>
							<span class="separator"></span>
							<span>{jobDetails.salary}</span>
						</div>
						<div class="job-meta">
							<span>{jobDetails.jobLocation}</span>
							<span class="separator"></span>
							<span>{jobDetails.jobField}</span>
						</div>
					</div>
					<div class="deadline-info">
						<p>Application Deadline</p>
						<p class="deadline-date">{jobDetails.deadline}</p>
					</div>
				</div>

				<hr class="divider" />

				<div id="modal-description" class="job-body-scrollable">
					<div class="job-description-text">
						<h4>Company Description</h4>
						<p>{@html jobDetails.companyDesc}</p>
					</div>
					<div class="job-description-text mt-3">
						<h4>Job Description</h4>
						<p>{@html jobDetails.jobDesc}</p>
					</div>

					<div class="job-section mt-3">
						<h4>Qualifications and Skills</h4>
						<p>
							{@html jobDetails.skills}
						</p>
					</div>
				</div>

				<hr class="divider" />

				<div class="modal-footer">
					<button onclick={handleApplyNow} class="apply-now-button">
						Apply Now
						<svg
							width="16"
							height="16"
							viewBox="0 0 24 24"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
							style="margin-left: 8px;"
						>
							<path
								d="M10 5L17 12L10 19"
								stroke="currentColor"
								stroke-width="2"
								stroke-linecap="round"
								stroke-linejoin="round"
							/>
							<path
								d="M17 12H3"
								stroke="currentColor"
								stroke-width="2"
								stroke-linecap="round"
								stroke-linejoin="round"
							/>
						</svg>
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	/* Ensure these styles are global enough if the modal is portaled, or scope them appropriately. */
	/* For a portaled element, styles might need to be in a global stylesheet or explicitly scoped if possible. */
	/* Svelte's default scoping might not apply directly to portaled content in the same way. */
	/* However, if the <style> tag is in this component, Svelte usually handles it. */

	.modal-backdrop {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.6);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
		padding: 20px;
		box-sizing: border-box;
	}

	.modal-content {
		background-color: white;
		padding: 30px 40px;
		border-radius: 16px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
		width: 100%;
		max-width: 800px;
		max-height: 90vh;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: hidden; /* Important for child scrolling */
		font-family:
			system-ui,
			-apple-system,
			BlinkMacSystemFont,
			'Segoe UI',
			Roboto,
			Oxygen,
			Ubuntu,
			Cantarell,
			'Open Sans',
			'Helvetica Neue',
			sans-serif;
	}

	.modal-close-button {
		position: absolute;
		top: 15px;
		right: 20px;
		background: none;
		border: none;
		font-size: 2rem;
		color: #888;
		cursor: pointer;
		line-height: 1;
		padding: 5px;
		z-index: 10; /* Ensure close button is above content within modal */
	}
	.modal-close-button:hover {
		color: #333;
	}

	.modal-title {
		text-align: left;
		font-size: 1.8rem;
		font-weight: 600;
		color: #333;
		margin-bottom: 25px;
	}

	.job-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 20px;
		border: 1px solid #007bff; /* Blue border */
		padding: 20px;
		border-radius: 8px;
		gap: 20px;
	}

	.company-info .company-name {
		font-size: 0.9rem;
		color: #555;
		margin-bottom: 4px;
		text-transform: capitalize;
	}

	.company-info h3 {
		font-size: 1.5rem;
		font-weight: 600;
		color: #222;
		margin-bottom: 8px;
	}
	h3 {
		text-transform: capitalize;
	}

	.job-meta {
		display: flex;
		align-items: center;
		font-size: 0.9rem;
		color: #555;
	}

	.job-meta .separator {
		height: 12px;
		width: 1px;
		background-color: #ccc;
		margin: 0 10px;
	}

	.job-meta span {
		text-transform: capitalize;
	}

	.deadline-info {
		text-align: right;
		font-size: 0.9rem;
		color: #555;
		min-width: 150px;
	}

	.deadline-info .deadline-date {
		font-weight: 600;
		color: #333;
		font-size: 1rem;
		margin-top: 4px;
	}

	.divider {
		border: none;
		border-top: 1px solid #eee;
		margin: 0 0 20px 0;
	}
	.job-body-scrollable + .divider {
		margin-top: 20px;
		margin-bottom: 20px;
	}

	.job-body-scrollable {
		flex-grow: 1;
		overflow-y: auto;
		padding-right: 15px; /* For scrollbar */
		/* margin-right: -15px; */ /* Can cause layout shifts, test carefully */
		color: #444;
		line-height: 1.6;
	}
	.job-body-scrollable::-webkit-scrollbar {
		width: 8px;
	}
	.job-body-scrollable::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 10px;
	}
	.job-body-scrollable::-webkit-scrollbar-thumb {
		background: #ccc;
		border-radius: 10px;
	}
	.job-body-scrollable::-webkit-scrollbar-thumb:hover {
		background: #aaa;
	}

	.job-description-text h4,
	.job-section h4 {
		font-size: 1.1rem;
		font-weight: 600;
		color: #333;
		margin-top: 20px;
		margin-bottom: 10px;
	}
	.job-description-text h4:first-child,
	.job-section h4:first-child {
		margin-top: 0;
	}

	.job-section ul {
		list-style-type: disc;
		padding-left: 20px;
		margin-bottom: 20px;
	}

	.job-section ul li {
		margin-bottom: 8px;
		font-size: 0.95rem;
	}

	.job-section p {
		font-size: 0.95rem;
		margin-bottom: 20px;
	}

	.modal-footer {
		padding-top: 5px;
		display: flex;
		justify-content: flex-start;
	}

	.apply-now-button {
		background-color: #6200ee;
		color: white;
		border: none;
		padding: 12px 25px;
		font-size: 1rem;
		font-weight: 500;
		border-radius: 25px;
		cursor: pointer;
		transition: background-color 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.apply-now-button:hover {
		background-color: #5300d0;
	}

	.apply-now-button svg {
		transition: transform 0.2s ease;
	}
	.apply-now-button:hover svg {
		transform: translateX(3px);
	}

	@media (max-width: 768px) {
		.modal-content {
			padding: 25px 20px;
			max-height: 85vh;
		}
		.modal-title {
			font-size: 1.5rem;
			margin-bottom: 20px;
		}
		.job-header {
			flex-direction: column;
			align-items: stretch;
			padding: 15px;
		}
		.deadline-info {
			text-align: left;
			margin-top: 15px;
		}
		.job-body-scrollable {
			padding-right: 5px;
			/* margin-right: -5px; */
		}
		.apply-now-button {
			width: 100%;
			padding: 14px 20px;
		}
	}
	@media (max-width: 480px) {
		.modal-content {
			padding: 20px 15px;
		}
		.modal-title {
			font-size: 1.3rem;
		}
		.company-info h3 {
			font-size: 1.3rem;
		}
	}
</style>
