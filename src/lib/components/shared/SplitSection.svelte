<script lang="ts" module>
	export type ListItem = { text: string };

	export type SplitSectionProps = {
		title?: string;
		label1?: string; // e.g., Healthcare Assistant
		label2?: string; // e.g., Agency Role
		introParagraph?: string;
		listItems: ListItem[];
		listItemsExplainer?: string;
		finalParagraph?: string;
		secondParagraph?: string;
		buttomCTA?: Snippet;
		images?: Snippet;
		imagePosition?: 'left' | 'right'; // Control image side
	};
</script>

<script lang="ts">
	import type { Snippet } from 'svelte';

	let {
		title = 'Register With Us Right Here',
		label1 = 'Nurses',
		label2 = 'Role',
		introParagraph = ``,
		listItems,
		listItemsExplainer,
		secondParagraph = '',
		finalParagraph = `Is your interest in a Permanent Role?  Our dedicated consultants will work with you to secure you opportunities that meet your criteria. 
`,
		buttomCTA,
		images,
		imagePosition = 'left'
	}: SplitSectionProps = $props();
</script>

{#snippet Image()}
	{#if images}
		<div class="relative flex flex-col items-center justify-center">
			<div class="relative aspect-square w-full max-w-xl">
				{@render images()}
			</div>
			<p class="text-[ #04091B ] mt-10 max-w-[638px] justify-center text-[20px] font-bold">
				{finalParagraph}
			</p>
		</div>
	{/if}
{/snippet}

<section class="bg-white py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="relative mb-12 text-center md:mb-16">
			<h2 class="text-3xl font-bold text-cyan-600 md:text-5xl">{title}</h2>
		</div>

		<div class="grid grid-cols-1 gap-10 lg:grid-cols-2 lg:gap-12 xl:gap-16">
			{#if images && imagePosition === 'right'}
				{@render Image()}
			{/if}

			<div class="flex h-full flex-col">
				<p class="mb-3 text-xs font-semibold tracking-wider text-orange-600 uppercase">
					{label1}
					{#if label2}
						| <span class="text-[#240951]">{label2}</span>
					{/if}
				</p>

				<p class="intro mb-6 text-sm leading-relaxed text-black">
					{introParagraph}
				</p>

				{#if listItemsExplainer}
					<p class="text-md mb-6 leading-relaxed font-bold text-[#04091B]">{listItemsExplainer}</p>
				{/if}

				<ul class="staffing-ul mb-6 flex flex-col gap-[23px] space-y-3">
					{#each listItems as item (item)}
						<li class="flex items-start space-x-3">
							<span class="flex h-5 w-5 flex-shrink-0 items-center justify-center bg-[#240951]">
								<svg class="h-[21px] w-[21px] text-white" fill="currentColor" viewBox="0 0 20 20"
									><path
										fill-rule="evenodd"
										d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
										clip-rule="evenodd"
									></path></svg
								>
							</span>
							<span class="font-xs font-medium text-[#151414]">{item.text}</span>
						</li>
					{/each}
				</ul>
				<p class="mb-6 text-sm leading-relaxed text-[#04091B]">
					{secondParagraph}
				</p>
			</div>
			{#if images && imagePosition === 'left'}
				{@render Image()}
			{/if}
		</div>
		<div class="mt-auto flex flex-col gap-4 pt-6 sm:flex-row">
			{#if buttomCTA}
				{@render buttomCTA()}
			{/if}
		</div>
	</div>
</section>

<style>
	.intro {
		max-width: 67ch;
	}
</style>
