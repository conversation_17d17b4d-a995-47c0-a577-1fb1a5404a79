<script lang="ts">
	import { menuItems, openMenuId, toggleMenu, type MenuItem } from '$lib/menuItems.svelte';
	import Self from './DesktopMenuItem.svelte';
	import { clickOutsideAction } from '@sveltelegos-blue/svelte-legos';
	import { slide } from 'svelte/transition';
	import { get } from 'svelte/store'; // To get current value of openMenuId if needed
	import { getContext } from 'svelte';

	let appConfig = getContext('appConfig');

	let { menu, routeId, level = 1 }: { menu: MenuItem; routeId: string; level?: number } = $props();

	// --- State ---
	let isOpen = $state(false);

	// --- Active State Logic ---
	function checkActive(item: MenuItem, currentRouteId: string): boolean {
		// remove preceding slash
		const properRouteId = currentRouteId.replace(/^\//, '');

		if (item.routeId === properRouteId && item.href) {
			return true;
		}
		if (item.children) {
			return item.children.some((child) => checkActive(child, properRouteId));
		}
		return false;
	}
	let isActiveBranch = $derived(checkActive(menu, routeId));

	// --- Open/Close Logic based on Store (for top level) and Local State ---
	$effect(() => {
		const unsub = openMenuId.subscribe((id) => {
			if (level === 1) {
				// Top-level menus' open state is directly tied to the global store
				isOpen = id === menu.routeId;
			} else {
				// For nested menus, if the global store closes their ancestor, they should also close.
				// This ensures nested menus close if their parent is closed by clicking another top-level menu.
				if (id !== findTopLevelParentRouteId($menuItems, menu.routeId)) {
					isOpen = false;
				}
			}
		});
		return unsub;
	});

	// Helper to find the top-level parent of a menu item (if nested)
	// This is a simplified example; you might need a more robust way depending on your menu structure depth
	// For this component, we primarily care if its top-level ancestor is still the $openMenuId
	function findTopLevelParentRouteId(
		items: MenuItem[],
		targetRouteId: string,
		currentPath: MenuItem[] = []
	): string | null {
		for (const item of items) {
			const path = [...currentPath, item];
			if (item.routeId === targetRouteId) {
				return path[0]?.routeId || null; // The first item in the path is the top-level one
			}
			if (item.children) {
				const foundInChild = findTopLevelParentRouteId(item.children, targetRouteId, path);
				if (foundInChild) return path[0]?.routeId || null;
			}
		}
		return null;
	}
	// Simpler check: A nested menu should close if no top-level menu is open, or if a *different* top-level menu opens.
	// The existing $effect already handles top-level. For nested:
	$effect(() => {
		if (level > 1) {
			const currentOpenTopLevelId = get(openMenuId);
			if (!currentOpenTopLevelId) {
				// If all top-level menus are closed
				isOpen = false;
			} else {
				// If a different top-level menu branch is opened, nested menus of other branches should close.
				// This requires knowing the item's top-level ancestor.
				// For simplicity, let's assume handleClickOutside and direct parent closing handle most cases.
				// The effect above subscribing to openMenuId helps close nested menus when their parent's chain breaks.
			}
		}
	});

	// --- Click Handler ---
	function handleClick(event: MouseEvent) {
		if (menu.children) {
			// This item is a parent menu item.
			if (isOpen && menu.href) {
				// Scenario: Parent item with an href, its submenu is open, and it's clicked again.
				// Action: Navigate and close all menus.
				openMenuId.set(null); // This will trigger $effect to set relevant isOpen states to false.
				// Allow the <a> tag's default navigation to proceed.
				// No event.preventDefault() here.
				return;
			} else {
				// Scenario: Parent item (with or without href), submenu is closed OR parent has no href.
				// Action: Toggle submenu.
				event.preventDefault(); // Prevent navigation if it's an <a> tag being used as a toggle.
				if (level === 1) {
					toggleMenu(menu.routeId); // This updates $openMenuId, $effect updates isOpen
				} else {
					isOpen = !isOpen; // Toggle local state for nested items
				}
				return;
			}
		}

		// If we reach here, menu.children is falsy.
		// This means it's a leaf item (no children).
		if (menu.href) {
			// This is a leaf link.
			openMenuId.set(null); // Close all menus.
			// No event.preventDefault(). Navigation is desired via the <a> tag.
		}
	}

	// --- Click Outside Handler ---
	function handleClickOutside() {
		// This function is called on the LI when a click happens outside the LI's boundary.
		// It should only act if this specific menu's dropdown was considered open.
		if (isOpen) {
			if (level === 1) {
				// For top-level menu, closing is handled by setting the global store,
				// which then causes its 'isOpen' to become false via the $effect.
				openMenuId.set(null);
			} else {
				// For nested levels, directly set its local isOpen state to false.
				isOpen = false;
			}
		}
	}

	// --- Keyboard Interaction for items acting as buttons ---
	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			if (menu.children) {
				// Only for parent items that toggle
				event.preventDefault(); // Prevent default space/enter action (e.g., scrolling, link navigation)
				handleClick(event as any); // Treat as a click
			} else if (menu.href) {
				// For leaf links, Enter should navigate, Space might also be expected by some users
				// but default <a> behavior for Space is often nothing or scroll.
				// handleClick will handle closing other menus.
				// If it's Enter on a link, default behavior is fine.
				// If it's Space on a link, default behavior might not be navigation.
				// However, since it's an <a>, Enter will work naturally.
				// We call handleClick to ensure menus close.
				handleClick(event as any);
			}
		}
	}
</script>

<li
	class="menu-item relative"
	class:active={isActiveBranch && !menu.children && menu.href}
	class:parent-active={isActiveBranch && menu.children}
	use:clickOutsideAction={handleClickOutside}
	onclickoutside={handleClickOutside}
>
	{#if menu.children}
		<a
			href={menu.href || '#'}
			role="button"
			aria-haspopup="true"
			aria-expanded={isOpen}
			class="trigger flex items-center gap-1"
			onclick={handleClick}
			onkeydown={handleKeyDown}
		>
			<span>{menu.label}</span>
			{#if isOpen}
				<svg
					width="22"
					height="22"
					viewBox="0 0 22 22"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						d="M6.41663 12.8333L11 8.24998L15.5833 12.8333"
						stroke="#5D28B6"
						stroke-width="2"
						stroke-linecap="round"
						stroke-linejoin="round"
					/>
				</svg>
			{:else}
				<svg
					width="22"
					height="22"
					viewBox="0 0 22 22"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						d="M6.41675 9.16669L11.0001 13.75L15.5834 9.16669"
						stroke="#5D28B6"
						stroke-width="2"
						stroke-linecap="round"
						stroke-linejoin="round"
					/>
				</svg>
			{/if}
		</a>
		{#if isOpen}
			<ul class="dropdown level-{level + 1}" role="menu" transition:slide|local={{ duration: 150 }}>
				{#each menu.children as child (child.routeId)}
					<Self menu={child} {routeId} level={level + 1} />
				{/each}
			</ul>
		{/if}
	{:else if menu.href}
		<a href={menu.href} onclick={handleClick} onkeydown={handleKeyDown}>{menu.label}</a>
	{:else}
		<span class="cursor-default">{menu.label}</span>
	{/if}
</li>

<style>
	li.menu-item {
		transition: color 0.3s ease;
		padding-bottom: 0.75rem;
	}

	li > a,
	li > button.trigger,
	li > span {
		text-decoration: none;
		color: inherit;
		background: none;
		border: none;
		padding: 0;
		font: inherit;
		text-align: left;
		display: inline-flex;
		align-items: center;
		position: relative;
		cursor: pointer;
	}
	li > span {
		cursor: default;
	}

	li > a::after,
	li > button.trigger::after {
		content: '';
		display: block;
		position: absolute;
		bottom: -0.5rem;
		left: 0;
		width: 0;
		height: 2px;
		background-color: var(--brand-purple);
		transition: width 0.3s ease-in-out;
	}
	li:hover > a:not(:focus-visible)::after,
	li:hover > button.trigger:not(:focus-visible)::after {
		width: 100%;
	}
	.active > a::after,
	.active > button.trigger::after,
	.parent-active > a::after,
	.parent-active > button.trigger::after {
		width: 100%;
		background-color: var(--brand-purple);
	}

	li.active.parent-active > a::after {
		width: 100%;
		background-color: var(--brand-purple);
	}

	.active > a,
	.active > button.trigger {
		font-weight: 600;
		color: var(--brand-purple);
	}
	.parent-active > a,
	.parent-active > button.trigger {
		color: var(--brand-purple);
	}

	.parent-active > a svg path {
		stroke: var(--brand-purple);
	}

	.active > a svg path {
		stroke: var(--brand-purple);
	}

	.dropdown {
		display: block;
		position: absolute;
		left: 0;
		top: 100%;
		margin: 0;
		padding: 0.5rem 0;
		list-style: none;
		background-color: white;
		border-radius: 0.375rem;
		box-shadow:
			0 4px 6px -1px rgb(0 0 0 / 0.1),
			0 2px 4px -2px rgb(0 0 0 / 0.1);
		min-width: 200px;
		z-index: 20;
	}
</style>
