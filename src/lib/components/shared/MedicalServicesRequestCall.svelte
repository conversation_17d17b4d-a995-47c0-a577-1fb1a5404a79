<script lang="ts">
	let {
		title = 'Staffing Solutions',
		subtitle = 'Connecting Business With The Right Talents',
		description = `Lorem ipsum dolor sit amet consectetur. Iaculis arcu ac praesent penatibus justo a arcu vitae. Integer.`
	} = $props();

	// Data for the 6 service cards based on the image
	const commonCardDescription = `Lorem ipsum dolor sit amet consectetur. Iaculis arcu ac praesent penatibus justo a arcu vitae. Integer.`;
	const services = [
		{
			id: 1,
			iconName: Dish,
			title: 'Permanent Service',
			description: `Secure top talent for long-term roles with our thorough recruitment process, ensuring
						the right fit for your organization's ongoing needs.`
		},
		{
			id: 2,
			iconName: BriefCase,
			title: 'Temporary Service',
			description: `Quickly fill temporary vacancies or short-term projects with experienced professionals
						who can step in and maintain productivity.`
		},
		{
			id: 3,
			iconName: DocumentIcon,
			title: 'Contract Services',
			description: `Access a pool of qualified medical staff, including locum doctors, nurses, and allied
						health professionals, available to cover shifts at short notice.`
		},
		{
			id: 4,
			iconName: SettingsWrenchIcon,
			title: 'Ad-hoc Staff Support Service',
			description: `			On-demand staffing for urgent needs, unexpected absences, or peak business periods –
						ensuring seamless operations without delays.`
		},
		{
			id: 5,
			iconName: HeartStethoscopeIcon,
			title: 'Medical Locums',
			description: `Expert locum medical professionals available for flexible assignments, providing specialized skills and immediate support to meet your healthcare service demands.
`
		},
		{
			id: 6,
			iconName: UserNurseIcon,
			title: 'Nurses',
			description: `Reliable, skilled nurses ready to deliver exceptional patient care, offering flexible staffing to support your team in diverse healthcare environments.
`
		}
	];
</script>

<section class="bg-white py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 max-w-3xl text-center md:mb-16">
			<p class="mb-2 text-sm font-bold tracking-wider text-[#FF6F00] uppercase">
				{title}
			</p>
			<h2 class="text-3xl leading-tight font-bold text-[#0586AB] md:text-4xl lg:text-5xl">
				{subtitle}
			</h2>
			<p class="mt-4 text-base text-[#737373]">
				{@html description}
			</p>
		</div>

		<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 md:gap-8">
			{#each services as service (service.id)}
				<div
					class="flex flex-col rounded-xl border border-gray-300 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md"
				>
					<div class="mb-4 inline-block">
						{@render service.iconName()}
					</div>
					<h4 class="mb-2 text-lg font-semibold text-gray-900">{service.title}</h4>
					<p class="flex-grow text-sm text-gray-600">
						{service.description}
					</p>
				</div>
			{/each}
		</div>

		<div class="mt-12">
			<a
				href="/services"
				class="inline-flex items-center justify-center rounded-full border border-transparent bg-[#5D28B6] px-8 py-3 text-center text-base font-medium text-white transition duration-200 hover:bg-purple-800 md:text-left"
			>
				Learn More
			</a>
		</div>
		<div class="flex w-full justify-center">
			<svg
				width="135"
				height="49"
				viewBox="0 0 135 49"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<circle cx="2.85781" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="2.85781" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="2.85781" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="2.85781" cy="45.725" r="2.85781" fill="#23A6F0" />
				<circle cx="60.0143" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="60.0143" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="60.0143" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="60.0143" cy="45.725" r="2.85781" fill="#23A6F0" />
				<circle cx="31.4362" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="31.4362" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="31.4362" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="31.4362" cy="45.725" r="2.85781" fill="#23A6F0" />
				<circle cx="88.5924" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="117.17" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="88.5924" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="117.17" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="88.5924" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="117.17" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="88.5924" cy="45.725" r="2.85781" fill="#23A6F0" />
				<circle cx="117.17" cy="45.725" r="2.85781" fill="#23A6F0" />
				<circle cx="17.1469" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="17.1469" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="17.1469" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="17.1469" cy="45.725" r="2.85781" fill="#23A6F0" />
				<circle cx="74.3034" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="74.3034" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="74.3034" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="74.3034" cy="45.725" r="2.85781" fill="#23A6F0" />
				<circle cx="45.7252" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="45.7252" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="45.7252" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="45.7252" cy="45.725" r="2.85781" fill="#23A6F0" />
				<circle cx="102.881" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="131.459" cy="2.85781" r="2.85781" fill="#23A6F0" />
				<circle cx="102.881" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="131.459" cy="31.4359" r="2.85781" fill="#23A6F0" />
				<circle cx="102.881" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="131.459" cy="17.1469" r="2.85781" fill="#23A6F0" />
				<circle cx="102.881" cy="45.725" r="2.85781" fill="#23A6F0" />
				<circle cx="131.459" cy="45.725" r="2.85781" fill="#23A6F0" />
			</svg>
		</div>
	</div>
</section>

{#snippet Dish()}
	<svg
		width="100"
		height="100"
		viewBox="0 0 100 100"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
		<path
			d="M34.9999 61.6667C34.5277 61.6667 34.1321 61.5068 33.8133 61.1868C33.4944 60.8668 33.3344 60.4712 33.3333 60.0001C33.3321 59.529 33.4921 59.1334 33.8133 58.8134C34.1344 58.4934 34.5299 58.3334 34.9999 58.3334H64.9999C65.4721 58.3334 65.8683 58.4934 66.1883 58.8134C66.5083 59.1334 66.6677 59.529 66.6666 60.0001C66.6655 60.4712 66.5055 60.8673 66.1866 61.1884C65.8677 61.5095 65.4721 61.669 64.9999 61.6667H34.9999ZM34.9999 56.6667V55.0001C34.9999 51.4445 36.0905 48.3056 38.2716 45.5834C40.4527 42.8612 43.251 41.139 46.6666 40.4168V40.0001C46.6666 39.0834 46.9933 38.299 47.6466 37.6468C48.2999 36.9945 49.0844 36.6679 49.9999 36.6668C50.9155 36.6656 51.7005 36.9923 52.3549 37.6468C53.0094 38.3012 53.3355 39.0856 53.3333 40.0001V40.4168C56.7777 41.139 59.5833 42.8612 61.7499 45.5834C63.9166 48.3056 64.9999 51.4445 64.9999 55.0001V56.6667H34.9999ZM38.4166 53.3334H61.5833C61.1944 50.4445 59.9027 48.0556 57.7083 46.1668C55.5138 44.2779 52.9444 43.3334 49.9999 43.3334C47.0555 43.3334 44.4933 44.2779 42.3133 46.1668C40.1333 48.0556 38.8344 50.4445 38.4166 53.3334Z"
			fill="#0586AB"
		/>
	</svg>
{/snippet}
{#snippet BriefCase()}
	<svg
		width="100"
		height="100"
		viewBox="0 0 100 100"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
		<path
			d="M41.6666 40V35H58.3333V40H61.6249L66.6666 51.5833V63.3333H33.3333V51.5833L38.3749 40H41.6666ZM44.9999 40H54.9999V38.3333H44.9999V40ZM41.6666 50V48.3333H44.9999V50H54.9999V48.3333H58.3333V50H62.3333L59.4999 43.3333H40.4999L37.6666 50H41.6666ZM41.6666 53.3333H36.6666V60H63.3333V53.3333H58.3333V55H54.9999V53.3333H44.9999V55H41.6666V53.3333Z"
			fill="#0586AB"
		/>
	</svg>
{/snippet}

{#snippet DocumentIcon()}
	<svg
		width="100"
		height="100"
		viewBox="0 0 100 100"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
		<path
			d="M40 66.6666C38.6111 66.6666 37.4306 66.1805 36.4583 65.2083C35.4861 64.236 35 63.0555 35 61.6666V56.6666H40V33.3333H65V61.6666C65 63.0555 64.5139 64.236 63.5417 65.2083C62.5694 66.1805 61.3889 66.6666 60 66.6666H40ZM60 63.3333C60.4722 63.3333 60.8683 63.1733 61.1883 62.8533C61.5083 62.5333 61.6678 62.1377 61.6667 61.6666V36.6666H43.3333V56.6666H58.3333V61.6666C58.3333 62.1388 58.4933 62.5349 58.8133 62.8549C59.1333 63.1749 59.5289 63.3344 60 63.3333ZM45 44.9999V41.6666H60V44.9999H45ZM45 49.9999V46.6666H60V49.9999H45Z"
			fill="#0586AB"
		/>
	</svg>
{/snippet}

{#snippet SettingsWrenchIcon()}
	<svg
		width="100"
		height="100"
		viewBox="0 0 100 100"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
		<path
			d="M44.6666 48.2499L48.2499 44.6249L45.9166 42.2499L44.0833 44.0833L41.7499 41.7499L43.5416 39.9166L41.6666 38.0416L38.0416 41.6666L44.6666 48.2499ZM58.3333 61.9583L61.9583 58.3333L60.0833 56.4583L58.2499 58.2499L55.9166 55.9166L57.7083 54.0833L55.3333 51.7499L51.7499 55.3333L58.3333 61.9583ZM42.0833 64.9999H34.9999V57.9166L42.2916 50.6249L33.3333 41.6666L41.6666 33.3333L50.6666 42.3333L59.3749 33.5833L66.3749 40.6666L57.7083 49.3749L66.6666 58.3333L58.3333 66.6666L49.3749 57.7083L42.0833 64.9999ZM38.3333 61.6666H40.6666L56.9999 45.3749L54.6249 42.9999L38.3333 59.3333V61.6666ZM55.8333 44.2083L54.6249 42.9999L56.9999 45.3749L55.8333 44.2083Z"
			fill="#0586AB"
		/>
	</svg>
{/snippet}

{#snippet HeartStethoscopeIcon()}
	<svg
		width="100"
		height="100"
		viewBox="0 0 100 100"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
		<path
			d="M44.6999 48.8166C44.5437 48.9715 44.4197 49.1559 44.3351 49.359C44.2505 49.5621 44.2069 49.7799 44.2069 49.9999C44.2069 50.2199 44.2505 50.4378 44.3351 50.6409C44.4197 50.844 44.5437 51.0283 44.6999 51.1833C45.0122 51.4937 45.4346 51.6679 45.8749 51.6679C46.3152 51.6679 46.7377 51.4937 47.0499 51.1833C47.2061 51.0283 47.3301 50.844 47.4148 50.6409C47.4994 50.4378 47.5429 50.2199 47.5429 49.9999C47.5429 49.7799 47.4994 49.5621 47.4148 49.359C47.3301 49.1559 47.2061 48.9715 47.0499 48.8166C46.7377 48.5062 46.3152 48.3319 45.8749 48.3319C45.4346 48.3319 45.0122 48.5062 44.6999 48.8166ZM48.8166 52.9499C48.5062 53.2622 48.3319 53.6846 48.3319 54.1249C48.3319 54.5652 48.5062 54.9877 48.8166 55.2999C48.9715 55.4561 49.1559 55.5801 49.359 55.6647C49.5621 55.7494 49.7799 55.7929 49.9999 55.7929C50.22 55.7929 50.4378 55.7494 50.6409 55.6647C50.844 55.5801 51.0283 55.4561 51.1833 55.2999C51.4937 54.9877 51.6679 54.5652 51.6679 54.1249C51.6679 53.6846 51.4937 53.2622 51.1833 52.9499C51.0283 52.7937 50.844 52.6697 50.6409 52.5851C50.4378 52.5005 50.22 52.4569 49.9999 52.4569C49.7799 52.4569 49.5621 52.5005 49.359 52.5851C49.1559 52.6697 48.9715 52.7937 48.8166 52.9499ZM63.8166 36.2833C62.0036 34.4553 59.5555 33.397 56.9818 33.3287C54.4082 33.2604 51.9073 34.1874 49.9999 35.9166C48.1013 34.2076 45.6207 33.2899 43.067 33.3519C40.5132 33.4139 38.08 34.4508 36.2666 36.2499C34.465 38.0663 33.428 40.5042 33.3691 43.0618C33.3102 45.6195 34.2338 48.1025 35.9499 49.9999C34.6669 51.4343 33.825 53.2085 33.5253 55.1095C33.2256 57.0105 33.4808 58.9576 34.2603 60.7172C35.0398 62.4768 36.3104 63.974 37.9198 65.0293C39.5291 66.0846 41.4088 66.6531 43.3333 66.6666C45.8003 66.6692 48.1788 65.7475 49.9999 64.0833C51.8994 65.7972 54.3836 66.7179 56.9413 66.6559C59.499 66.5939 61.9357 65.5539 63.7499 63.7499C65.5493 61.9314 66.5832 59.4923 66.639 56.9347C66.6948 54.377 65.7682 51.8952 64.0499 49.9999C65.7682 48.1046 66.6948 45.6228 66.639 43.0652C66.5832 40.5075 65.5493 38.0685 63.7499 36.2499L63.8166 36.2833ZM61.3833 38.6166C62.5423 39.8089 63.214 41.3913 63.2664 43.0533C63.3188 44.7153 62.7482 46.3369 61.6666 47.5999L52.3999 38.3333C53.674 37.2762 55.2906 36.7214 56.9453 36.7736C58.5999 36.8258 60.1784 37.4813 61.3833 38.6166ZM38.6166 61.3833C37.4575 60.1909 36.7859 58.6085 36.7335 56.9465C36.6811 55.2845 37.2517 53.6629 38.3333 52.3999L47.6666 61.7333C46.3784 62.7966 44.744 63.3487 43.0749 63.2841C41.4057 63.2196 39.8188 62.5429 38.6166 61.3833ZM61.3833 61.3833C60.1178 62.5935 58.4343 63.269 56.6833 63.269C54.9322 63.269 53.2487 62.5935 51.9833 61.3833L38.6499 48.0499C37.4083 46.8008 36.7113 45.1112 36.7113 43.3499C36.7113 41.5887 37.4083 39.899 38.6499 38.6499C39.899 37.4083 41.5887 36.7113 43.3499 36.7113C45.1112 36.7113 46.8008 37.4083 48.0499 38.6499L61.3833 51.9833C62.6249 53.2323 63.3219 54.922 63.3219 56.6833C63.3219 58.4445 62.6249 60.1342 61.3833 61.3833ZM52.9499 48.8166C52.7937 48.9715 52.6697 49.1559 52.5851 49.359C52.5005 49.5621 52.4569 49.7799 52.4569 49.9999C52.4569 50.2199 52.5005 50.4378 52.5851 50.6409C52.6697 50.844 52.7937 51.0283 52.9499 51.1833C53.2622 51.4937 53.6846 51.6679 54.1249 51.6679C54.5652 51.6679 54.9877 51.4937 55.2999 51.1833C55.4561 51.0283 55.5801 50.844 55.6648 50.6409C55.7494 50.4378 55.7929 50.2199 55.7929 49.9999C55.7929 49.7799 55.7494 49.5621 55.6648 49.359C55.5801 49.1559 55.4561 48.9715 55.2999 48.8166C54.9877 48.5062 54.5652 48.3319 54.1249 48.3319C53.6846 48.3319 53.2622 48.5062 52.9499 48.8166ZM48.8166 44.6999C48.5062 45.0122 48.3319 45.4346 48.3319 45.8749C48.3319 46.3152 48.5062 46.7377 48.8166 47.0499C48.9715 47.2061 49.1559 47.3301 49.359 47.4147C49.5621 47.4994 49.7799 47.5429 49.9999 47.5429C50.22 47.5429 50.4378 47.4994 50.6409 47.4147C50.844 47.3301 51.0283 47.2061 51.1833 47.0499C51.4937 46.7377 51.6679 46.3152 51.6679 45.8749C51.6679 45.4346 51.4937 45.0122 51.1833 44.6999C51.0283 44.5437 50.844 44.4197 50.6409 44.3351C50.4378 44.2505 50.22 44.2069 49.9999 44.2069C49.7799 44.2069 49.5621 44.2505 49.359 44.3351C49.1559 44.4197 48.9715 44.5437 48.8166 44.6999Z"
			fill="#0586AB"
		/>
	</svg>
{/snippet}

{#snippet UserNurseIcon()}
	<svg
		width="100"
		height="100"
		viewBox="0 0 100 100"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<rect width="100" height="100" rx="50" fill="#24BEE0" fill-opacity="0.1" />
		<path
			d="M56.9566 51.5268C56.9277 51.5123 56.8983 51.499 56.8683 51.4868C56.6383 51.3834 56.4116 51.2751 56.1766 51.1818C57.8123 49.8972 59.0067 48.1345 59.5936 46.1392C60.1805 44.1439 60.1306 42.0153 59.451 40.0497C58.7714 38.084 57.4958 36.3792 55.8018 35.1725C54.1078 33.9659 52.0798 33.3174 50 33.3174C47.9201 33.3174 45.8921 33.9659 44.1981 35.1725C42.5041 36.3792 41.2286 38.084 40.5489 40.0497C39.8693 42.0153 39.8195 44.1439 40.4064 46.1392C40.9932 48.1345 42.1876 49.8972 43.8233 51.1818C43.59 51.2751 43.3616 51.3834 43.1316 51.4868L43.0416 51.5268C40.4265 52.7205 38.1666 54.5734 36.4837 56.9039C34.8007 59.2344 33.7524 61.9623 33.4416 64.8201C33.418 65.0379 33.4375 65.2582 33.499 65.4684C33.5605 65.6787 33.6628 65.8747 33.8001 66.0455C33.9373 66.2162 34.1069 66.3582 34.299 66.4633C34.4912 66.5685 34.7022 66.6348 34.92 66.6585C35.1377 66.6821 35.358 66.6626 35.5683 66.6011C35.7785 66.5396 35.9746 66.4373 36.1453 66.3C36.316 66.1627 36.458 65.9932 36.5632 65.801C36.6684 65.6089 36.7347 65.3979 36.7583 65.1801C36.9854 63.0911 37.7061 61.086 38.861 59.3305C40.0158 57.5749 41.5716 56.1192 43.4 55.0834L48.82 60.5018C49.1325 60.8142 49.5564 60.9898 49.9983 60.9898C50.4402 60.9898 50.8641 60.8142 51.1766 60.5018L56.5966 55.0834C58.4256 56.1188 59.9821 57.5743 61.1375 59.3299C62.2929 61.0855 63.0142 63.0908 63.2416 65.1801C63.2859 65.588 63.4791 65.9651 63.7842 66.2394C64.0892 66.5137 64.4847 66.6659 64.895 66.6668C64.9562 66.6668 65.0174 66.6635 65.0783 66.6568C65.5176 66.609 65.92 66.3886 66.1969 66.0442C66.4738 65.6998 66.6026 65.2595 66.555 64.8201C66.2442 61.9623 65.1959 59.2344 63.5129 56.9039C61.8299 54.5734 59.5717 52.7205 56.9566 51.5268ZM43.4016 42.6568C43.5583 41.0154 44.3212 39.4913 45.5413 38.3821C46.7614 37.273 48.3511 36.6584 50 36.6584C51.6488 36.6584 53.2385 37.273 54.4586 38.3821C55.6787 39.4913 56.4416 41.0154 56.5983 42.6568H43.4016ZM50 56.9668L46.7716 53.7384C48.8896 53.1955 51.1103 53.1955 53.2283 53.7384L50 56.9668ZM50 50.0001C48.7022 49.9986 47.4332 49.6184 46.3484 48.9061C45.2637 48.1938 44.4104 47.1803 43.8933 45.9901H56.1066C55.5895 47.1803 54.7362 48.1938 53.6515 48.9061C52.5667 49.6184 51.2977 49.9986 50 50.0001Z"
			fill="#0586AB"
		/>
	</svg>
{/snippet}

<style>
	/* Add any additional styles if needed, for example, to precisely match icon backgrounds from image */
	/* The icon placeholder above uses a rect with fill-opacity for the background circle */
</style>
