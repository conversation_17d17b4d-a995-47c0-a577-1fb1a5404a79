<script lang="ts">
	import type { MenuItem } from '$lib/menuItems.svelte';
	import { slide } from 'svelte/transition';
	import Self from './MobileMenuItem.svelte';
	let { menu, routeId, onNav }: { menu: MenuItem; routeId: string; onNav: () => void } = $props();

	let isOpen = $state(false);

	function handleClick(event: MouseEvent) {
		if (menu.children) {
			event.preventDefault(); // Prevent navigation if it has children
			isOpen = !isOpen;
		} else if (menu.href) {
			// Only call onNav (which closes drawer) if it's a final link
			onNav();
			// Navigation happens via the href attribute
		} else {
			// Item without href and without children - might close drawer or do nothing
			onNav();
		}
	}

	// Check if current item or a child is active
	function checkActiveMobile(item: MenuItem, currentRouteId: string): boolean {
		if (item.routeId === currentRouteId && item.href) {
			// Only active if it has a link
			return true;
		}
		if (item.children) {
			return item.children.some((child) => checkActiveMobile(child, currentRouteId));
		}
		return false;
	}
	let isActiveBranchMobile = $derived(checkActiveMobile(menu, routeId));
</script>

<li class:active={isActiveBranchMobile}>
	<a href={menu.href ?? '#'} class="flex items-center justify-between" onclick={handleClick}>
		<span>{menu.label}</span>
		{#if menu.children}
			<svg
				xmlns="http://www.w3.org/2000/svg"
				viewBox="0 0 20 20"
				fill="currentColor"
				class="h-5 w-5 transition-transform duration-200 {isOpen ? 'rotate-90' : ''}"
			>
				<path
					fill-rule="evenodd"
					d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
					clip-rule="evenodd"
				/>
			</svg>
		{/if}
	</a>

	{#if menu.children && isOpen}
		<ul class="submenu" transition:slide={{ duration: 250 }}>
			{#each menu.children as child (child.routeId)}
				<Self menu={child} {routeId} {onNav} />
			{/each}
		</ul>
	{/if}
</li>

<style>
	/* Base styles for list items */
	li {
		/* Styles from MobileMenu.svelte */
	}

	li a {
		display: flex; /* Changed to flex */
		justify-content: space-between; /* Space between label and icon */
		align-items: center;
		padding: 0.9rem 1.5rem;
		text-decoration: none;
		color: #333;
		font-size: 1rem;
		font-weight: 500;
		border-left: 4px solid transparent;
		transition:
			background-color 0.2s ease,
			border-left-color 0.2s ease,
			color 0.2s ease;
		cursor: pointer; /* Make clickable */
	}

	/* Hover state */
	li a:hover {
		background-color: #f5f5f5;
		color: #111;
	}

	/* Active state - Apply to the li based on isActiveBranchMobile */
	li.active > a {
		color: var(--brand-purple);
		font-weight: 600;
		border-left-color: var(--brand-purple);
		background-color: rgba(128, 0, 128, 0.05);
	}
	li.active > a span {
		/* Ensure text color is purple */
		color: var(--brand-purple);
	}

	/* Submenu UL styles */
	.submenu {
		list-style: none;
		padding: 0;
		margin: 0;
		/* background-color: #fafafa; */ /* Optional slight background difference */
		overflow: hidden; /* Needed for slide transition */
	}

	/* Chevron icon inherits color */
	svg {
		color: #666; /* Default icon color */
		transition: transform 0.2s ease-out;
	}
	li a:hover svg {
		color: #111;
	}
	li.active > a svg {
		color: var(--brand-purple);
	}
</style>
