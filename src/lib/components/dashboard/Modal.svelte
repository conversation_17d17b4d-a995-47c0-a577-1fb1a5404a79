<script lang="ts">
	export let id: string = 'modal';
	export let title: string = 'Modal Title';
	export let size: string = 'modal-lg'; // Default size
	export let onSave: () => void = () => {}; // Callback for save button
	export let showSaveButton: boolean = true; // Toggle save button visibility
	export let saveButtonText: string = 'Save';
	export let cancelButtonText: string = 'Cancel';
</script>

<div class="modal fade" {id} tabindex="-1" aria-labelledby={`${id}Label`} aria-hidden="true">
	<div class={`modal-dialog ${size}`}>
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id={`${id}Label`}>{title}</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<slot></slot>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
					{cancelButtonText}
				</button>
				{#if showSaveButton}
					<button type="button" class="btn btn-primary" on:click={onSave}>
						{saveButtonText}
					</button>
				{/if}
			</div>
		</div>
	</div>
</div>
