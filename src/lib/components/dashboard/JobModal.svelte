<script lang="ts">
	import Modal from './Modal.svelte';

	let dispatchTarget: HTMLElement;

	function dispatch(event: string, detail: any) {
		dispatchTarget?.dispatchEvent(new CustomEvent(event, { detail }));
	}

	// Modal visibility state
	export let addModalOpen: boolean = false;
	export let viewModalOpen: boolean = false;
	export let editModalOpen: boolean = false;

	// Open Edit Modal with provided job data
	function openEditModal(jobData) {
		job = { ...jobData }; // Copy job data to avoid mutation of original object
		editModalOpen = true;
	}

	// Form data for Add/Edit
	let job = {
		title: '',
		company: '',
		type: '',
		location: '',
		salary: '',
		experience: '',
		description: '',
		requirements: '',
		deadline: '',
		status: 'active'
	};

	// Triggered when Save is clicked in Add Modal
	function saveJob() {
		// Dispatch event to parent with new job data
		dispatch('save', { ...job });
		addModalOpen = false;
	}

	// Triggered when Save Changes is clicked in Edit Modal
	function saveChanges() {
		dispatch('update', { ...job });
		editModalOpen = false;
	}

	// Open View Modal with provided job data
	function openViewModal(jobData: typeof job) {
		job = { ...jobData };
		viewModalOpen = true;
	}

	// Handle transitioning from View Modal to Edit Modal
	function handleEditFromView() {
		editModalOpen = true;
		viewModalOpen = false;
	}
</script>

<div bind:this={dispatchTarget}></div>

<!-- Add Job Modal -->
{#if addModalOpen}
	<Modal id="addJobModal" title="Add New Job Listing" onSave={saveJob}>
		<form>
			<div class="row mb-3">
				<div class="col-md-6">
					<label for="jobTitle" class="form-label">Job Title</label>
					<input
						type="text"
						class="form-control"
						id="jobTitle"
						placeholder="e.g. Senior Software Engineer"
						required
					/>
				</div>
				<div class="col-md-6">
					<label for="company" class="form-label">Company</label>
					<select class="form-select" id="company" required>
						<option value="" selected disabled>Select company</option>
						<option>Tech Solutions Inc.</option>
						<option>Innovate Corp</option>
						<option>Creative Minds LLC</option>
						<option>Analytics Pro</option>
						<option>Growth Marketing</option>
						<option>Cloud Systems</option>
						<option>WebTech Solutions</option>
					</select>
				</div>
			</div>

			<div class="row mb-3">
				<div class="col-md-6">
					<label for="jobType" class="form-label">Job Type</label>
					<select class="form-select" id="jobType" required>
						<option value="" selected disabled>Select job type</option>
						<option>Full-time</option>
						<option>Part-time</option>
						<option>Contract</option>
						<option>Internship</option>
						<option>Remote</option>
					</select>
				</div>
				<div class="col-md-6">
					<label for="location" class="form-label">Location</label>
					<input
						type="text"
						class="form-control"
						id="location"
						placeholder="e.g. San Francisco, CA or Remote"
						required
					/>
				</div>
			</div>

			<div class="row mb-3">
				<div class="col-md-6">
					<label for="salary" class="form-label">Salary Range</label>
					<input
						type="text"
						class="form-control"
						id="salary"
						placeholder="e.g. $90,000 - $120,000"
					/>
				</div>
				<div class="col-md-6">
					<label for="experience" class="form-label">Experience Level</label>
					<select class="form-select" id="experience">
						<option value="" selected disabled> Select experience level </option>
						<option>Entry Level</option>
						<option>Mid Level</option>
						<option>Senior Level</option>
						<option>Executive</option>
					</select>
				</div>
			</div>

			<div class="mb-3">
				<label for="description" class="form-label">Job Description</label>
				<textarea
					class="form-control"
					id="description"
					rows="5"
					placeholder="Enter detailed job description..."
					required
				></textarea>
			</div>

			<div class="mb-3">
				<label for="requirements" class="form-label">Requirements</label>
				<textarea
					class="form-control"
					id="requirements"
					rows="3"
					placeholder="Enter job requirements..."
					required
				></textarea>
			</div>

			<div class="row mb-3">
				<div class="col-md-6">
					<label for="applicationDeadline" class="form-label">Application Deadline</label>
					<input type="date" class="form-control" id="applicationDeadline" />
				</div>
				<div class="col-md-6">
					<label for="status" class="form-label">Status</label>
					<select class="form-select" id="status" required>
						<option value="active" selected>Active</option>
						<option value="inactive">Inactive</option>
						<option value="pending">Pending</option>
					</select>
				</div>
			</div>
		</form>
	</Modal>
{/if}

<!-- View Job Modal -->
<!-- {#if viewModalOpen}
	<div class="modal fade show" style="display: block" aria-modal="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">{job.title}</h5>
					<button
						type="button"
						class="btn-close"
						on:click={() => (viewModalOpen = false)}
						aria-label="Close"
					></button>
				</div>
				<div class="modal-body">
					<div class="row mb-4">
						<div class="col-md-6">
							<p><strong>Company:</strong> {job.company}</p>
							<p><strong>Location:</strong> {job.location}</p>
							<p>
								<strong>Type:</strong>
								<span class="badge bg-primary">{job.type}</span>
							</p>
						</div>
						<div class="col-md-6">
							<p><strong>Salary:</strong> {job.salary}</p>
							<p><strong>Experience:</strong> {job.experience}</p>
							<p>
								<strong>Status:</strong>
								<span class="status-active">{job.status}</span>
							</p>
						</div>
					</div>
					<div class="mb-4">
						<h6>Job Description</h6>
						<p>{job.description}</p>
					</div>
					<div class="mb-4">
						<h6>Requirements</h6>
						{#each job.requirements.split('\n') as reqs}
							{#if reqs.trim() !== ''}
								<ul>
									<li>{reqs}</li>
								</ul>
							{/if}
						{/each}
					</div>
					<div class="mb-3">
						<h6>Additional Information</h6>
						<p><strong>Posted:</strong> May 12, 2023</p>
						<p><strong>Applications:</strong> 124</p>
						<p><strong>Deadline:</strong> {job.deadline}</p>
					</div>
					<button type="button" class="btn btn-primary" on:click={handleEditFromView}>
						Edit Job
					</button>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" on:click={() => (viewModalOpen = false)}>
						Close
					</button>
				</div>
			</div>
		</div>
	</div>
{/if} -->

<!-- Edit Job Modal -->
{#if editModalOpen}
	<div class="modal fade show" style="display: block" aria-modal="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Edit Job Listing</h5>
					<button
						type="button"
						class="btn-close"
						on:click={() => (editModalOpen = false)}
						aria-label="Close"
					></button>
				</div>
				<div class="modal-body">
					<form>
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="editJobTitle" class="form-label">Job Title</label>
								<input
									type="text"
									class="form-control"
									id="editJobTitle"
									bind:value={job.title}
									required
								/>
							</div>
							<div class="col-md-6">
								<label for="editCompany" class="form-label">Company</label>
								<select class="form-select" id="editCompany" bind:value={job.company} required>
									<option>Tech Solutions Inc.</option>
									<option>Innovate Corp</option>
									<option>Creative Minds LLC</option>
									<option>Analytics Pro</option>
									<option>Growth Marketing</option>
									<option>Cloud Systems</option>
									<option>WebTech Solutions</option>
								</select>
							</div>
						</div>
						<!-- Repeat same structure as Add Modal for other fields -->
						<!-- Omitted for brevity but follow the same pattern -->
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="editJobType" class="form-label">Job Type</label>
								<select class="form-select" id="editJobType" bind:value={job.type} required>
									<option>Full-time</option>
									<option>Part-time</option>
									<option>Contract</option>
									<option>Internship</option>
									<option>Remote</option>
								</select>
							</div>
							<div class="col-md-6">
								<label for="editLocation" class="form-label">Location</label>
								<input
									type="text"
									class="form-control"
									id="editLocation"
									bind:value={job.location}
									required
								/>
							</div>
						</div>
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="editSalary" class="form-label">Salary Range</label>
								<input type="text" class="form-control" id="editSalary" bind:value={job.salary} />
							</div>
							<div class="col-md-6">
								<label for="editExperience" class="form-label">Experience Level</label>
								<select class="form-select" id="editExperience" bind:value={job.experience}>
									<option>Entry Level</option>
									<option>Mid Level</option>
									<option>Senior Level</option>
									<option>Executive</option>
								</select>
							</div>
						</div>
						<div class="mb-3">
							<label for="editDescription" class="form-label">Job Description</label>
							<textarea
								class="form-control"
								id="editDescription"
								rows="5"
								bind:value={job.description}
							>
							</textarea>
						</div>
						<div class="mb-3">
							<label for="editRequirements" class="form-label">Requirements</label>
							<textarea
								class="form-control"
								id="editRequirements"
								rows="3"
								bind:value={job.requirements}
							>
							</textarea>
						</div>
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="editDeadline" class="form-label">Application Deadline</label>
								<input
									type="date"
									class="form-control"
									id="editDeadline"
									bind:value={job.deadline}
								/>
							</div>
							<div class="col-md-6">
								<label for="editStatus" class="form-label">Status</label>
								<select class="form-select" id="editStatus" bind:value={job.status} required>
									<option value="active">Active</option>
									<option value="inactive">Inactive</option>
									<option value="pending">Pending</option>
								</select>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" on:click={() => (editModalOpen = false)}>
						Cancel
					</button>
					<button type="button" class="btn btn-primary" on:click={saveChanges}>
						Save Changes
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}
