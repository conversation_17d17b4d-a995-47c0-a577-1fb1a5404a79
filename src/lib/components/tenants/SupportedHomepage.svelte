<script lang="ts">
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ServiceSplitScreen from '$lib/components/shared/ServiceSplitScreen.svelte';
	import Subscribe from '$lib/components/home/<USER>';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';

	const h1 = 'SUPPORTED LIVING';
	const p =
		'Your Life, Your Home, Your Support – Empowered Living for Adults. At Centred Healthcare, we provide supported living services that enable adults and young people with disabilities, mental health conditions, and complex needs to live independently in their own homes with the support they need, when they need it. We work closely with each individual, their families, and professionals to create personalised support packages that are empowering, respectful, and effective.';

	const coreServices = [
		{
			title: 'Tenancy Management',
			description: 'Assistance with tenancy agreements and liaising with landlords.'
		},
		{
			title: 'Budgeting, Benefits & Bills',
			description: 'Help with managing money, benefits, and accessing financial entitlements.'
		},
		{
			title: 'Meal Planning & Cooking',
			description: 'Support with meal preparation and nutrition planning.'
		},
		{
			title: 'Household Tasks',
			description: 'Support with cleaning, laundry, and maintaining home safety.'
		},
		{
			title: 'Accessing Health Services',
			description: 'Help with GP visits, dentist appointments, and prescription management.'
		},
		{
			title: 'Medication Support',
			description: 'Assistance with medication management and reminders.'
		},
		{
			title: 'Social & Recreational Activities',
			description: 'Encouragement and support to engage in social and fun activities.'
		},
		{
			title: 'Support into Work & Education',
			description: 'Guidance for work, volunteering, or educational opportunities.'
		},
		{
			title: 'Developing Life Skills',
			description: 'Guidance on travel, communication, and personal safety to build confidence.'
		}
	];

	const whoWeSupportList: ListItem[] = [
		{ text: 'Adults (18+) with learning disabilities or autism' },
		{ text: 'People with mental health needs' },
		{ text: 'Those with physical or sensory impairments' },
		{ text: 'Individuals transitioning from hospital, residential care, or family homes' },
		{ text: 'People at risk of homelessness or social isolation' }
	];

	const whyChooseUsList: ListItem[] = [
		{ text: 'Clear tenancy rights and service user autonomy' },
		{ text: 'Small, safe homes across Milton Keynes and surrounding areas' },
		{ text: 'Transparent processes and open communication' },
		{ text: 'Culturally aware, diverse staff team' },
		{ text: 'Proven track record of helping individuals thrive' },
		{ text: 'Compliance with CQC and all safeguarding standards' }
	];

	const processSteps = [
		{
			step: 1,
			title: 'Referral & Enquiry',
			description: 'We receive referrals from families, social workers, or other professionals.'
		},
		{
			step: 2,
			title: 'Assessment',
			description:
				'We review individual needs, risks, goals, and support preferences collaboratively.'
		},
		{
			step: 3,
			title: 'Housing & Transition',
			description:
				'We assist in identifying a suitable property and coordinate a smooth transition.'
		},
		{
			step: 4,
			title: 'Ongoing Support & Reviews',
			description:
				'We provide regular check-ins and reviews to adapt the support plan as needs evolve.'
		}
	];
</script>

<svelte:head>
	<title>Supported Living Services | Centred Healthcare Living</title>
	<meta
		name="description"
		content="Empowering adults with disabilities and complex needs to live independently with our tailored supported living services."
	/>
</svelte:head>

{#snippet HeroImage()}
	<img
		src="/services/04.avif"
		alt="A person enjoying their independent living space"
		class="h-full w-full object-cover"
	/>
{/snippet}
<PoorCarousel
	{h1}
	{p}
	image={HeroImage}
	ctaText="Make an Enquiry"
	ctaLink="/contact#callback"
	showSecond={false}
	baseClass="bg-white"
/>

<!-- Coming Soon Banner -->
<section class="bg-gradient-to-r from-teal-500 to-teal-600 py-6">
	<div class="central-p mx-auto px-4">
		<div class="flex items-center justify-center">
			<div class="flex items-center space-x-4 text-center">
				<div class="flex items-center space-x-2">
					<svg
						class="h-8 w-8 animate-pulse text-white"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
						/>
					</svg>
					<span class="text-2xl font-bold text-white">Coming Soon</span>
				</div>
				<div class="hidden h-8 w-px bg-white/30 sm:block"></div>
				<div class="text-white">
					<p class="text-lg font-medium">Supported Living Services</p>
					<p class="text-sm opacity-90">
						These services are currently being developed and will be available soon
					</p>
				</div>
			</div>
		</div>
	</div>
</section>

<section id="services" class="bg-gray-50 py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 text-center">
			<h2 class="text-3xl font-bold text-[#0586AB]">Our Support Services</h2>
		</div>
		<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
			{#each coreServices as service}
				<div
					class="flex flex-col rounded-lg bg-white p-6 shadow-md transition-shadow hover:shadow-xl"
				>
					<h3 class="mb-2 text-xl font-semibold">{service.title}</h3>
					<p class="flex-grow text-sm text-gray-600">{service.description}</p>
				</div>
			{/each}
		</div>
	</div>
</section>

{#snippet WhoWeSupportImage()}
	<div class="h-full w-full">
		<img
			src="/fold/13.jpg"
			alt="A diverse community of people"
			class="h-full w-full rounded-lg object-cover shadow-lg"
		/>
	</div>
{/snippet}
<ServiceSplitScreen
	topLabel="WHO WE SUPPORT"
	mainHeading="Empowering a Wide Range of Individuals"
	listItems={whoWeSupportList}
	imagePosition="left"
	imageSnippet={WhoWeSupportImage}
	baseClass="bg-white"
/>

<section id="process" class="bg-gray-50 py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 text-center">
			<h2 class="text-3xl font-bold text-[#0586AB]">Our Process</h2>
		</div>
		<div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
			{#each processSteps as item}
				<div class="text-center">
					<div
						class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-teal-100 text-3xl font-bold text-teal-700"
					>
						{item.step}
					</div>
					<h3 class="mb-2 text-lg font-semibold">{item.title}</h3>
					<p class="text-sm text-gray-600">{item.description}</p>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Why Choose Us Section -->
<section class="bg-white py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 max-w-3xl text-center">
			<h2 class="text-3xl font-bold text-[#0586AB]">Why Centred Healthcare?</h2>
		</div>
		<div class="mx-auto max-w-4xl">
			<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
				{#each whyChooseUsList as reason}
					<div class="flex items-start space-x-3">
						<div class="flex-shrink-0">
							<svg class="h-6 w-6 text-teal-600" fill="currentColor" viewBox="0 0 20 20">
								<path
									fill-rule="evenodd"
									d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
									clip-rule="evenodd"
								/>
							</svg>
						</div>
						<p class="text-gray-700">{reason.text}</p>
					</div>
				{/each}
			</div>
		</div>
	</div>
</section>

<Subscribe />
