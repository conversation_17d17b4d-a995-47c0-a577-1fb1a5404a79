<script lang="ts">
	import Carousel from '$lib/components/home/<USER>';
	import Services from '$lib/components/home/<USER>';
	import Stats from '$lib/components/home/<USER>';
	import About from '$lib/components/home/<USER>';
	import Medical from '$lib/components/home/<USER>';
	import Subscribe from '$lib/components/home/<USER>';

	let { data } = $props();
</script>

<svelte:head>
	<title>Healthcare, Commercial & Industrial Staffing Solutions | Central Staffing</title>
	<meta
		name="description"
		content="Central Staffing connects top talent with opportunities in healthcare, commercial, and industrial sectors across the UK. Discover our domiciliary care, supported living, and bespoke recruitment services."
	/>
	<meta
		name="keywords"
		content="healthcare staffing, industrial recruitment, commercial staffing, care services UK, domiciliary care, supported living UK, nurse agency, Central Staffing, Milton Keynes recruitment agency"
	/>
</svelte:head>

<Carousel />
<Stats />
<Services />
<About />
<div class="mt-12 md:mt-[300px]">
	<Medical />
</div>
<Subscribe />
