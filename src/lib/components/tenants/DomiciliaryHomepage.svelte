<script lang="ts">
	import PoorCarousel from '$lib/components/shared/PoorCarousel.svelte';
	import ServiceSplitScreen from '$lib/components/shared/ServiceSplitScreen.svelte';
	import Subscribe from '$lib/components/home/<USER>';
	import type { ListItem } from '$lib/components/shared/SplitSection.svelte';

	const h1 = 'DOMICILIARY CARE';
	const p =
		'Welcome to Personalised Home Care That Puts You First. We support you or your loved one in the place that matters most, your own home.';

	const coreServices = [
		{
			title: 'Personal Care',
			description:
				'Assistance with bathing, grooming, dressing, toileting, and continence management.'
		},
		{
			title: 'Medication Assistance',
			description:
				'Prompting, supervision, and administration of medication (including collection from pharmacy).'
		},
		{
			title: 'Meal Preparation & Nutrition',
			description: 'Preparing meals based on your preferences and dietary requirements.'
		},
		{
			title: 'Housekeeping Support',
			description: 'Help with cleaning, laundry, and maintaining a safe living space.'
		},
		{
			title: 'Companionship & Sitting Services',
			description: 'Friendly support to combat loneliness and provide a listening ear.'
		},
		{
			title: 'Live-in Care & Overnight Support',
			description: '24-hour or night-time care options for round-the-clock peace of mind.'
		},
		{ title: 'Respite Care', description: 'Temporary relief for family caregivers.' },
		{
			title: 'Palliative & End-of-Life Care',
			description: "Dignified and compassionate support in life's final stages."
		},
		{
			title: 'Hospital Discharge Support',
			description: 'Helping you settle back into your home after a hospital stay.'
		}
	];

	const whoWeSupportList: ListItem[] = [
		{ text: 'Older adults (65+)' },
		{ text: 'Adults and children with physical or sensory disabilities' },
		{ text: 'Individuals recovering from illness or surgery' },
		{ text: 'People living with dementia or mental health challenges' },
		{ text: 'Adults and young adults with learning disabilities or autism' }
	];

	const faqs = [
		{
			question: 'How quickly can care be arranged?',
			answer:
				'We aim to begin care within 24-72 hours of assessment, depending on urgency and availability.'
		},
		{
			question: 'Can I choose my carer?',
			answer:
				'Yes, we make every effort to match you with a carer who suits your personality, needs, and preferences.'
		},
		{
			question: 'Do you support complex medical needs?',
			answer:
				'Yes. Our team includes trained staff who can work with district nurses, GPs, and other professionals.'
		}
	];
</script>

<svelte:head>
	<title>Domiciliary Care Services | Centred Healthcare at Home</title>
	<meta
		name="description"
		content="Personalised domiciliary care services from Centred Healthcare, providing support for daily tasks, personal care, and companionship in your own home."
	/>
</svelte:head>

{#snippet HeroImage()}
	<img
		src="/services/06.avif"
		alt="Compassionate domiciliary care"
		class="h-full w-full object-cover object-center"
	/>
{/snippet}
<PoorCarousel
	{h1}
	{p}
	image={HeroImage}
	ctaText="Request a Free Assessment"
	ctaLink="/contact#callback"
	showSecond={false}
	baseClass="bg-white"
/>

<section id="services" class="bg-gray-50 py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 max-w-3xl text-center md:mb-16">
			<h2 class="text-3xl font-bold text-[#0586AB]">Our Services at a Glance</h2>
		</div>
		<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
			{#each coreServices as service}
				<div
					class="flex flex-col rounded-lg bg-white p-6 shadow-md transition-shadow hover:shadow-xl"
				>
					<h3 class="mb-2 text-xl font-semibold text-gray-900">{service.title}</h3>
					<p class="flex-grow text-sm text-gray-600">{service.description}</p>
				</div>
			{/each}
		</div>
	</div>
</section>

{#snippet WhoWeSupportImage()}
	<div class="h-full w-full">
		<img
			src="/fold/nurses-1.avif"
			alt="A diverse group of individuals"
			class="h-full w-full rounded-lg object-cover shadow-lg"
		/>
	</div>
{/snippet}
<ServiceSplitScreen
	topLabel="WHO WE SUPPORT"
	mainHeading="Care for a Range of Conditions and Needs"
	listItems={whoWeSupportList}
	imagePosition="left"
	imageSnippet={WhoWeSupportImage}
	baseClass="bg-white"
/>

<section id="faq" class="bg-gray-50 py-16 md:py-24">
	<div class="central-p mx-auto px-4">
		<div class="mx-auto mb-12 text-center">
			<h2 class="text-3xl font-bold text-[#0586AB]">Frequently Asked Questions</h2>
		</div>
		<div class="mx-auto max-w-3xl space-y-4">
			{#each faqs as faq}
				<details class="group rounded-lg bg-white p-6 shadow-md">
					<summary class="flex cursor-pointer items-center justify-between font-semibold"
						>{faq.question}<span class="transform transition-transform group-open:rotate-180"
							>&darr;</span
						></summary
					>
					<p class="mt-4 text-gray-700">{faq.answer}</p>
				</details>
			{/each}
		</div>
	</div>
</section>

<Subscribe />
