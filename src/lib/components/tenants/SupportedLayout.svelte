<script lang="ts">
	import Logo from '$lib/components/shared/Logo.svelte';
	let { children } = $props();
</script>

<div class="tenant-layout bg-gray-50 text-gray-800">
	<header class="bg-white shadow-md">
		<div class="central-p flex items-center justify-between">
			<a href="/" class="logo w-32 md:w-36">
				<Logo />
			</a>
			<nav class="flex items-center gap-4 md:gap-6">
				<a href="#services" class="text-sm font-medium hover:text-teal-700">Our Support</a>
				<a href="#process" class="text-sm font-medium hover:text-teal-700">Our Process</a>
				<a
					href="/contact#callback"
					class="rounded-full bg-[#0586AB] px-4 py-2 text-sm font-semibold text-white transition hover:bg-teal-700"
				>
					Make an Enquiry
				</a>
			</nav>
		</div>
	</header>

	<main>
		{@render children()}
	</main>

	<footer class="bg-[#2F2246] py-8 text-white">
		<div class="central-p text-center">
			<h3 class="font-bold">Centred Healthcare Living Services</h3>
			<p class="mt-2 text-sm">433a Margaret Powell House, Milton Keynes MK9 3BN</p>
			<p class="text-sm">Call: 01908 915205 | Email: <EMAIL></p>
			<p class="mt-4 text-xs text-gray-400">
				&copy; {new Date().getFullYear()} Centred Healthcare. All Rights Reserved.
			</p>
		</div>
	</footer>
</div>
