<script>
	import DownSquick from '../shared/DownSquick.svelte';
</script>

{#snippet InnerIcon()}
	<span class="absolute top-0 right-0 z-10 h-[201px] w-[58px] md:h-[403px] md:w-[116px]">
		<svg
			width="100%"
			height="100%"
			viewBox="0 0 116 403"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<g filter="url(#filter0_f_492_2125)">
				<path
					d="M372.605 237.292L218.926 379.006L71.3336 218.952C32.2005 176.515 34.8789 110.389 77.3162 71.2558C119.753 32.1226 185.879 34.8011 225.012 77.2383L372.605 237.292Z"
					fill="#94B4B8"
				/>
				<path
					d="M218.935 378.791L71.4451 218.849C32.3691 176.474 35.0442 110.444 77.4196 71.3677C119.795 32.2917 185.824 34.9662 224.9 77.3415L372.39 237.284L218.935 378.791Z"
					stroke="#94B4B9"
					stroke-width="0.304287"
				/>
			</g>
			<defs>
				<filter
					id="filter0_f_492_2125"
					x="-23.5233"
					y="-23.6011"
					width="420.128"
					height="426.607"
					filterUnits="userSpaceOnUse"
					color-interpolation-filters="sRGB"
				>
					<feFlood flood-opacity="0" result="BackgroundImageFix" />
					<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
					<feGaussianBlur stdDeviation="12" result="effect1_foregroundBlur_492_2125" />
				</filter>
			</defs>
		</svg>
	</span>
{/snippet}
<section class=" bg-white py-16 font-sans">
	<div class="central-p relative">
		{@render InnerIcon()}
		<div class="mb-4 flex justify-center lg:justify-start">
			<div class="mx-auto">
				<DownSquick />
			</div>
		</div>
		<div class="flex flex-col items-center gap-y-12 lg:flex-row lg:gap-x-16">
			<div class="relative order-2 w-full px-4 sm:px-0 lg:order-1 lg:w-1/2">
				<div class="relative lg:h-[500px]">
					<div class="image relative z-10 flex w-3/4 gap-12 sm:w-2/3 lg:w-3/5">
						<img
							src="/about/01.avif"
							alt="Team "
							class="aspect-[3/4] w-full rounded-lg object-cover shadow-lg"
						/>
						<div class="placeholder-starburst md:mt-24 md:ml-12">
							<svg
								width="114"
								height="112"
								viewBox="0 0 114 112"
								fill="none"
								xmlns="http://www.w3.org/2000/svg"
							>
								<ellipse
									cx="2.10613"
									cy="2.13538"
									rx="2.10613"
									ry="2.13538"
									transform="matrix(1 0 0 -1 78.1934 74.4576)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="2.80817"
									cy="2.84717"
									rx="2.80817"
									ry="2.84717"
									transform="matrix(1 0 0 -1 82.4058 80.152)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="3.51021"
									cy="3.55896"
									rx="3.51021"
									ry="3.55896"
									transform="matrix(1 0 0 -1 88.0217 87.2699)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="4.21225"
									cy="4.27075"
									rx="4.21225"
									ry="4.27075"
									transform="matrix(1 0 0 -1 95.0426 95.8114)"
									fill="#00B5E9"
								/>
								<ellipse cx="78.8951" cy="39.1925" rx="2.10613" ry="2.13538" fill="#00B5E9" />
								<ellipse cx="83.8092" cy="34.21" rx="2.80817" ry="2.84717" fill="#00B5E9" />
								<ellipse cx="90.1278" cy="27.8038" rx="3.51021" ry="3.55896" fill="#00B5E9" />
								<ellipse cx="97.8498" cy="19.9741" rx="4.21225" ry="4.27075" fill="#00B5E9" />
								<ellipse
									cx="39.5806"
									cy="72.3223"
									rx="2.10613"
									ry="2.13538"
									transform="rotate(180 39.5806 72.3223)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="34.6663"
									cy="77.3048"
									rx="2.80817"
									ry="2.84717"
									transform="rotate(180 34.6663 77.3048)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="28.3481"
									cy="83.7109"
									rx="3.51021"
									ry="3.55896"
									transform="rotate(180 28.3481 83.7109)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="20.6259"
									cy="91.5406"
									rx="4.21225"
									ry="4.27075"
									transform="rotate(180 20.6259 91.5406)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="2.10613"
									cy="2.13538"
									rx="2.10613"
									ry="2.13538"
									transform="matrix(-1 0 0 1 40.2833 37.0571)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="2.80817"
									cy="2.84717"
									rx="2.80817"
									ry="2.84717"
									transform="matrix(-1 0 0 1 36.0708 31.3628)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="3.51021"
									cy="3.55896"
									rx="3.51021"
									ry="3.55896"
									transform="matrix(-1 0 0 1 30.4542 24.2449)"
									fill="#00B5E9"
								/>
								<ellipse
									cx="4.21225"
									cy="4.27075"
									rx="4.21225"
									ry="4.27075"
									transform="matrix(-1 0 0 1 23.4341 15.7034)"
									fill="#00B5E9"
								/>
								<circle
									cx="2.1208"
									cy="2.1208"
									r="2.1208"
									transform="matrix(0.702213 0.711966 -0.702213 0.711966 80.9564 52.5438)"
									fill="#00B5E9"
								/>
								<circle
									cx="2.82774"
									cy="2.82774"
									r="2.82774"
									transform="matrix(0.702213 0.711966 -0.702213 0.711966 87.9067 51.5371)"
									fill="#00B5E9"
								/>
								<circle
									cx="3.53467"
									cy="3.53467"
									r="3.53467"
									transform="matrix(0.702213 0.711966 -0.702213 0.711966 96.8422 50.5305)"
									fill="#00B5E9"
								/>
								<circle
									cx="4.2416"
									cy="4.2416"
									r="4.2416"
									transform="matrix(0.702213 0.711966 -0.702213 0.711966 107.764 49.5239)"
									fill="#00B5E9"
								/>
								<circle
									cx="2.1208"
									cy="2.1208"
									r="2.1208"
									transform="matrix(-0.702213 0.711966 0.702213 0.711966 33.7376 52.5439)"
									fill="#00B5E9"
								/>
								<circle
									cx="2.82774"
									cy="2.82774"
									r="2.82774"
									transform="matrix(-0.702213 0.711966 0.702213 0.711966 26.7873 51.5372)"
									fill="#00B5E9"
								/>
								<circle
									cx="3.53467"
									cy="3.53467"
									r="3.53467"
									transform="matrix(-0.702213 0.711966 0.702213 0.711966 17.8518 50.5306)"
									fill="#00B5E9"
								/>
								<circle
									cx="4.2416"
									cy="4.2416"
									r="4.2416"
									transform="matrix(-0.702213 0.711966 0.702213 0.711966 6.93097 49.524)"
									fill="#00B5E9"
								/>
								<circle
									cx="2.1208"
									cy="2.1208"
									r="2.1208"
									transform="matrix(0.702213 0.711966 0.702213 -0.711966 56.5797 77.8649)"
									fill="#00B5E9"
								/>
								<circle
									cx="2.82774"
									cy="2.82774"
									r="2.82774"
									transform="matrix(0.702213 0.711966 0.702213 -0.711966 55.5869 84.9113)"
									fill="#00B5E9"
								/>
								<circle
									cx="3.53467"
									cy="3.53467"
									r="3.53467"
									transform="matrix(0.702213 0.711966 0.702213 -0.711966 54.5941 93.9708)"
									fill="#00B5E9"
								/>
								<circle
									cx="4.2416"
									cy="4.2416"
									r="4.2416"
									transform="matrix(0.702213 0.711966 0.702213 -0.711966 53.6015 105.044)"
									fill="#00B5E9"
								/>
								<circle
									cx="2.1208"
									cy="2.1208"
									r="2.1208"
									transform="matrix(0.702213 -0.711966 0.702213 0.711966 56.5797 33.6499)"
									fill="#00B5E9"
								/>
								<circle
									cx="2.82774"
									cy="2.82774"
									r="2.82774"
									transform="matrix(0.702213 -0.711966 0.702213 0.711966 55.5869 26.6035)"
									fill="#00B5E9"
								/>
								<circle
									cx="3.53467"
									cy="3.53467"
									r="3.53467"
									transform="matrix(0.702213 -0.711966 0.702213 0.711966 54.5941 17.5438)"
									fill="#00B5E9"
								/>
								<circle
									cx="4.2416"
									cy="4.2416"
									r="4.2416"
									transform="matrix(0.702213 -0.711966 0.702213 0.711966 53.6015 6.47095)"
									fill="#00B5E9"
								/>
							</svg>
						</div>
					</div>

					<div
						class="image absolute right-0 bottom-0 z-20 mt-0 w-3/4 sm:w-2/3 lg:top-2/4 lg:right-0 lg:bottom-auto lg:mt-0 lg:w-3/5"
					>
						<img
							src="/about/02.avif"
							alt="Team "
							class="aspect-[3/4] w-full rounded-lg object-cover shadow-lg"
						/>
					</div>
				</div>
			</div>
			<div class="order-1 w-full text-center lg:order-2 lg:w-1/2 lg:text-left">
				<div class="mb-2 flex items-center justify-center lg:justify-start">
					<span class="mr-3 h-6 w-1 rounded bg-[#FF6F00]"></span>
					<p class="text-md font-semibold tracking-wider text-[#FF6F00] uppercase">ABOUT US</p>
				</div>

				<h2 class="text[#04091B] mb-4 text-3xl leading-tight font-bold sm:text-4xl lg:text-5xl">
					Who We Are And Why We Do What We Do
				</h2>

				<p class="mb-4 leading-relaxed text-[#000000D4]">
					Central Staffing is a registered company with its head office in Milton Keynes. Our main
					business focus are Medical/healthcare, Commercial and Industrial Recruitment.
				</p>
				<p class="mb-8 leading-relaxed text-[#000000D4]">
					We work with companies and candidates to provide a bespoke staffing solution. Meeting all
					their needs in a flexible and partnership approach.
				</p>

				<a
					href="/about-us"
					class="inline-block rounded-lg px-8 py-3 font-medium text-white shadow transition duration-150 ease-in-out hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
				>
					Learn More
				</a>
			</div>
		</div>
	</div>
</section>

<style>
	p,
	h2 {
		font-family: Montserrat;
	}

	a {
		border-radius: 40px;
		padding-top: 5px;
		padding-right: 20px;
		padding-bottom: 5px;
		padding-left: 20px;
		font-family: 'Mukta', sans-serif;
		background-color: #5d28b6;
	}

	.placeholder-starburst {
		display: none;

		@media screen and (min-width: 700px) {
			display: block;
		}
	}

	.image {
		height: 458.6783447265625px;
	}

	.image img {
		height: 100%;
	}

	.order-1 {
		max-width: 538px;
	}
</style>
