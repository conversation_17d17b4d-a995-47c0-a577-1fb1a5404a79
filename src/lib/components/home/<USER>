<script lang="ts">
	import { getContext } from 'svelte';
	import Logo from '../shared/Logo.svelte';
	import type { ExternalLinksConfig } from '../../../app';
	let appConfig = getContext('appConfig');

	const externalLinks = appConfig?.externalLinks as ExternalLinksConfig;
</script>

<footer class="bg-[#04091B] text-gray-300">
	<div class="central-p mx-auto px-4 py-12 md:py-16">
		<div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
			<div>
				<h2 class="mb-4 text-2xl font-bold text-white">
					<Logo />
				</h2>
				<!-- <p class="text-sm leading-relaxed">
					Lorem ipsum dolor sit amet consectetur. Tincidunt proin diam egestas nec sed sit molestie
					viverra donec. Aliquam pellentesque phasellus nisi venenatis sed cras pretium iaculis.
					Arcu malesuada tincidunt morbi diam orci senectus tincidunt fusce. Cu nec vitae vitae eget
					eu. Turpis leo.
				</p> -->
			</div>

			<div>
				<h3 class="mb-4 text-lg font-semibold text-white">Navigation</h3>
				<ul class="space-y-2 text-sm">
					<li><a href="/" class="hover:text-white hover:underline">Home</a></li>
					<li>
						<a href="/division/health" class="hover:text-white hover:underline"
							>Medical/Healthcare</a
						>
					</li>
					<li>
						<a href="/division/commercial" class="hover:text-white hover:underline">Commercial</a>
					</li>
					<li>
						<a href="/division/industrial" class="hover:text-white hover:underline">Industrial</a>
					</li>
					<li><a href="/services" class="hover:text-white hover:underline">Services</a></li>
				</ul>
			</div>

			<div>
				<h3 class="mb-4 text-lg font-semibold text-white">Candidates</h3>
				<ul class="space-y-2 text-sm">
					<li><a href="/downloads" class="hover:text-white hover:underline">Downloads</a></li>
					<li><a href="/contact#join-us" class="hover:text-white hover:underline">Join Us</a></li>
					<li><a href="/about-us" class="hover:text-white hover:underline">About Us</a></li>
					<li>
						<a href={externalLinks.privacyPolicy?.url} class="hover:text-white hover:underline"
							>Privacy Policy</a
						>
					</li>
					<li>
						<a href={externalLinks.termsOfUse?.url} class="hover:text-white hover:underline"
							>Terms of Use</a
						>
					</li>
					<li>
						<a href={externalLinks.cookiesPolicy?.url} class="hover:text-white hover:underline"
							>Cookies Policy</a
						>
					</li>
				</ul>
			</div>

			<div>
				<h3 class="mb-4 text-lg font-semibold text-white">Get In Touch</h3>
				<address class="space-y-3 text-sm not-italic">
					<p class="flex items-center space-x-2">
						<svg class="h-4 w-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"
							><path
								d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.06-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
							></path></svg
						>
						<span>01908915180</span>
					</p>
					<p class="flex items-center space-x-2">
						<svg class="h-4 w-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"
							><path
								d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.06-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
							></path></svg
						>
						<span>09108915205</span>
					</p>
					<p class="flex items-start space-x-2">
						<svg class="mt-1 h-4 w-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"
							><path
								fill-rule="evenodd"
								d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
								clip-rule="evenodd"
							></path></svg
						>
						<span>Suit 2.10 Margaret Powell House 421-447 Midsummer Boulevard Milton Keynes</span>
					</p>
					<p class="flex items-center space-x-2">
						<svg class="h-4 w-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"
							><path
								d="M2.003 5.884L10 11.884l7.997-6M2 18h16a1 1 0 001-1V3a1 1 0 00-1-1H2a1 1 0 00-1 1v14a1 1 0 001 1z"
							></path></svg
						>
						<a href="mailto:<EMAIL>" class="hover:text-white hover:underline"
							><EMAIL></a
						>
					</p>
				</address>
			</div>
		</div>

		<div
			class="mt-8 flex flex-col items-center justify-between border-t border-gray-700 pt-8 text-sm md:flex-row"
		>
			<p class="mb-4 text-gray-400 md:mb-0">&copy; 2025 Central Staffing. All Rights Reserved.</p>
			<div class="flex space-x-4">
				<a
					href="https://www.facebook.com/Centralstaffinglimited/"
					aria-label="Facebook"
					class="text-orange-500 hover:text-orange-400"
				>
					<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24"
						><path
							fill-rule="evenodd"
							d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
							clip-rule="evenodd"
						></path></svg
					>
				</a>

				<a
					href="https://twitter.com/StaffingCentral"
					aria-label="Twitter"
					class="text-orange-500 hover:text-orange-400"
				>
					<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24"
						><path
							d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
						></path></svg
					>
				</a>
			</div>
		</div>
	</div>
</footer>

<style>
	footer {
		font-family: 'Montserrat', sans-serif;
	}
</style>
