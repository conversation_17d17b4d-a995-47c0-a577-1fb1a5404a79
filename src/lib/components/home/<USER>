<script lang="ts">
	import { fly } from 'svelte/transition';
	// Make props required as the wrapper ensures they are passed
	let {
		block1 = $bindable(),
		block2 = $bindable(),
		subtitle = $bindable(),
		paragraph = $bindable(),
		cta = $bindable(),
		ctalink = $bindable(),
		img = $bindable()
	} = $props<{
		block1: string;
		block2: string;
		subtitle: string;
		paragraph: string;
		cta: string;
		ctalink: string;
		img: string;
	}>();
</script>

<div class="hero">
	<div class="bg-image">
		<img src={img} alt={cta} />
	</div>

	<div class="central-wrapper">
		<div class="central">
			<h1 in:fly={{ y: 20, duration: 400, delay: 250 }}>WELCOME TO CENTRAL STAFFING</h1>

			<div class="hero-text" in:fly={{ y: 20, duration: 400, delay: 350 }}>
				<h2>
					{@html block1}
				</h2>
				<h2>
					{block2}-<span class="subtitle">{subtitle}</span>
				</h2>
			</div>

			<!-- <p in:fly={{ y: 20, duration: 400, delay: 450 }}>
				{paragraph}
			</p> -->

			<div class="cta" in:fly={{ y: 20, duration: 400, delay: 550 }}>
				<div class="circle"></div>
				<div class="dash"></div>
				<a href={ctalink}>
					<span>{cta}</span>
					<span>
						<svg
							width="33"
							height="33"
							viewBox="0 0 33 33"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<circle cx="16.5" cy="16.5" r="16.5" fill="white" />
							<path
								d="M10 24L23 11M23 11V23.48M23 11H10.52"
								stroke="#5D28B6"
								stroke-width="1.5"
								stroke-linecap="round"
								stroke-linejoin="round"
							/>
						</svg>
					</span>
				</a>
			</div>
		</div>
	</div>
</div>

<style>
	/* REMOVED @keyframes fadeInImage */

	.hero {
		width: 100%;
		height: 100%;
		position: relative;
	}

	.bg-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		/* background-size/position not needed if using img tag */
		z-index: -20;
	}

	.bg-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		object-position: center;
		/* REMOVED animation properties */
		/* Opacity is handled by parent fade */
	}

	/* Rest of the styles remain the same */
	.central-wrapper {
		z-index: 10;
		width: 100%;
		height: 100%;
		background-color: #04091b99; /* Adjust opacity as needed */
		display: flex;
		align-items: center;
	}
	.central {
		padding-top: 5.56rem; /* Consider using consistent padding or clamp */
		padding-left: 7.5rem; /* Consider using consistent padding or clamp */
		padding: clamp(0.5rem, 0.5rem + 5vw, 2rem) clamp(1rem, -4.3rem + 7.8vw, 7.5rem);

		@media screen and (min-width: 900px) {
			padding: 2rem clamp(1rem, -4.3rem + 7.8vw, 7.5rem);
		}
	}

	h1 {
		font-family: Montserrat;
		font-weight: 700;
		font-size: 18px;
		line-height: 24px;
		letter-spacing: 0.2px;
		color: #5d28b6;
		padding: 4px 8px;
		background-color: #fff;
		border-radius: 4px;
		width: fit-content;

		@media (max-width: 1200px) {
			font-size: 16px;
			line-height: 22px;
			letter-spacing: 0.15px;
		}
		@media (max-width: 768px) {
			font-size: 14px;
			line-height: 20px;
			letter-spacing: 0.1px;
		}
		@media (max-width: 480px) {
			font-size: 12px;
			line-height: 18px;
			letter-spacing: 0.05px;
		}
	}

	.hero-text {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
		margin-top: 0.5rem;
	}

	h2 {
		font-family: Montserrat;
		font-weight: 700;
		font-size: 64px;
		line-height: 75px;
		letter-spacing: 0%;
		color: #ffffff;

		@media (max-width: 1200px) {
			font-size: 52px;
			line-height: 60px;
		}
		@media (max-width: 768px) {
			font-size: 40px;
			line-height: 50px;
		}
		@media (max-width: 480px) {
			font-size: 32px;
			line-height: 40px;
		}
	}

	/* Added necessary style for the span inside h2 block1 */
	h2 :global(span.spec) {
		color: #cdb0ff; /* Or your desired highlight color */
	}

	h2 .subtitle {
		font-family: Montserrat;
		font-weight: 700;
		font-size: 40px;
		line-height: 75px; /* Usually should match h2 line-height */
		letter-spacing: 0%;
		color: #cdb0ff; /* Your highlight color */

		@media (max-width: 1200px) {
			font-size: 32px;
			line-height: 60px;
		}
		@media (max-width: 768px) {
			font-size: 28px;
			line-height: 50px;
		}
		@media (max-width: 480px) {
			font-size: 20px;
			line-height: 40px;
		}
	}

	p {
		margin-top: 30px;
		font-family: Montserrat;
		font-weight: 400;
		font-size: 18px;
		line-height: 26px;
		letter-spacing: 0.2px;
		color: #f1f3f9;
		max-width: 50ch; /* Good for readability */

		@media (max-width: 1200px) {
			font-size: 16px;
			line-height: 24px;
			letter-spacing: 0.18px;
		}
		@media (max-width: 768px) {
			font-size: 14px;
			line-height: 22px;
			letter-spacing: 0.16px;
		}
		@media (max-width: 480px) {
			font-size: 12px;
			line-height: 20px;
			letter-spacing: 0.14px;
		}
	}

	.cta {
		margin-top: 1rem;
		display: flex;
		align-items: center;
	}

	.cta .circle {
		position: relative;
		display: inline-block;
		height: 20px;
		width: 20px;
		background-color: #fff;
		border-radius: 50%;
		@media (max-width: 768px) {
			display: none;
		}
	}

	.cta .dash {
		width: 282px;
		height: 2px;
		background-color: #fff;
		@media (max-width: 768px) {
			display: none;
		}
	}

	.cta a {
		background-color: #5d28b6;
		border-radius: 34px;
		color: #fff;
		padding: 1rem 23px;
		display: flex;
		align-items: center; /* Vertically align text and icon */
		gap: 15px;
		font-family: Montserrat;
		font-weight: 600;
		font-size: 24px;
		line-height: 26px; /* Adjust if needed */
		letter-spacing: 0.2px;
		text-decoration: none; /* Remove underline */
		transition: background-color 0.2s ease; /* Add hover effect */

		@media (max-width: 1200px) {
			font-size: 22px;
			line-height: 24px;
			letter-spacing: 0.18px;
		}
		@media (max-width: 768px) {
			font-size: 20px;
			line-height: 22px;
			letter-spacing: 0.16px;
			padding: 0.8rem 18px; /* Adjust padding for smaller screens */
			gap: 10px;
		}
		@media (max-width: 480px) {
			font-size: 18px;
			line-height: 20px;
			letter-spacing: 0.14px;
			padding: 0.7rem 15px;
			gap: 8px;
		}
	}
	.cta a:hover {
		background-color: #4b2092; /* Darker shade for hover */
	}

	/* Ensure SVG scales correctly */
	.cta a svg {
		flex-shrink: 0; /* Prevent SVG from shrinking */
	}
</style>
