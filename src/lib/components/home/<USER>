{#snippet CheckMark()}
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		stroke="#FF6F00"
		stroke-width="2"
		stroke-linecap="round"
		stroke-linejoin="round"
		class="lucide lucide-badge-check-icon lucide-badge-check"
		><path
			d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z"
		/><path d="m9 12 2 2 4-4" /></svg
	>
{/snippet}

<section class="bg-[#FEFAF7] py-16 md:py-36">
	<div class="central-p mx-auto px-4">
		<div class="mb-8 text-center">
			<p class="mb-2 text-sm font-semibold tracking-wider text-[#FF6F00] uppercase">
				Job Categories
			</p>
			<h2 class="mb-4 text-3xl font-bold text-[#0586AB] md:text-5xl">
				Explore Career Opportunities Across<br class="hidden md:block" /> Various Fields
			</h2>
			<p class="mx-auto max-w-2xl text-gray-600">
				Lorem ipsum dolor sit amet consectetur. Adipiscing nec ipsum mauris porttitor adipiscing
				lorem praesent massa. Purus velit nunc dolor in posuere netus.
			</p>
		</div>

		<div class="my-8 flex justify-center md:my-4">
			<svg
				width="84"
				height="89"
				viewBox="0 0 84 89"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path
					d="M6.18838 5.97942C24.6998 13.3189 44.1573 18.5704 64.0397 21.7171C66.6305 22.0898 69.2213 22.4625 71.7079 22.8318C73.2613 23.0747 75.1428 23.0392 76.353 23.8487C76.5513 24.0477 76.7598 24.0545 76.6402 24.3395C76.5258 24.5284 75.7706 24.9854 75.4426 25.2637C73.5101 26.2605 71.1861 26.7631 69.0604 27.4647C59.1822 30.6119 49.2098 33.5634 39.2375 36.5149C29.2703 39.3703 19.2878 42.5141 10.9296 48.5031C10.1693 49.0563 9.90984 50.0108 10.1818 50.79C11.1042 55.0569 16.0671 55.9877 19.9927 56.7888C25.2582 57.922 30.5288 58.9592 35.7943 60.0924C40.236 61.0065 45.0006 61.7383 48.954 63.9847C51.8905 65.7167 53.9419 68.3831 54.9306 71.4002C56.0031 74.8053 55.7992 78.6505 53.9579 81.8651C53.2713 82.9984 53.5075 84.4505 54.7228 85.1639C55.8389 85.7777 57.6263 85.5467 58.313 84.4133C62.3286 77.6099 61.328 68.9108 55.8902 63.1498C52.8269 59.8729 48.5353 58.0971 44.1089 56.8947C38.9578 55.5725 33.5779 54.6282 28.4167 53.4983C25.4242 52.8237 22.4266 52.2453 19.4341 51.5707C18.506 51.3481 17.4738 51.1221 16.6551 50.8068C15.8365 50.4914 15.4348 50.1895 15.3611 49.6094C15.1119 50.3717 14.8626 51.134 14.6133 51.8963C22.1019 46.5533 31.3343 43.7704 40.2438 41.1697C50.7424 38.1389 61.1419 35.0086 71.5516 31.686C75.48 30.4649 81.9256 29.0364 82.0762 24.2264C82.2218 19.5126 76.5291 18.5581 72.6925 18.0489C62.2199 16.6509 51.8821 14.6795 41.7834 12.1381C30.2405 9.26103 19.0512 5.62507 8.10611 1.32293C6.77642 0.798444 5.26601 1.71256 4.78785 2.85265C3.98167 4.27102 4.8587 5.45493 6.18838 5.97942Z"
					fill="#161A1E"
				/>
				<path
					d="M37.2097 72.4678C40.3179 73.1488 43.2857 74.5022 45.5899 76.5113C46.7943 77.5175 47.7844 78.6134 48.6596 79.8991C49.5347 81.1848 50.0909 82.5569 50.8614 83.8393C52.9001 86.9036 56.7101 88.1876 60.3749 86.2751C63.9351 84.3593 66.4314 80.7647 68.9913 77.946C72.2251 74.2786 75.5584 70.7111 78.7921 67.0437C79.6854 66.0087 79.1446 64.347 78.0244 63.7304C76.7051 62.9139 75.3142 63.4492 74.4209 64.4842C70.9676 68.3379 67.5195 72.0951 64.1709 75.9522C62.7186 77.6462 61.2612 79.4367 59.6099 80.9307C58.732 81.6761 57.295 83.0804 56.0855 82.1707C54.876 81.2611 54.3556 79.2131 53.58 78.0273C52.5109 76.4452 51.3371 74.8597 49.8342 73.5536C46.8334 70.845 43.0644 68.7886 38.9201 67.8806C37.5752 67.5469 36.0746 68.1754 35.5896 69.417C35.1046 70.6587 35.8649 72.1341 37.2097 72.4678Z"
					fill="#161A1E"
				/>
			</svg>
		</div>

		<div class="grid grid-cols-1 items-center gap-12 md:grid-cols-2">
			<div class="order-2 hidden md:order-1 md:block">
				<div class="relative mx-auto w-full max-w-lg">
					<img
						src="/categories/02.avif"
						alt="Office colleagues shaking hands"
						class="relative z-10 h-auto w-full rounded-lg object-cover shadow-lg"
					/>

					<img
						src="/categories/01.avif"
						alt="Team meeting or presentation"
						class="absolute
                           right-0 bottom-0 z-20 h-auto
                           w-[80%] translate-y-1/4 transform rounded-lg object-cover shadow-md
                           md:translate-y-2/3"
					/>
				</div>
			</div>

			<div class="relative order-1 md:order-2">
				<button
					class="absolute top-0 right-0 -mt-4 rounded-[70px] bg-[#FFD3B2] px-3 py-1 text-xs font-bold text-[#FF6F00] hover:bg-orange-200 md:top-[-50px] md:mt-0 lg:top-[-20px] lg:right-[-30px]"
				>
					See More
				</button>

				<h3 class="mt-8 mb-4 text-2xl font-semibold text-gray-900 md:mt-0">
					Lorem Ipsum Dolor Sit Amet Consectetur. Nunc Risus Blandit Gravida Tellus Consequat Tellus
					Sit.
				</h3>
				<p class="mb-6 text-gray-600">
					Lorem ipsum dolor sit amet consectetur. Adipiscing nec ipsum mauris porttitor adipiscing
					lorem praesent massa. Purus velit nunc dolor in posuere netus.
				</p>

				<ul class="mb-8 space-y-4 text-gray-700">
					<li class="flex items-start">
						<span class="mt-1 mr-2 text-orange-500">{@render CheckMark()}</span>
						<div>
							<span class="font-semibold">Medical & Health Care:</span> General Nurse, Mental Health
							Nurses, Midwives, Doctors/GPs/Consultants, Allied Health Care Professionals - Dentist.
						</div>
					</li>
					<li class="flex items-start">
						<span class="mt-1 mr-2 text-orange-500">{@render CheckMark()}</span>
						<div>
							<span class="font-semibold">Commercial & Office:</span> Human Resource, Recruitment Consultant,
							Finance/Account, Sales & Rental, Call Centres, Marketing, Medical Administrator/Front Desk
							Personnel, Junior/Senior Management Roles, Banking, IT/Project Managers.
						</div>
					</li>
					<li class="flex items-start">
						<span class="mt-1 mr-2 text-orange-500">{@render CheckMark()}</span>
						<div>
							<span class="font-semibold">Industrial:</span> Warehousing, Cleaners/House Keepers, Kitchen
							Assistants, Potters/Laundry Assistant.
						</div>
					</li>
				</ul>

				<button
					class="rounded-[40px] bg-[#5D28B6] px-6 py-2 font-semibold text-white transition duration-200 hover:bg-purple-700"
				>
					View Vacancies
				</button>
			</div>
		</div>
	</div>
</section>

<style>
	/* Apply Montserrat font if not globally applied via Tailwind config */
	/* You might already have this font configured */
	/* @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap'); */
	section {
		font-family: 'Montserrat', sans-serif; /* Example */
	}
</style>
