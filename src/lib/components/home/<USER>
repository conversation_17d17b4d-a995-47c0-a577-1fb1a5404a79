<script lang="ts" module>
	// Same as before
	export type CarouselItem = {
		block1: string;
		block2: string;
		subtitle: string;
		paragraph: string;
		cta: string;
		ctalink: string;
		img: string;
	};
</script>

<script lang="ts">
	import { env } from '$env/dynamic/public';
	import { fade } from 'svelte/transition';
	import { browser } from '$app/environment'; // Import browser check
	import { FiniteStateMachine } from 'runed';

	import Carousel from './Carousel.svelte';
	// import type { CarouselItem } from './CarouselWrapper.svelte'; // Import the type

	type Events = 'supported' | 'care' | 'staff';
	// | 'solution' | 'recruitment';
	type Actions = 'next';

	const carouselData: Record<Events, CarouselItem> = {
		supported: {
			block1: '<span class="spec">Caring</span> For You, Just In <br /> The Comfort of Your',
			block2: 'Home',
			subtitle: 'Supported Living',
			paragraph:
				'Lorem ipsum dolor sit amet consectetur. Leo sed non sagittis at enim laoreet id elit eu. Morbi ut nisl scelerisque lorem quam sollicitudin.',
			cta: 'View Supported Living',
			ctalink: `http://supported.${env.PUBLIC_URL}/`,
			img: '/carousel/01.webp'
		},
		care: {
			block1: 'Bringing You <span class="spec">Comfort</span>, <br />Care To Your Door',
			block2: 'Step',
			subtitle: 'Domiciliary Care',
			paragraph:
				'Lorem ipsum dolor sit amet consectetur. Leo sed non sagittis at enim laoreet id elit eu. Morbi ut nisl scelerisque lorem quam sollicitudin.',
			cta: 'Explore Domiciliary Care',
			ctalink: `http://domiciliary.${env.PUBLIC_URL}/`,
			img: '/carousel/02.webp'
		},
		staff: {
			block1: 'Connecting <span class="spec">HealthCare</span><br />Professionals For Right',
			block2: 'Opportunities',
			// NOTE: Subtitle was 'Supported Living', maybe intended 'Healthcare Staffing'?
			subtitle: 'Social Care Services', // Corrected?
			paragraph:
				'Lorem ipsum dolor sit amet consectetur. Leo sed non sagittis at enim laoreet id elit eu. Morbi ut nisl scelerisque lorem quam sollicitudin.',
			cta: 'Social Care Services',
			ctalink: '/services#staffing',
			img: '/carousel/04.webp'
		}
	};

	let activeCarousel: CarouselItem | undefined = $state(undefined);
	const transitionDuration = 5000; // ms

	// --- Image Preloading ---
	if (browser) {
		// Only run preloading in the browser
		Object.values(carouselData).forEach((item) => {
			if (item.img) {
				const image = new Image();
				image.src = item.img;
			}
		});
	}
	// --- End Image Preloading ---

	const fsm = new FiniteStateMachine<Events, Actions>('supported', {
		supported: {
			_enter: () => {
				activeCarousel = carouselData.supported;
			},
			next: 'care'
		},
		care: {
			_enter: () => {
				activeCarousel = carouselData.care;
				fsm.debounce(transitionDuration, 'next');
			},
			next: 'staff'
		},
		staff: {
			_enter: () => {
				activeCarousel = carouselData.staff;
				fsm.debounce(transitionDuration, 'next');
			},
			next: 'supported'
		}
		// solution: {
		// 	_enter: () => {
		// 		activeCarousel = carouselData.solution;
		// 		fsm.debounce(transitionDuration, 'next');
		// 	},
		// 	next: 'recruitment'
		// },
		// recruitment: {
		// 	_enter: () => {
		// 		activeCarousel = carouselData.recruitment;
		// 		fsm.debounce(transitionDuration, 'next');
		// 	},
		// 	next: 'supported'
		// }
	});

	// Effect to handle the initial transition trigger
	$effect(() => {
		if (fsm.current === 'supported' && activeCarousel) {
			// Ensure activeCarousel is set before starting the first timer
			const timerId = setTimeout(() => {
				fsm.send('next');
			}, transitionDuration);

			// Cleanup function for the effect
			return () => {
				clearTimeout(timerId);
			};
		}
	});

	// Effect to clear debounce timer on component destroy (good practice)
	// $effect(() => {
	// 	return () => {
	// 		fsm.clearDebounce();
	// 	};
	// });
</script>

<div class="hero-wrapper">
	{#if activeCarousel}
		{#key fsm.current}
			<div
				class="carousel-container"
				in:fade={{ duration: 600, delay: 50 }}
				out:fade={{ duration: 400 }}
			>
				<a class="call" href="tel:01908915180">
					<span> Call Us Now </span>
					<span
						><svg
							width="18"
							height="18"
							viewBox="0 0 18 18"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
							><path
								d="M14.9625 15.75C13.4 15.75 11.8562 15.4095 10.3312 14.7285C8.80625 14.0475 7.41875 13.0817 6.16875 11.8312C4.91875 10.5807 3.95325 9.19325 3.27225 7.66875C2.59125 6.14425 2.2505 4.6005 2.25 3.0375C2.25 2.8125 2.325 2.625 2.475 2.475C2.625 2.325 2.8125 2.25 3.0375 2.25H6.075C6.25 2.25 6.40625 2.3095 6.54375 2.4285C6.68125 2.5475 6.7625 2.688 6.7875 2.85L7.275 5.475C7.3 5.675 7.29375 5.84375 7.25625 5.98125C7.21875 6.11875 7.15 6.2375 7.05 6.3375L5.23125 8.175C5.48125 8.6375 5.778 9.08425 6.1215 9.51525C6.465 9.94625 6.84325 10.362 7.25625 10.7625C7.64375 11.15 8.05 11.5095 8.475 11.841C8.9 12.1725 9.35 12.4755 9.825 12.75L11.5875 10.9875C11.7 10.875 11.847 10.7907 12.0285 10.7347C12.21 10.6788 12.388 10.663 12.5625 10.6875L15.15 11.2125C15.325 11.2625 15.4688 11.3532 15.5812 11.4847C15.6937 11.6163 15.75 11.763 15.75 11.925V14.9625C15.75 15.1875 15.675 15.375 15.525 15.525C15.375 15.675 15.1875 15.75 14.9625 15.75ZM4.51875 6.75L5.75625 5.5125L5.4375 3.75H3.76875C3.83125 4.2625 3.91875 4.76875 4.03125 5.26875C4.14375 5.76875 4.30625 6.2625 4.51875 6.75ZM11.2313 13.4625C11.7188 13.675 12.2157 13.8438 12.7222 13.9688C13.2287 14.0938 13.738 14.175 14.25 14.2125V12.5625L12.4875 12.2062L11.2313 13.4625Z"
								fill="#5D28B6"
							/></svg
						></span
					>
					<span>01908 915 180</span>
				</a>

				<Carousel
					block1={activeCarousel.block1}
					block2={activeCarousel.block2}
					subtitle={activeCarousel.subtitle}
					paragraph={activeCarousel.paragraph}
					cta={activeCarousel.cta}
					ctalink={activeCarousel.ctalink}
					img={activeCarousel.img}
				/>
			</div>
		{/key}
	{/if}
</div>

<style>
	.hero-wrapper {
		height: 100vh;
		position: relative;
		/* Important: Keep overflow hidden to contain absolutely positioned children */
		overflow: hidden;
	}
	.carousel-container {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
	}

	.call {
		display: none;
		position: absolute;
		top: 0;
		right: 0;
		padding: 2px 11px;
		margin-top: 2px; /* Adjust positioning as needed */
		margin-right: 2px; /* Adjust positioning as needed */
		background-color: #fff;
		border-radius: 4px;
		z-index: 30; /* Ensure it's above carousel content */
		align-items: center;
		gap: 4px;
		color: #5d28b6;
		@media screen and (min-width: 900px) {
			display: flex;
		}
	}
</style>
