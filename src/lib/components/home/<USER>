<script lang="ts">
	import type { ListItem } from '../shared/SplitSection.svelte';

	const listItems: ListItem[] = [
		{ text: 'Human Resources, Recruitment Consultant, Fiance/Account' },
		{
			text: 'Sales & Retail, Call Centres, Marketing, Medical Administrators/Front Desk Personnel, Banking'
		},
		{
			text: 'Office Administrators/Front Desk Personnel/PA’s, Junior/Senior Management Roles, IT/Project Managers '
		}
	];

	let listItemsExplainer = 'Check Out The Job Categories';

	const listItems2: ListItem[] = [
		{ text: 'Warehousing' },
		{
			text: 'Cleaners/House Keepers'
		},
		{
			text: 'Kitchen Assistant'
		},
		{
			text: '<PERSON><PERSON>/Laundry Assistants'
		}
	];
</script>

{#snippet Icon()}
	<span
		class="ml-2 inline-flex items-center justify-center rounded-full text-white transition-transform duration-200 group-hover:translate-x-1"
	>
		<svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<circle cx="16.5" cy="16.5" r="16.5" fill="#5D28B6" />
			<path
				d="M10 24L23 11M23 11V23.48M23 11H10.52"
				stroke="white"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
		</svg>
	</span>
{/snippet}

{#snippet SubTagLine(text: string)}
	<p class="mt-6 text-justify text-sm text-[#737373]">
		{text}
	</p>
{/snippet}

<section class="bg-white py-16 sm:py-24">
	<div class="container mx-auto px-6 lg:px-8">
		<div class="mx-auto mb-12 text-center lg:mb-16 lg:max-w-3xl">
			<p class="text-base leading-7 font-bold tracking-wide text-[#FF6F00] uppercase">MEDICAL</p>
			<h2 class="mt-2 text-3xl font-bold tracking-tight text-[#0586AB] sm:text-4xl">
				Join The Future Of HealthCare
			</h2>
			{@render SubTagLine(`Discover premier opportunities within the healthcare sector. We specialize in connecting
				skilled medical professionals with rewarding roles in innovative and supportive
				environments.`)}
		</div>

		<div class=" gap-y-16 lg:gap-x-16 lg:gap-y-0">
			<div class="flex flex-1 flex-col gap-y-6 sm:gap-x-6 lg:min-h-[488px] lg:flex-row lg:gap-x-12">
				<div class="flex-1">
					<img
						class="h-full w-full rounded-xl object-cover object-left shadow-lg ring-1 ring-gray-400/10"
						src="/medical/10.png"
						alt=""
					/>
				</div>
				<div class="mt-8 flex-1">
					<div class="flex flex-col gap-y-6 text-center sm:min-w-0 sm:flex-1 lg:text-left">
						<h3 class="text-lg leading-6 font-bold text-[#04091B]">
							Specialized Recruitment for a Thriving Healthcare Sector.
						</h3>
						<p class="w-full text-base font-normal text-[#737373]">
							We work with our clients to understand their day to day staffing challenges - our
							experienced consultants understand the increasing demand for medical and healthcare
							professionals and the stress understaffed ward and units could impose to managers and
							front line professionals. With ad-hoc staffing support services we are committed to
							efficiency and consistency in our staff placement. We recruit, vet and manage a pool
							of medical and healthcare professionals for roles with private hospitals, NHS
							hospitals, local authorities, learning disability units, nursing and residential
							settings.
						</p>
						<div class="mt-auto">
							<a
								href="/division/health"
								class="group inline-flex items-center justify-center rounded-full border border-transparent bg-purple-700 px-7 py-3 text-base font-medium text-white transition duration-200 hover:bg-purple-800"
							>
								Learn More
								<span
									class="ml-2 inline-flex rotate-[135deg] items-center justify-center rounded-full bg-white p-1 text-purple-700 transition-transform duration-200 group-hover:translate-x-1"
								>
									<svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20"
										><path
											fill-rule="evenodd"
											d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
											clip-rule="evenodd"
										></path></svg
									>
								</span>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
<!-- <section class="relative bg-[#FEFAF7] py-16 sm:py-24">
	<div class="container mx-auto px-6 lg:px-8">
		<div class="mx-auto mb-12 max-w-3xl text-center lg:mb-16">
			<p class="text-base leading-7 font-bold tracking-wide text-[#FF6F00] uppercase">COMMERCIAL</p>
			<h2 class="mt-2 text-3xl font-bold tracking-tight text-[#0586AB] sm:text-4xl">
				Explore Exciting Commercial Positions
			</h2>
			{@render SubTagLine(`Unlock your potential in the dynamic world of commerce. We connect ambitious professionals
				with rewarding opportunities across a spectrum of industries, helping you build a thriving
				career and businesses achieve their growth objectives.`)}
		</div>

		<div class="gap-y-16 lg:flex-row lg:gap-x-16 lg:gap-y-0">
			<div class="flex h-full flex-col gap-y-6 sm:gap-x-6 lg:flex-row-reverse lg:gap-x-12">
				<div class="flex flex-1 sm:min-w-0 lg:max-w-[617px]">
					<img
						class="h-full w-full rounded-xl object-cover object-right shadow-lg ring-1 ring-gray-400/10"
						src="/commercial/02.avif"
						alt=""
					/>
				</div>
				<div class=" flex h-full flex-1 flex-col gap-y-6 sm:min-w-0 sm:flex-1">
					<p class="w-full text-center text-base font-normal text-black lg:text-left">
						Central Staffing specializes in comprehensive commercial recruitment. Our team of
						seasoned consultants delivers professional, personalized service by partnering closely
						with businesses. We focus on understanding your specific needs to tailor effective
						staffing solutions and match you with ideal candidates who will contribute to your
						success. From temporary and permanent placements to contract roles, full recruitment
						process outsourcing, or expert hiring advice, our specialist consultants are ready to
						help you reach your goals.
					</p>
					{#if listItemsExplainer}
						<p class="text-md leading-relaxed font-bold text-[#04091B]">
							{listItemsExplainer}
						</p>
					{/if}
					<ul class="staffing-ul mb-6 flex flex-col gap-[23px] space-y-3">
						{#each listItems as item (item)}
							<li class="flex items-start space-x-3">
								<span class="flex h-5 w-5 flex-shrink-0 items-center justify-center bg-[#240951]">
									<svg class="h-[21px] w-[21px] text-white" fill="currentColor" viewBox="0 0 20 20"
										><path
											fill-rule="evenodd"
											d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
											clip-rule="evenodd"
										></path></svg
									>
								</span>
								<span class="font-xs font-medium text-[#151414]">{item.text}</span>
							</li>
						{/each}
					</ul>
					<div class="mt-auto text-center lg:text-left">
						<a
							href="/division/commercial"
							class="group inline-flex items-center justify-center rounded-full border border-transparent bg-purple-700 px-7 py-3 text-base font-medium text-white transition duration-200 hover:bg-purple-800"
						>
							Learn More
							<span
								class="ml-2 inline-flex rotate-[135deg] items-center justify-center rounded-full bg-white p-1 text-purple-700 transition-transform duration-200 group-hover:translate-x-1"
							>
								<svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20"
									><path
										fill-rule="evenodd"
										d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
										clip-rule="evenodd"
									></path></svg
								>
							</span>
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</section> -->
{#snippet InnerIcon()}
	<span
		class="absolute -top-20 right-0 z-10 h-[201px] w-[58px] md:-top-40 md:h-[403px] md:w-[116px]"
	>
		<svg
			width="100%"
			height="100%"
			viewBox="0 0 116 403"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<g filter="url(#filter0_f_492_2125)">
				<path
					d="M372.605 237.292L218.926 379.006L71.3336 218.952C32.2005 176.515 34.8789 110.389 77.3162 71.2558C119.753 32.1226 185.879 34.8011 225.012 77.2383L372.605 237.292Z"
					fill="#94B4B8"
				/>
				<path
					d="M218.935 378.791L71.4451 218.849C32.3691 176.474 35.0442 110.444 77.4196 71.3677C119.795 32.2917 185.824 34.9662 224.9 77.3415L372.39 237.284L218.935 378.791Z"
					stroke="#94B4B9"
					stroke-width="0.304287"
				/>
			</g>
			<defs>
				<filter
					id="filter0_f_492_2125"
					x="-23.5233"
					y="-23.6011"
					width="420.128"
					height="426.607"
					filterUnits="userSpaceOnUse"
					color-interpolation-filters="sRGB"
				>
					<feFlood flood-opacity="0" result="BackgroundImageFix" />
					<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
					<feGaussianBlur stdDeviation="12" result="effect1_foregroundBlur_492_2125" />
				</filter>
			</defs>
		</svg>
	</span>
{/snippet}

<section class="relative bg-white py-16 sm:py-4">
	{@render InnerIcon()}
	<div class="mx-auto max-w-7xl px-6 lg:px-8">
		<div class="mx-auto mb-12 max-w-4xl text-center lg:mb-16">
			<p class="text-base leading-7 font-bold tracking-wide text-[#FF6F00] uppercase">INDUSTRIAL</p>
			<h2 class="mt-2 text-3xl font-bold tracking-tight text-[#0586AB] sm:text-4xl">
				Forge Your Path In The Industrial WorkForce
			</h2>
			{@render SubTagLine(`Ready to power the industrial sector? We offer access to a wide range of roles for skilled
				workers in manufacturing, production, logistics, and more. Find your next challenge and
				build a solid future with our dedicated industrial recruitment team.`)}
		</div>
	</div>
</section>
<section class="h-fit bg-white text-white">
	<div class="central-p relative mx-auto bg-[#2F2246]">
		<!-- <span
			class="before:absolute before:-bottom-40 before:-left-[320px] before:z-0 before:h-[400px] before:w-[400px] before:rounded-full before:bg-[#1705362B] before:lg:min-h-[522px]"
		></span> -->
		<!-- <span
			class="after:absolute after:-right-[320px] after:z-0 after:h-[400px] after:w-[400px] after:rounded-full after:bg-[#1705362B] lg:min-h-[522px]"
		></span> -->
		<div
			class="container mt-8 grid grid-cols-1 items-start gap-10 lg:grid-cols-2 lg:gap-12 xl:gap-16"
		>
			<div class="z-20 flex flex-col gap-4 lg:max-w-[436px] lg:gap-8">
				<p class="mb-2 text-sm font-bold tracking-wider uppercase">INDUSTRIAL</p>
				<p class="mb-6 text-sm leading-relaxed">
					We offer honest and transparent services and we are committed to deliver what we promise
				</p>

				<ul class="staffing-ul mb-6 flex flex-col space-y-3 lg:gap-[16px]">
					{#each listItems2 as item (item)}
						<li class="flex items-start space-x-3">
							<span class="flex h-5 w-5 flex-shrink-0 items-center justify-center">
								<svg
									width="22"
									height="22"
									viewBox="0 0 22 22"
									fill="none"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path
										d="M1.66667 0.5H20.3333C20.6428 0.5 20.9395 0.622916 21.1583 0.841709C21.3771 1.0605 21.5 1.35725 21.5 1.66667V20.3333C21.5 20.6428 21.3771 20.9395 21.1583 21.1583C20.9395 21.3771 20.6428 21.5 20.3333 21.5H1.66667C1.35725 21.5 1.0605 21.3771 0.841709 21.1583C0.622916 20.9395 0.5 20.6428 0.5 20.3333V1.66667C0.5 1.35725 0.622916 1.0605 0.841709 0.841709C1.0605 0.622916 1.35725 0.5 1.66667 0.5ZM9.83683 15.6667L18.0852 7.41717L16.4367 5.7675L9.83683 12.3673L6.53633 9.06683L4.88667 10.7165L9.83683 15.6667Z"
										fill="white"
									/>
								</svg>
							</span>
							<span class="font-xs font-medium text-white">{item.text}</span>
						</li>
					{/each}
				</ul>

				<div>
					<a
						href="/division/industrial"
						class="group inline-flex items-center justify-center rounded-full border border-white bg-white px-7 py-3 text-base font-bold text-[#5D28B6] transition duration-200"
					>
						Learn More
						{@render Icon()}
					</a>
				</div>
			</div>
			<div class=" flex flex-col">
				<!-- <h3 class="mb-8 text-2xl leading-snug font-semibold text-white lg:mb-16">
					To Be The First To Know About New Job Offers, Please Click Below To Register With Us Today
				</h3> -->

				<div class="relative z-10">
					<img
						src="/industrial/05.avif"
						alt="Industrial team standing in warehouse"
						class="absolute h-auto w-full rounded-lg object-cover shadow-md lg:top-24"
					/>
				</div>
			</div>
		</div>
	</div>
</section>
