<script lang="ts">
	import * as Card from '$lib/components/ui/card/index.js';
	import * as Carousel from '$lib/components/ui/carousel/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import type { CarouselAPI } from '$lib/components/ui/carousel/context.js';
	import SuccessModal from '../../../routes/ongoing-jobs/SuccessModal.svelte';
	import JobsModal from '../shared/JobsModal.svelte';
	import GenericContact from '../../../routes/contact/GenericContact.svelte';
	type Data = {
		items: any[];
		count: number;
		pages: number;
		page: number;
		limit: number;
		message: string;
	};

	let { data }: { data: Data } = $props();

	let jobs = $derived.by(() => {
		return data.items.map((item) => {
			return {
				...item,
				id: item._id,
				title: item?.title,
				location: item?.jobLocation,
				description: item.jobDesc,
				salary: item.salary
			};
		});
	});

	let currentSelectedJob = $state<any>(null);
	let activeModal = $state<'none' | 'jobDetail' | 'application' | 'success'>('none');
	function handleShowJobDetail(job: any) {
		currentSelectedJob = job;
		activeModal = 'jobDetail';
	}

	function handleApplyNowClicked() {
		// Called from JobDetailModal's "Apply Now" button click
		activeModal = 'application';
	}

	function handleApplicationSubmitted() {
		// Called from ApplicationModal on successful "submission"
		activeModal = 'success';
	}

	function handleCloseAllModals() {
		activeModal = 'none';
	}

	let api = $state<CarouselAPI>();
	let activeIndex = $state(0); // 0-based index for internal logic
	// slideCount is now primarily for the dot indicators
	const slideCount = $derived(api ? api.scrollSnapList().length : 0);

	$effect(() => {
		const currentApi = api;
		if (currentApi) {
			const updateState = () => {
				if (currentApi) {
					// Check api still valid inside listener
					activeIndex = currentApi.selectedScrollSnap();
				}
			};

			updateState(); // Set initial index correctly
			currentApi.on('select', updateState);
			currentApi.on('reInit', updateState);

			// Cleanup
			return () => {
				currentApi?.off('select', updateState);
				currentApi?.off('reInit', updateState);
			};
		}
	});

	function scrollTo(index: number) {
		api?.scrollTo(index);
	}

	let roleApplyingFor = $derived(
		`${currentSelectedJob?.title?.toUpperCase()}, at ${currentSelectedJob?.companyName}`
	);

	let successModalOpen = $state(false);
	let successMessage = $state('');
	let successTitle = $state('');

	async function handleApiFormResponse(response: {
		success: boolean;
		message?: string;
		messageId?: string;
		error?: any;
	}) {
		if (response.success) {
			activeModal = 'none'; // GenericContact will show its own success and can be closed by user.
			// Or, if you want parent to control:
			successTitle = 'Submission Successful!';
			successMessage = response.message || `Your Job Application has been sent.`;
			successModalOpen = true; // If you still want a separate success modal

			// The GenericContact component will display its own success message.
			// You might want to close the modal after a delay or let the user close it.
			// setTimeout(() => {
			// if (currentModal !== 'none' && !response.error) closeModal(); // closeModal sets currentModal to 'none'
			// }, 3000); // Example: close after 3 seconds
		} else {
			console.error(
				`Form type Job Application submission failed:`,
				response.error || response.message
			);
			// Error is displayed within GenericContact.svelte
			// Optionally, show a generic error in a parent modal/toast if desired.
		}
	}
</script>

<section class="relative mt-30 bg-white py-8 md:mt-4 md:py-4 lg:mt-4">
	<div class="central-p mx-auto px-4">
		<div class="mb-12 text-center">
			<p class="mb-2 text-sm font-semibold tracking-wider text-[#FF6F00] uppercase">Vacancies</p>
			<h2 class="mb-4 text-xl font-bold text-[#0586AB] md:text-4xl">
				Exciting Career Opportunities Await - Apply Today!
			</h2>
			<p class="mx-auto max-w-2xl text-xs text-gray-600 md:text-base">
				Your next great career move starts here. We are constantly updating our listings with new
				and exciting roles across various industries, dedicated to matching your expertise with the
				perfect opportunity.
			</p>
			<a href="/ongoing-jobs" class="float-end">see more</a>
		</div>
		<div class="relative px-10 md:px-12">
			<Carousel.Root
				opts={{
					align: 'start',
					loop: true
				}}
				setApi={(emblaApi) => (api = emblaApi)}
				class="mx-auto w-full max-w-xs sm:max-w-xl md:max-w-3xl lg:max-w-5xl"
			>
				<Carousel.Content class="-ml-4">
					{#each jobs as job, index (job.id)}
						<Carousel.Item class="basis-full pl-4 sm:basis-1/2 lg:basis-1/3">
							<div class="h-full p-1">
								<Card.Root
									class="flex h-full flex-col rounded-lg transition-colors duration-300 {index ===
									activeIndex
										? 'border-purple-700 bg-[#5D28B6] text-white shadow-lg'
										: 'border-[2.5px] border-dashed border-[#B4B4B4] bg-white text-[#062126]'}"
								>
									<Card.Header class="p-4">
										<Card.Title
											class="text-xs font-medium capitalize {index === activeIndex
												? 'text-white'
												: 'text-[#062126B2]'}"
										>
											Job:
										</Card.Title>
									</Card.Header>
									<Card.Content class="flex-grow p-4 pt-0">
										<h3
											class="mb-2 text-lg font-semibold capitalize {index === activeIndex
												? 'text-white'
												: 'text-gray-900'}"
										>
											{job.title}
										</h3>
										<p
											class="mb-4 overflow-hidden text-sm text-ellipsis whitespace-nowrap text-[#737373] capitalize {index ===
											activeIndex
												? 'text-purple-100'
												: 'text-[#062126]'}"
										>
											{job.description}
										</p>
										<div class="font-medium">
											<p
												class="mb-2 pr-4 text-sm {index === activeIndex
													? 'text-purple-100'
													: 'text-[#062126]'}"
											>
												{job.location}

												<span
													class=" inline-block rounded-4xl px-2 py-1 text-sm font-semibold {index ===
													activeIndex
														? 'bg-[#E6E5E8] text-[#062126]'
														: 'bg-[#E6E5E8] text-gray-600'}"
												>
													{job.salary}
												</span>
											</p>
										</div>
									</Card.Content>
									<Card.Footer class="p-4 pt-2">
										<Button
											onclick={() => handleShowJobDetail(job)}
											variant={index === activeIndex ? 'secondary' : 'outline'}
											size="sm"
											class="{index === activeIndex
												? 'bg-white text-purple-700 hover:bg-gray-100'
												: 'rounded-[40px] border-purple-700 bg-[#5D28B6] text-white hover:bg-purple-50 hover:text-purple-800'} w-full md:w-auto"
										>
											View Job Details
										</Button>
									</Card.Footer>
								</Card.Root>
							</div>
						</Carousel.Item>
					{/each}
				</Carousel.Content>

				<div class="absolute flex w-full justify-between">
					<Carousel.Previous
						class=" relative -bottom-10 left-0 bg-[#3D3C7F] text-white hover:bg-purple-700 md:h-10 md:w-10"
					/>
					<Carousel.Next
						class=" relative right-0 -bottom-10 bg-[#3D3C7F] text-white hover:bg-purple-700 md:h-10 md:w-10"
					/>
				</div>
			</Carousel.Root>
		</div>

		<div class="mt-8 space-x-2 text-center">
			{#if slideCount > 0}
				{#each { length: slideCount } as _, index (index)}
					<button
						type="button"
						aria-label="Go to slide {index + 1}"
						onclick={() => scrollTo(index)}
						class="h-2 w-2 rounded-full {activeIndex === index
							? 'scale-125 bg-purple-700'
							: 'bg-gray-300'} transition-all duration-200 ease-in-out"
					></button>
				{/each}
			{/if}
		</div>
	</div>
</section>

{#if currentSelectedJob}
	<JobsModal
		open={activeModal === 'jobDetail'}
		jobDetails={currentSelectedJob}
		onClose={handleCloseAllModals}
		onApplyNow={handleApplyNowClicked}
	/>
{/if}

{#if activeModal === 'application'}
	<GenericContact
		open={activeModal === 'application'}
		formType="jobApplication"
		jobTitle={roleApplyingFor}
		onSubmit={handleApiFormResponse}
		onClose={handleCloseAllModals}
	/>
{/if}

{#if successModalOpen}
	<SuccessModal
		text={successMessage}
		open={successModalOpen}
		onClose={() => {
			successModalOpen = false;
			handleCloseAllModals();
		}}
	/>
{/if}

<style>
	section {
		font-family: 'Montserrat', sans-serif;
	}
</style>
