import type { APIPagination, Pagination } from '@lib/shared/types';

type SyncOrAsyncFunction<T> = () => T | Promise<T>;
function isPromise<T>(value: T | Promise<T>): value is Promise<T> {
	return typeof (value as Promise<T>).then === 'function';
}
export async function handleSyncOrAsyncFunction<T>(fn: SyncOrAsyncFunction<T>): Promise<T> {
	const result = fn();
	if (isPromise(result)) {
		return await result;
	} else {
		return result;
	}
}

// parse zod error and return an object with key as field name and value as a list
// of error messages

export function parseZodError(error: any) {
	// return empty object if error is not an instance of ZodError
	if (!error.errors) {
		return {};
	}

	const errors: Record<string, string[]> = {};
	error.errors.forEach((err: any) => {
		const field = err.path.join('.');
		if (!errors[field]) {
			errors[field] = [];
		}
		errors[field].push(err.message);
	});
	return errors;
}

function getCurrentPageWithNext(url: string) {
	if (!url) return '';
	const parsedUrl = new URL(url);
	const offset = parseInt(parsedUrl.searchParams.get('offset') || '0');
	const limit = parseInt(parsedUrl.searchParams.get('limit') || '10');

	return Math.ceil(offset / limit);
}

function getCurrentPageWithPrevious(url: string) {
	if (!url) return '';
	const parsedUrl = new URL(url);
	const offset = parseInt(parsedUrl.searchParams.get('offset') || '0');
	const limit = parseInt(parsedUrl.searchParams.get('limit') || '10');

	return Math.floor(offset / limit) + 2;
}

export const convertApiPaginationToPagination = <T>(data: APIPagination<T>): Pagination<T> => {
	const itemsPerpage = 10;

	return {
		items: data.results,
		total_count: data.count,
		current_page: data?.next
			? getCurrentPageWithNext(data.next)
			: getCurrentPageWithPrevious(data?.previous),
		total_pages: Math.ceil(data.count / itemsPerpage),
		has_next: !!data.next,
		has_previous: !!data.previous,
		next_page_number: data.next ? parseInt(data.next.split('=')[1]) : null,
		previous_page_number: data.previous ? parseInt(data.previous.split('=')[1]) : null,
		items_per_page: itemsPerpage,
		next_page_url: data.next,
		previous_page_url: data.previous,
		type: 'API'
	};
};

export function autoResize(node: HTMLTextAreaElement) {
	const initialMinHeightStyle = window.getComputedStyle(node).minHeight;
	const initialMinHeight = parseFloat(initialMinHeightStyle);

	function updateHeight() {
		node.style.height = 'auto'; // Reset height to allow it to shrink
		// Calculate new height, ensuring it's not less than the initial min-height
		const newHeight = Math.max(initialMinHeight, node.scrollHeight);
		node.style.height = `${newHeight}px`;
	}

	// Initial call after the browser has had a chance to paint and compute styles
	requestAnimationFrame(() => {
		updateHeight();
	});

	node.addEventListener('input', updateHeight);

	// Optional: If the textarea's width can change (e.g., due to window resize),
	// which would affect text wrapping and thus scrollHeight.
	// const observer = new ResizeObserver(updateHeight);
	// observer.observe(node);

	return {
		destroy() {
			node.removeEventListener('input', updateHeight);
			// observer.unobserve(node);
		}
	};
}
