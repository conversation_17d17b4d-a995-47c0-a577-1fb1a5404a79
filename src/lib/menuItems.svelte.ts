import { writable } from 'svelte/store';
import type { ExternalLinksConfig } from '../app';
import { env } from '$env/dynamic/public';

export interface MenuItem {
	label: string;
	href?: string;
	children?: MenuItem[];
	isButton?: boolean; // Optional flag for special styling like "Join Us Now"
	routeId: string;
}

export const menuItems = writable([
	{ label: 'Home', href: '/', routeId: '' },
	{
		label: 'Division',
		children: [
			{
				routeId: 'health',
				label: 'Medical/HealthCare',
				href: '/division/health',
				children: [
					{ label: 'Nurses', href: '/division/nurses', routeId: 'nurses' },
					{ label: 'HealthCare Assistant', href: '/division/hca', routeId: 'hca' },
					{ label: 'Support Workers', href: '/division/locums', routeId: 'locums' }
				]
			},
			{ label: 'Commercial', href: '/division/commercial', routeId: 'commercial' },
			{ label: 'Industrial', href: '/division/industrial', routeId: 'industrial' },
			{ label: 'Managed Services', href: '/division/managed-services', routeId: 'managed-services' }
		],
		routeId: 'division'
	},
	{
		label: 'Domiciliary Care',
		href: `http://domiciliary.${env.PUBLIC_URL}/`,
		routeId: 'domiciliary'
	},
	{
		label: 'Supported Living',
		href: `http://supported.${env.PUBLIC_URL}/`,
		routeId: 'supported-living'
	},

	{
		label: 'Services',
		href: '/services',

		routeId: 'services'
	},
	{
		label: 'Jobs',
		href: '/ongoing-jobs',
		// children: [
		// 	{ label: 'Ongoing Jobs', href: '/ongoing-jobs', routeId: 'ongoing-jobs' },
		// 	{ label: 'Candidates', href: '/candidates', routeId: 'candidates' }
		// ],
		routeId: 'ongoing-jobs'
	},

	{ label: 'Contact Us', href: '/contact', routeId: 'contact' }
]);

// src/lib/stores/menuStore.ts (or adjust path)

// Store holds the routeId of the currently open top-level menu, or null if none
export const openMenuId = writable<string | null>(null);

// Helper function to toggle a menu, closing others
export function toggleMenu(id: string | null) {
	openMenuId.update((currentId) => {
		// If clicking the already open menu, close it. Otherwise, open the new one.
		return currentId === id ? null : id;
	});
}
