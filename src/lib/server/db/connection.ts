import mongoose from 'mongoose';
import { env } from '$env/dynamic/private';
import { seedExternalLinks } from '../db.seed';
// import { seedDatabase } from '../db.seed';

let _isDbConnected = false;

export async function connectToDatabase() {
	if (_isDbConnected) {
		console.log('Already connected to database.');
		return;
	}
	try {
		await mongoose.connect(env.MONGODB_URI);
		_isDbConnected = true;
		// console.log('Database is connected');
		// console.log('seeding');
		// await seedDatabase(1000, 0.05);
		// seedExternalLinks();

		console.log('seeding done');
	} catch (error) {
		console.error('Database connection error:', error);
		process.exit(1);
	}
}
