import mongoose, { Schema } from 'mongoose';

export interface IOtp extends Document {
	code: string;
	email: string;
}

const OtpSchema: Schema = new Schema(
	{
		code: { type: String, required: true },
		userid: { type: Schema.Types.ObjectId, ref: 'User', required: true }
	},
	{ timestamps: true }
);

OtpSchema.index({ createdAt: 1 }, { expireAfterSeconds: 900 });
OtpSchema.index({ userid: 1 });

const OTP = mongoose.model<IOtp>('Otp', OtpSchema);

export { OTP, OtpSchema };
