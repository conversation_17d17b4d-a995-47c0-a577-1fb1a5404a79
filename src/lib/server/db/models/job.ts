import mongoose from 'mongoose';

export type JobType = 'on-site' | 'remote' | 'hybrid';

export type JobField = 'medical' | 'care' | 'industrial' | 'unspecified';

export interface IJob extends mongoose.Document {
	_id: mongoose.Types.ObjectId;
	title: string;
	companyName: string;
	companyDesc?: string;
	jobType: JobType;
	jobDesc: string;
	jobField: JobField;
	jobLocation: string;
	skills: string;
	salary: string;
	deadline: string;
	homepage?: boolean;
	createdAt: Date;
	updatedAt: Date;
}

const JobSchema = new mongoose.Schema<IJob>(
	{
		title: {
			type: String,
			required: [true, 'Please provide a job title'],
			minLength: [3, 'Title must be at least 3 characters.'],
			maxLength: [100, 'Title is too large'],
			lowercase: true,
			trim: true
		},
		companyName: {
			type: String,
			required: [true, 'Please provide a company name']
		},
		companyDesc: {
			type: String
		},
		jobType: {
			type: String,
			required: [true, 'Please provide a job type'],
			enum: {
				values: ['on-site', 'remote', 'hybrid'] as JobType[],
				message: 'Job type can not be {VALUE}, must be on-site/remote/hybrid'
			}
		},
		jobDesc: {
			type: String,
			required: [true, 'Please provide a job description']
		},
		jobField: {
			type: String,
			required: [true, 'Please provide a job field'],
			enum: {
				values: ['medical', 'care', 'industrial', 'unspecified'] as JobField[],
				message: 'Job field can not be {VALUE}. Must be medical/care/industrial/unspecified'
			}
		},
		jobLocation: {
			type: String,
			required: [true, 'Please provide a job location'],
			lowercase: true
		},
		skills: {
			type: String,
			required: [true, 'Please provide skills needed for this job']
		},
		salary: {
			type: String,
			required: [true, 'Please provide salary']
		},
		deadline: {
			type: String,
			required: [true, 'Please provide application deadline']
		},
		homepage: {
			type: Boolean,
			default: false
		}
	},
	{ timestamps: true }
);

export const Job = mongoose.model<IJob>('Job', JobSchema);

export type JobInput = Omit<IJob, '_id' | 'createdAt' | 'updatedAt' | keyof mongoose.Document> & {
	homepage?: boolean;
};
