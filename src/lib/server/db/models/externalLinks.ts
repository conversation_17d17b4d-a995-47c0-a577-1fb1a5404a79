import mongoose, { Schema, Document } from 'mongoose';

export interface IExternalLink extends Document {
	name: string;
	url: string;
	createdAt: Date;
	updatedAt: Date;
}

const ExternalLinkSchema: Schema = new Schema(
	{
		name: { type: String, required: true, unique: true, trim: true },
		url: { type: String, required: true, trim: true }
	},
	{ timestamps: true }
);

export const ExternalLink =
	mongoose.models.ExternalLink || mongoose.model<IExternalLink>('ExternalLink', ExternalLinkSchema);

export type ExternalLinkUpdateInput = {
	url: string;
};
