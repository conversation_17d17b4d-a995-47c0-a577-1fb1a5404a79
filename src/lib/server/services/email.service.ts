// src/lib/server/services/email.service.js
import { env } from '$env/dynamic/private';
import { Resend } from 'resend';

const resend = new Resend(env.RESEND_API_KEY);

/**
 * Sends an OTP email to the manager.
 * @param {string} toEmail - The recipient's email address.
 * @param {string} otp - The One-Time Password.
 * @returns {Promise<{success: boolean, messageId?: string, error?: any}>}
 */
export async function sendLoginOtpEmail(toEmail, otp) {
	try {
		const { data, error } = await resend.emails.send({
			from: `Your Job Portal <${env.FROM_EMAIL}>`,
			to: [toEmail],
			subject: 'Your Job Portal Login OTP',
			html: `
                <div style="font-family: sans-serif; text-align: center; padding: 20px;">
                    <h2>Job Portal Login</h2>
                    <p>Your One-Time Password is:</p>
                    <p style="font-size: 24px; font-weight: bold; letter-spacing: 2px; margin: 20px 0; padding: 10px; background-color: #f0f0f0; border-radius: 5px;">
                        ${otp}
                    </p>
                    <p>This OTP is valid for 15 minutes. If you did not request this, please ignore this email.</p>
                </div>
            `
		});

		if (error) {
			console.error('Resend sendLoginOtpEmail error:', error);
			return { success: false, error };
		}
		// console.log('Login OTP Email sent successfully:', data?.id);
		return { success: true, messageId: data?.id };
	} catch (err) {
		console.error('Error in sendLoginOtpEmail:', err);
		return { success: false, error: err };
	}
}

/**
 * Forwards an application form to the manager's email.
 * @param {string} managerEmail - The hiring manager's email address.
 * @param {object} applicationDetails - Details from the application form.
 * @param {{filename: string, content: Buffer, contentType: string} | null} pdfAttachment - The PDF attachment details or null.
 * @returns {Promise<{success: boolean, messageId?: string, error?: any}>}
 */
export async function forwardApplicationToManager(managerEmail, applicationDetails, pdfAttachment) {
	const {
		candidateName = 'N/A',
		candidateEmail = 'N/A',
		jobTitle = 'N/A', // You should get this from the job details
		coverLetter = 'No cover letter provided.'
		// Add any other fields you want in the email body
	} = applicationDetails;

	const attachments = [];
	if (pdfAttachment && pdfAttachment.content) {
		attachments.push({
			filename: pdfAttachment.filename,
			content: pdfAttachment.content // Resend expects content as a Buffer or base64 string
		});
	}

	try {
		const { data, error } = await resend.emails.send({
			from: `Job Portal Applications <${env.FROM_EMAIL}>`,
			to: [managerEmail],
			subject: `New Application for ${jobTitle}: ${candidateName}`,
			html: `
                <div style="font-family: sans-serif; padding: 15px; border: 1px solid #ddd;">
                    <h2>New Job Application Received</h2>
                    <p><strong>Job Title:</strong> ${jobTitle}</p>
                    <p><strong>Candidate Name:</strong> ${candidateName}</p>
                    <p><strong>Candidate Email:</strong> <a href="mailto:${candidateEmail}">${candidateEmail}</a></p>
                    <hr>
                    <h3>Cover Letter/Message:</h3>
                    <div style="white-space: pre-wrap; background-color: #f9f9f9; padding: 10px; border-radius: 4px;">${coverLetter}</div>
                    <hr>
                    ${attachments.length > 0 ? "<p>The candidate's resume/CV is attached.</p>" : '<p>No resume/CV was attached.</p>'}
                    <p><em>Please review this application in the admin panel or reply to the candidate directly.</em></p>
                </div>
            `,
			attachments: attachments
		});

		if (error) {
			console.error('Resend forwardApplicationToManager error:', error);
			return { success: false, error };
		}
		console.log('Application forwarded to manager successfully:', data?.id);
		return { success: true, messageId: data?.id };
	} catch (err) {
		console.error('Error in forwardApplicationToManager:', err);
		return { success: false, error: err };
	}
}
