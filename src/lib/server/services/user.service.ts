import mongoose from 'mongoose';
import { CentralUser } from '../db';
import { OTPService } from './otp.service';
import { sendLoginOtpEmail } from './email.service';

export interface ICentralUser extends mongoose.Document {
	_id: mongoose.Types.ObjectId;
	email: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface UserInput {
	email: string;
}

export class UserService {
	/**
	 * Creates a new user (equivalent to signUpService).
	 * Handles potential errors, like duplicate emails.
	 * @param userData - The data for the new user (email).
	 * @returns The newly created user document.
	 * @throws Mongoose validation errors or other database errors.
	 */
	async createUser(userData: UserInput): Promise<ICentralUser> {
		try {
			const newUser = await CentralUser.create(userData);
			return newUser;
		} catch (error: any) {
			// Log the error for debugging
			console.error('Error creating user:', error.message);

			// Handle specific known errors, e.g., duplicate key (email)
			if (error.code === 11000 && error.keyPattern?.email) {
				throw new Error(`Email already exists: ${userData.email}`);
			}
			// Re-throw other errors
			throw error;
		}
	}

	/**
	 * Finds a user by their email address.
	 * @param email - The email address to search for.
	 * @returns The user document if found, otherwise null.
	 */
	async findUserByEmail(email: string): Promise<ICentralUser | null> {
		try {
			if (!email) {
				// Basic validation
				return null;
			}
			// Find one user matching the email (case-insensitive due to lowercase: true in schema)
			const user = await CentralUser.findOne({ email: email.toLowerCase().trim() }).exec();
			return user;
		} catch (error) {
			console.error(`Error finding user by email ${email}:`, error);
			// Re-throw error for higher-level handling if needed
			throw error;
		}
	}

	/**
	 * Finds a user by their MongoDB ObjectId.
	 * @param id - The user's ID.
	 * @returns The user document if found, otherwise null.
	 */
	async findUserById(id: string): Promise<ICentralUser | null> {
		if (!mongoose.Types.ObjectId.isValid(id)) {
			console.warn(`Invalid user ID format: ${id}`);
			return null;
		}
		try {
			return await CentralUser.findById(id).exec();
		} catch (error) {
			console.error(`Error finding user by ID ${id}:`, error);
			throw error;
		}
	}

	async requestOTP(email: string) {
		const user = await this.findUserByEmail(email);
		if (user) {
			const otpService = new OTPService();
			const [otp, signature, _] = await otpService.getAndStoreSignatureForEmail(
				user._id.toString()
			);
			if (otp) {
				console.log('OTP', otp);
				await sendLoginOtpEmail(user.email, otp).catch((err) => {
					console.log('Error sending OTP', err);
					console.log('OTP', err?.error?.details);
				});
			}
			return true;
		}
		return false;
	}

	async verifyOTP(email: string, otp: string) {
		const user = await this.findUserByEmail(email);
		if (!user) {
			return false;
		}

		const otpService = new OTPService();
		const isVerified = await otpService.verifyAndDeleteSignatureForEmail(user._id.toString(), otp);
		return { user, isVerified };
	}
}

// Export a singleton instance of the service
export default new UserService();
