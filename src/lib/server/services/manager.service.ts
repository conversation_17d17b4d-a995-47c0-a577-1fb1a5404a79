import { Job, type IJob, type JobInput } from '../db'; // Adjust the import path as needed
import mongoose, { type FilterQuery, type SortOrder } from 'mongoose';

// --- Helper Interfaces for Filtering/Pagination/Sorting ---

/**
 * Defines the possible query parameters for filtering jobs.
 * Add more fields from IJob as needed for filtering.
 * Using 'string' for potentially partial matches (like title, companyName, location).
 */
export interface JobQueryFilters {
	title?: string; // Partial match, case-insensitive
	companyName?: string; // Partial match, case-insensitive
	jobType?: IJob['jobType']; // Exact match using the enum type
	jobField?: IJob['jobField']; // Exact match using the enum type
	jobLocation?: string; // Partial match, case-insensitive
	homepage?: 'true' | 'false' | boolean;
	// Add other fields you want to filter by, e.g., skills (might need special handling if it's a comma-separated string)
}

/**
 * Defines options for pagination.
 */
export interface PaginationOptions {
	page?: number; // The page number (1-based)
	limit?: number; // Number of items per page
}

/**
 * Defines options for sorting.
 */
export interface SortOptions {
	sortBy?: keyof IJob | string; // Field to sort by (use string for flexibility if needed)
	sortOrder?: 'asc' | 'desc'; // Sort direction
}

/**
 * Structure of the response for paginated results.
 */
export interface PaginatedJobResult {
	jobs: IJob[];
	total: number;
	page: number;
	limit: number;
	pages: number; // Total number of pages
}

// --- Job Service Class ---

export class JobService {
	/**
	 * Creates a new job listing.
	 * @param jobData - The data for the new job.
	 * @returns The newly created job document.
	 */
	async createJob(jobData: JobInput): Promise<IJob> {
		try {
			const newJob = await Job.create(jobData);
			return newJob;
		} catch (error) {
			// Basic error logging, consider more robust error handling
			console.error('Error creating job:', error);
			// Re-throw or handle specific Mongoose validation errors if needed
			throw error;
		}
	}

	/**
	 * Retrieves a single job by its ID.
	 * @param id - The MongoDB ObjectId (_id) of the job.
	 * @returns The job document or null if not found or ID is invalid.
	 */
	async getJobById(id: string): Promise<IJob | null> {
		if (!mongoose.Types.ObjectId.isValid(id)) {
			console.warn(`Invalid job ID format: ${id}`);
			return null; // Invalid ID format
		}
		try {
			return await Job.findById(id).exec();
		} catch (error) {
			console.error(`Error fetching job with ID ${id}:`, error);
			throw error; // Re-throw error
		}
	}

	/**
	 * Retrieves a list of jobs with filtering, sorting, and pagination.
	 * @param filters - An object containing filter criteria.
	 * @param pagination - An object containing page and limit for pagination.
	 * @param sort - An object containing sortBy and sortOrder for sorting.
	 * @returns An object containing the list of jobs and pagination details.
	 */
	async getJobs(
		filters: JobQueryFilters = {},
		pagination: PaginationOptions = { page: 1, limit: 10 },
		sort: SortOptions = { sortBy: 'createdAt', sortOrder: 'desc' }
	): Promise<PaginatedJobResult> {
		const query: FilterQuery<IJob> = {};
		// --- Build Filter Query ---
		if (filters.title) {
			// Case-insensitive partial match using regex
			query.title = { $regex: filters.title, $options: 'i' };
		}
		if (filters.companyName) {
			query.companyName = { $regex: filters.companyName, $options: 'i' };
		}
		if (filters.jobType) {
			query.jobType = filters.jobType; // Exact match
		}
		if (filters.jobField) {
			query.jobField = filters.jobField; // Exact match
		}
		if (filters.jobLocation) {
			query.jobLocation = { $regex: filters.jobLocation, $options: 'i' };
		}

		if (filters.homepage && filters.homepage === 'true') {
			query.homepage = true;
		}
		// Add more filters here based on JobQueryFilters interface

		// --- Pagination ---
		const page = Math.max(1, pagination.page ?? 1); // Ensure page is at least 1
		const limit = Math.max(1, pagination.limit ?? 10); // Ensure limit is at least 1
		const skip = (page - 1) * limit;

		// --- Sorting ---
		const sortOptions: { [key: string]: SortOrder } = {};
		const sortBy = sort.sortBy ?? 'createdAt'; // Default sort field
		const sortOrder = sort.sortOrder === 'asc' ? 1 : -1; // Default desc
		sortOptions[sortBy] = sortOrder;

		try {
			// Get total count matching the filters (before pagination)
			const total = await Job.countDocuments(query);

			// Get the paginated and sorted jobs
			const jobs = await Job.find(query).sort(sortOptions).skip(skip).limit(limit).lean().exec();

			// Calculate total pages
			const pages = Math.ceil(total / limit);

			return {
				jobs,
				total,
				page,
				limit,
				pages
			};
		} catch (error) {
			console.error('Error fetching jobs:', error);
			throw error;
		}
	}

	/**
	 * Updates an existing job by its ID.
	 * @param id - The ID of the job to update.
	 * @param updateData - An object containing the fields to update. Use Partial<JobInput> for flexibility.
	 * @returns The updated job document or null if not found or ID is invalid.
	 */
	async updateJob(id: string, updateData: Partial<JobInput>): Promise<IJob | null> {
		if (!mongoose.Types.ObjectId.isValid(id)) {
			console.warn(`Invalid job ID format for update: ${id}`);
			return null;
		}

		// Optional: Prevent updating certain fields if necessary
		// delete updateData.createdAt; // Example

		try {
			return await Job.findByIdAndUpdate(id, updateData, {
				new: true, // Return the updated document
				runValidators: true // Ensure schema validation runs on update
			}).exec();
		} catch (error) {
			console.error(`Error updating job with ID ${id}:`, error);
			// Handle specific errors like validation errors if needed
			throw error;
		}
	}

	/**
	 * Deletes a job by its ID.
	 * @param id - The ID of the job to delete.
	 * @returns The deleted job document or null if not found or ID is invalid.
	 */
	async deleteJob(id: string): Promise<IJob | null> {
		if (!mongoose.Types.ObjectId.isValid(id)) {
			console.warn(`Invalid job ID format for delete: ${id}`);
			return null;
		}
		try {
			return await Job.findByIdAndDelete(id).exec();
		} catch (error) {
			console.error(`Error deleting job with ID ${id}:`, error);
			throw error;
		}
	}
}

// Export a singleton instance of the service
export default new JobService();
