// import mongoose from 'mongoose';
// import { connectToDatabase, Job, type JobField, type JobInput } from '$lib/server/db';

// // --- Mock Data Arrays ---
// const jobTitlesByField = {
// 	medical: [
// 		'Healthcare Assistant',
// 		'Support Worker (Medical)',
// 		'Clinical Support Worker',
// 		'Medical Administrator',
// 		'Pharmacy Assistant',
// 		'Phlebotomist',
// 		'Junior Nurse',
// 		'Trainee Paramedic',
// 		'Medical Receptionist',
// 		'Hospital Porter'
// 	],
// 	care: [
// 		'Care Assistant',
// 		'Support Worker',
// 		'Home Carer',
// 		'Residential Support Worker',
// 		'Senior Care Assistant',
// 		'Disability Support Worker',
// 		'Mental Health Support Worker',
// 		'Community Care Worker',
// 		'Personal Assistant (Care)',
// 		'Live-in Carer'
// 	],
// 	industrial: [
// 		'Warehouse Operative',
// 		'Production Operative',
// 		'Forklift Driver',
// 		'Assembly Worker',
// 		'Machine Operator',
// 		'Logistics Assistant',
// 		'Picker Packer',
// 		'Manufacturing Technician',
// 		'Quality Control Inspector',
// 		'General Labourer'
// 	],
// 	unspecified: [
// 		// More generic titles
// 		'Operations Assistant',
// 		'Team Member',
// 		'General Assistant',
// 		'Administrator',
// 		'Customer Service Advisor',
// 		'Entry Level Associate',
// 		'Project Support Officer',
// 		'Data Entry Clerk',
// 		'Site Assistant',
// 		'Junior Coordinator'
// 	]
// };

// const companyNames = [
// 	'UK Health Services Ltd',
// 	'CareDirect UK',
// 	'Britannia Industries PLC',
// 	'Sterling Solutions Group',
// 	'Nationwide Support Services',
// 	'MediCare Britain',
// 	'Community Link Care',
// 	'Apex Manufacturing UK',
// 	'General Resources Ltd',
// 	' Albion Personnel',
// 	'Thames Valley Medical',
// 	'Northern Care Providers',
// 	'Midlands Industrial Co.',
// 	'UK Business Systems',
// 	'BritCare Agency',
// 	'London Central Industrial',
// 	'South Coast Medical Services',
// 	'Everyday Care Solutions',
// 	'GB Manufacturing',
// 	'ConnectWell Jobs'
// ];

// const locations = [
// 	'London, UK',
// 	'Manchester, UK',
// 	'Birmingham, UK',
// 	'Leeds, UK',
// 	'Glasgow, UK',
// 	'Liverpool, UK',
// 	'Bristol, UK',
// 	'Sheffield, UK',
// 	'Edinburgh, UK',
// 	'Cardiff, UK',
// 	'Belfast, UK',
// 	'Reading, UK',
// 	'Southampton, UK',
// 	'Nottingham, UK',
// 	'Remote (UK based)'
// ];
// const jobTypes: JobInput['jobType'][] = ['on-site', 'remote', 'hybrid'];

// const jobFields: JobInput['jobField'][] = ['medical', 'care', 'industrial', 'unspecified'];

// const skillsPoolByField = {
// 	medical: [
// 		'Patient Care',
// 		'Basic Life Support',
// 		'Medical Terminology',
// 		'Infection Control',
// 		'Record Keeping',
// 		'Communication Skills',
// 		'Teamwork',
// 		'Attention to Detail',
// 		'Admin Skills',
// 		'NHS Systems'
// 	],
// 	care: [
// 		'Personal Care',
// 		'Empathy',
// 		'Communication Skills',
// 		'Patience',
// 		'Safeguarding Adults',
// 		'Moving and Handling',
// 		'Dementia Care',
// 		'Medication Prompting',
// 		'NVQ Level 2/3 Health & Social Care',
// 		'Record Keeping'
// 	],
// 	industrial: [
// 		'Manual Handling',
// 		'Health and Safety',
// 		'Forklift License',
// 		'Machine Operation',
// 		'Quality Checks',
// 		'Teamwork',
// 		'Time Management',
// 		'Warehouse Systems (WMS)',
// 		'Assembly',
// 		'Production Line Experience'
// 	],
// 	unspecified: [
// 		'Communication Skills',
// 		'Teamwork',
// 		'Microsoft Office Suite',
// 		'Customer Service',
// 		'Problem Solving',
// 		'Organisation',
// 		'Time Management',
// 		'Data Entry',
// 		'Adaptability',
// 		'Basic IT Skills'
// 	]
// };

// // --- Helper Functions for Mock Data ---
// const getRandomElement = <T>(arr: T[]): T => arr[Math.floor(Math.random() * arr.length)];

// const getRandomSkills = (field: JobField, num: number = 3): string => {
// 	const specificSkillsPool = skillsPoolByField[field] || skillsPoolByField.unspecified;
// 	const selectedSkills = new Set<string>();
// 	// Ensure we don't try to pick more skills than available
// 	const skillsToPick = Math.min(num, specificSkillsPool.length);
// 	while (selectedSkills.size < skillsToPick) {
// 		selectedSkills.add(getRandomElement(specificSkillsPool));
// 	}
// 	return Array.from(selectedSkills).join(', ');
// };

// const getRandomHourlySalaryGBP = (): string => {
// 	const rates = [
// 		'£10.50 - £11.50 per hour',
// 		'£11.00 - £12.50 per hour',
// 		'£12.00 - £14.00 per hour',
// 		'£10.00 per hour',
// 		'£11.75 per hour',
// 		'£13.20 per hour',
// 		'Competitive hourly rate',
// 		'£10.80 - £13.00 per hour (DOE)',
// 		'Up to £15.00 per hour',
// 		'£9.90 - £11.20 per hour (agency)'
// 	];
// 	return getRandomElement(rates);
// };

// const getRandomFutureDate = (): string => {
// 	const date = new Date();
// 	date.setDate(date.getDate() + Math.floor(Math.random() * 60) + 7); // 1 week to ~2 months in future
// 	return date.toISOString().split('T')[0]; // YYYY-MM-DD
// };

// // --- Generate a Single Mock Job ---
// const generateMockJob = (isHomepageJob: boolean = false): JobInput => {
// 	const jobField = getRandomElement(jobFields);
// 	const title = getRandomElement(jobTitlesByField[jobField]);
// 	const companyName = getRandomElement(companyNames);
// 	const skills = getRandomSkills(jobField, Math.floor(Math.random() * 3) + 3); // 3 to 5 skills

// 	return {
// 		title: title,
// 		companyName: companyName,
// 		companyDesc: `Join ${companyName}, a respected provider of ${jobField} services in the UK. We are committed to excellence and are looking for dedicated individuals. Our team values professionalism and a positive work ethic.`,
// 		jobType: getRandomElement(jobTypes),
// 		jobDesc: `We are currently recruiting for a ${title} to join our team at ${companyName}. Key responsibilities will include [Primary Task for ${jobField}], [Secondary Task for ${jobField}], and maintaining high standards of work. The successful candidate should possess skills in ${skills.split(', ')[0]} and ${skills.split(', ')[1]}. This role offers a ${getRandomHourlySalaryGBP()} and the opportunity to work in a supportive environment. Basic understanding of health and safety regulations is a plus.`,
// 		jobField: jobField,
// 		jobLocation: getRandomElement(locations),
// 		skills: skills,
// 		salary: getRandomHourlySalaryGBP(),
// 		deadline: getRandomFutureDate(),
// 		homepage: isHomepageJob
// 	};
// };

// // --- Seeding Function (remains largely the same) ---
// export const seedDatabase = async (
// 	numberOfJobs: number = 1000,
// 	homepageJobPercentage: number = 0.05
// ) => {
// 	try {
// 		await connectToDatabase();

// 		const jobCount = await Job.countDocuments();
// 		if (jobCount > 0) {
// 			console.log('Database already contains jobs. Seeding skipped.');
// 			return;
// 		}

// 		console.log('No jobs found. Seeding database with mock British jobs...');

// 		const mockJobs: JobInput[] = [];
// 		const numHomepageJobs = Math.floor(numberOfJobs * homepageJobPercentage);

// 		for (let i = 0; i < numberOfJobs; i++) {
// 			const isHomepage = i < numHomepageJobs;
// 			mockJobs.push(generateMockJob(isHomepage));
// 		}

// 		await Job.insertMany(mockJobs);
// 		console.log(
// 			`Successfully seeded ${numberOfJobs} jobs (${numHomepageJobs} marked for homepage).`
// 		);
// 	} catch (error) {
// 		console.error('Error seeding database:', error);
// 	} finally {
// 		// Optional: If you run this script standalone, you might want to disconnect.
// 		// if (mongoose.connection.readyState === 1 && require.main === module) {
// 		//     await mongoose.disconnect();
// 		//     console.log('Disconnected from database after seeding script execution.');
// 		// }
// 	}
// };

// src/lib/server/db.seed.ts
import mongoose from 'mongoose';
import {
	connectToDatabase,
	Job,
	type JobField,
	type JobInput,
	ExternalLink,
	type IExternalLink
} from '$lib/server/db';

const predefinedExternalLinks: Array<Pick<IExternalLink, 'name' | 'url' | 'description'>> = [
	{
		name: 'privacyPolicy',
		description: 'Company Privacy Policy',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/Central-Staffing-Limited-Privacy-policy.pdf'
	},
	{
		name: 'termsOfUse',
		description: 'Website Terms of Use',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/Terms-of-use-final.pdf'
	},
	{
		name: 'cookiesPolicy',
		description: 'Website Cookies Policy',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/Cookies-Policy.pdf'
	},
	{
		name: 'modernSlaveryStatement',
		description: 'Modern Slavery Statement',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/modern-slavery-statment.pdf'
	},
	{
		name: 'applicationFormHCA',
		description: 'Application Form - Healthcare Assistant',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/Application-Form-Healthcare-Assistant.pdf'
	},
	{
		name: 'applicationFormNursesAHP',
		description: 'Application Form - Nurses/AHP',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/Application-Form-Nurses-AHP.pdf'
	},
	{
		name: 'applicationFormDoctorsGPs',
		description: 'Application Form - Doctors/GPs',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/Application-Form-Doctors-GPs.pdf'
	},
	{
		name: 'timesheetForm',
		description: 'Timesheet Download',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/Timesheet.pdf'
	},
	{
		name: 'requiredDocumentsInfo',
		description: 'Information on Required Documents',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/REQUIRED-DOCUMENTS.pdf'
	},
	{
		name: 'preHealthQuestionnaire',
		description: 'Pre-Health Questionnaire',
		url: 'https://secure.toolkitfiles.co.uk/clients/38245/sitedata/files/Pre-health-Questionnaire.pdf'
	}
];

export async function seedExternalLinks() {
	try {
		console.log('Checking for existing external links...');
		for (const linkData of predefinedExternalLinks) {
			const existingLink = await ExternalLink.findOne({ name: linkData.name });
			if (!existingLink) {
				await ExternalLink.create(linkData);
				console.log(`Seeded external link: ${linkData.name}`);
			} else {
				// Optionally update URL or description if they differ, or just log
				if (
					existingLink.url !== linkData.url ||
					existingLink.description !== linkData.description
				) {
					existingLink.url = linkData.url; // Ensure URL is up-to-date from seed if preferred
					existingLink.description = linkData.description;
					await existingLink.save();
					console.log(`Updated existing external link: ${linkData.name}`);
				} else {
					console.log(`External link "${linkData.name}" already exists and matches seed data.`);
				}
			}
		}
		console.log('External links seeding/update process complete.');
	} catch (error) {
		console.error('Error seeding/updating external links:', error);
	}
}

// export const seedDatabase = async (
// 	numberOfJobs: number = 1000, // Default value from your code
// 	homepageJobPercentage: number = 0.05 // Default value from your code
// ) => {
// 	try {
// 		await connectToDatabase();
// 		// ... (your existing job seeding logic) ...
//         // Example:
//         const jobCount = await Job.countDocuments();
// 		if (jobCount > 0) {
// 			console.log('Database already contains jobs. Job seeding skipped.');
// 		} else {
//             console.log('No jobs found. Seeding database with mock British jobs...');
//             // ... your job generation and insertion logic
//             console.log(`Successfully seeded ${numberOfJobs} jobs.`);
//         }

//         // Seed or update external links
//         await seedExternalLinks();

// 	} catch (error) {
// 		console.error('Error seeding database:', error);
// 	} finally {
// 		// Optional: mongoose.disconnect(); if run standalone
// 	}
// };
