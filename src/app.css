@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Mukta:wght@200;300;400;500;600;700;800&display=swap');

@import 'tailwindcss';

:root {
	--brand-purple: #5d28b6;
}

.spec {
	color: #cdb0ff;
}

.central-p {
	padding: clamp(0.5rem, 0.5rem + 5vw, 2rem) clamp(1rem, -4.3rem + 7.8vw, 7.5rem);

	@media screen and (min-width: 900px) {
		padding: 2rem clamp(1rem, -4.3rem + 7.8vw, 7.5rem);
	}
}

body {
	font-family: Montserrat;
}

.staffing-ul li {
	color: #000000;
	font-family: Montserrat;
	font-weight: 500;
	font-size: 14px;
	line-height: 100%;
	letter-spacing: 0%;
}

.parent-active > a .chevron,
.parent-active > button.trigger .chevron {
	color: var(--brand-purple); /* Match chevron color */
}

/* Styling for items within dropdown */
.dropdown > li.menu-item {
	padding-bottom: 0;
	position: relative;
}
.dropdown > li > a,
.dropdown > li > button.trigger,
.dropdown > li > span {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	padding: 0.6rem 1.2rem;
	white-space: nowrap;
	color: #374151;
	font-size: 0.95rem;
	font-weight: 400;
	cursor: pointer;
	box-sizing: border-box;
}
.dropdown > li > a::after,
.dropdown > li > button.trigger::after {
	display: none;
}
.dropdown > li > a:hover,
.dropdown > li > button.trigger:hover {
	background-color: #f3f4f6;
	color: #1f2937;
}
.dropdown > li.active > a,
.dropdown > li.active > button.trigger {
	font-weight: 600;
	color: var(--brand-purple);
}
.dropdown > li.parent-active > a,
.dropdown > li.parent-active > button.trigger {
	color: var(--brand-purple);
}

/* --- Level 2+ Dropdown Positioning --- */
.dropdown .dropdown {
	/* Target nested dropdowns */
	top: -0.5rem; /* Align top edge with parent padding */
	left: 100%;
	margin-left: 0.1rem; /* Small gap */
}

/* Chevron styling within dropdowns */
.dropdown .chevron {
	width: 0.8rem;
	height: 0.8rem;
	margin-left: 0.5rem;
	transform: rotate(-90deg); /* Point right */
	transition: transform 0.2s ease-out;
}
/* Rotate nested chevron when its submenu opens */
.dropdown li > button.trigger .chevron[style*='rotate(0deg)'] {
	/* Crude check, might need state class */
	transform: rotate(0deg);
}
