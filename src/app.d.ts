// src/app.d.ts
export interface ExternalLinksConfig {
	[key: string]: { url: string; description?: string } | undefined;
}
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			tenant: 'domiciliary' | 'supported' | 'main';
			user: {
				id: string;
				email: string;
			} | null; // User can be null if not logged in
			appConfig: {
				externalLinks: ExternalLinksConfig;
			};
		}
		// interface PageData {}
		// interface Platform {}
	}
}

export {};
