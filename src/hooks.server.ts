import type { Handle } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import * as jose from 'jose';
import { connectToDatabase, ExternalLink, type IExternalLink } from '$lib/server/db';

interface UserJWTPayload extends jose.JWTPayload {
	userId: string;
	email: string;
}

interface ExternalLinksConfig {
	[key: string]: { url: string; description?: string } | undefined;
}

interface AppLocalsUser {
	id: string;
	email: string;
}

// Function to determine tenant from hostname
const getTenant = (hostname: string): 'domiciliary' | 'supported' | 'main' => {
	const parts = hostname.split('.');
	// Assuming subdomains like 'domiciliary.centralstaffing.co.uk' or 'localhost'
	if (parts[0] === 'domiciliary') {
		return 'domiciliary';
	}
	if (parts[0] === 'supported') {
		return 'supported';
	}
	return 'main';
};

export const handle: Handle = async ({ event, resolve }) => {
	if (event.url.pathname.startsWith('/.well-known/appspecific/com.chrome.devtools')) {
		return new Response(null, { status: 204 });
	}

	// --- Tenant Identification ---
	const tenant = getTenant(event.url.hostname);
	event.locals.tenant = tenant;

	await connectToDatabase();
	const externalLinksConfig: ExternalLinksConfig = {};
	try {
		const linksFromDb: IExternalLink[] = await ExternalLink.find().lean();
		linksFromDb.forEach((link) => {
			externalLinksConfig[link.name] = { url: link.url, description: link.description };
		});
	} catch (error) {
		console.error('Error fetching external links for app config:', error);
	}
	event.locals.appConfig = { externalLinks: externalLinksConfig };

	const sessionToken = event.cookies.get('session_token');
	event.locals.user = null;

	if (sessionToken) {
		try {
			const secretKeyUint8Array = new TextEncoder().encode(env.JWT_SECRET_KEY);
			const { payload } = await jose.jwtVerify<UserJWTPayload>(sessionToken, secretKeyUint8Array);
			event.locals.user = {
				id: payload.userId,
				email: payload.email
			} as AppLocalsUser;
		} catch (error) {
			console.warn(`Session token verification failed:`, error);
			event.cookies.delete('session_token', { path: '/' });
			event.locals.user = null;
		}
	}

	// Pass control to the SvelteKit router
	const response = await resolve(event);
	return response;
};
