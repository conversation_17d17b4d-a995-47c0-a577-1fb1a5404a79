{"name": "central-staffing", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.28.0", "@lucide/svelte": "^0.513.0", "@sveltejs/adapter-vercel": "^5.7.2", "@sveltejs/kit": "^2.21.3", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@sveltelegos-blue/svelte-legos": "^0.5.1", "@tailwindcss/vite": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "bits-ui": "^2.5.0", "clsx": "^2.1.1", "embla-carousel-svelte": "^8.6.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.2", "globals": "^16.2.0", "jsdom": "^26.1.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "runed": "^0.28.0", "svelte": "^5.33.18", "svelte-check": "^4.2.1", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0", "vite": "^6.3.5", "vitest": "^3.2.3"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"jose": "^6.0.11", "mongoose": "^8.15.1", "resend": "^4.5.2", "svelte-datatables-net": "^2.0.4", "zod": "^3.25.57"}}